"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/memoize";
exports.ids = ["vendor-chunks/memoize"];
exports.modules = {

/***/ "(ssr)/./node_modules/memoize/distribution/index.js":
/*!****************************************************!*\
  !*** ./node_modules/memoize/distribution/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ memoize),\n/* harmony export */   memoizeClear: () => (/* binding */ memoizeClear),\n/* harmony export */   memoizeDecorator: () => (/* binding */ memoizeDecorator)\n/* harmony export */ });\n/* harmony import */ var mimic_function__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mimic-function */ \"(ssr)/./node_modules/mimic-function/index.js\");\n\nconst cacheStore = new WeakMap();\nconst cacheTimerStore = new WeakMap();\n/**\n[Memoize](https://en.wikipedia.org/wiki/Memoization) functions - An optimization used to speed up consecutive function calls by caching the result of calls with identical input.\n\n@param function_ - The function to be memoized.\n\n@example\n```\nimport memoize from 'memoize';\n\nlet index = 0;\nconst counter = () => ++index;\nconst memoized = memoize(counter);\n\nmemoized('foo');\n//=> 1\n\n// Cached as it's the same argument\nmemoized('foo');\n//=> 1\n\n// Not cached anymore as the arguments changed\nmemoized('bar');\n//=> 2\n\nmemoized('bar');\n//=> 2\n```\n*/\nfunction memoize(function_, { cacheKey, cache = new Map(), maxAge, } = {}) {\n    if (maxAge === 0) {\n        return function_;\n    }\n    if (typeof maxAge === 'number') {\n        const maxSetIntervalValue = 2_147_483_647;\n        if (maxAge > maxSetIntervalValue) {\n            throw new TypeError(`The \\`maxAge\\` option cannot exceed ${maxSetIntervalValue}.`);\n        }\n        if (maxAge < 0) {\n            throw new TypeError('The `maxAge` option should not be a negative number.');\n        }\n    }\n    const memoized = function (...arguments_) {\n        const key = cacheKey ? cacheKey(arguments_) : arguments_[0];\n        const cacheItem = cache.get(key);\n        if (cacheItem) {\n            return cacheItem.data;\n        }\n        const result = function_.apply(this, arguments_);\n        const computedMaxAge = typeof maxAge === 'function' ? maxAge(...arguments_) : maxAge;\n        cache.set(key, {\n            data: result,\n            maxAge: computedMaxAge ? Date.now() + computedMaxAge : Number.POSITIVE_INFINITY,\n        });\n        if (computedMaxAge && computedMaxAge > 0 && computedMaxAge !== Number.POSITIVE_INFINITY) {\n            const timer = setTimeout(() => {\n                cache.delete(key);\n            }, computedMaxAge);\n            timer.unref?.();\n            const timers = cacheTimerStore.get(function_) ?? new Set();\n            timers.add(timer);\n            cacheTimerStore.set(function_, timers);\n        }\n        return result;\n    };\n    (0,mimic_function__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(memoized, function_, {\n        ignoreNonConfigurable: true,\n    });\n    cacheStore.set(memoized, cache);\n    return memoized;\n}\n/**\n@returns A [decorator](https://github.com/tc39/proposal-decorators) to memoize class methods or static class methods.\n\n@example\n```\nimport {memoizeDecorator} from 'memoize';\n\nclass Example {\n    index = 0\n\n    @memoizeDecorator()\n    counter() {\n        return ++this.index;\n    }\n}\n\nclass ExampleWithOptions {\n    index = 0\n\n    @memoizeDecorator({maxAge: 1000})\n    counter() {\n        return ++this.index;\n    }\n}\n```\n*/\nfunction memoizeDecorator(options = {}) {\n    const instanceMap = new WeakMap();\n    return (target, propertyKey, descriptor) => {\n        const input = target[propertyKey]; // eslint-disable-line @typescript-eslint/no-unsafe-assignment\n        if (typeof input !== 'function') {\n            throw new TypeError('The decorated value must be a function');\n        }\n        delete descriptor.value;\n        delete descriptor.writable;\n        descriptor.get = function () {\n            if (!instanceMap.has(this)) {\n                const value = memoize(input, options);\n                instanceMap.set(this, value);\n                return value;\n            }\n            return instanceMap.get(this);\n        };\n    };\n}\n/**\nClear all cached data of a memoized function.\n\n@param function_ - The memoized function.\n*/\nfunction memoizeClear(function_) {\n    const cache = cacheStore.get(function_);\n    if (!cache) {\n        throw new TypeError('Can\\'t clear a function that was not memoized!');\n    }\n    if (typeof cache.clear !== 'function') {\n        throw new TypeError('The cache Map can\\'t be cleared!');\n    }\n    cache.clear();\n    for (const timer of cacheTimerStore.get(function_) ?? []) {\n        clearTimeout(timer);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/memoize/distribution/index.js\n");

/***/ })

};
;