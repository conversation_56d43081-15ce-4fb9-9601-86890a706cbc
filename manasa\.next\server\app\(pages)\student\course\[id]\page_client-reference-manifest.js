globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/(pages)/student/course/[id]/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/app/layout.jsx":{"*":{"id":"(ssr)/./src/app/layout.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.jsx":{"*":{"id":"(ssr)/./src/app/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/login/page.jsx":{"*":{"id":"(ssr)/./src/app/(pages)/login/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/signup/page.jsx":{"*":{"id":"(ssr)/./src/app/(pages)/signup/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/student/course/[id]/page.jsx":{"*":{"id":"(ssr)/./src/app/(pages)/student/course/[id]/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/instructor/dashboard/layout.jsx":{"*":{"id":"(ssr)/./src/app/(pages)/instructor/dashboard/layout.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/instructor/dashboard/not-found.jsx":{"*":{"id":"(ssr)/./src/app/(pages)/instructor/dashboard/not-found.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/instructor/dashboard/page.jsx":{"*":{"id":"(ssr)/./src/app/(pages)/instructor/dashboard/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/instructor/dashboard/new/page.jsx":{"*":{"id":"(ssr)/./src/app/(pages)/instructor/dashboard/new/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/instructor/dashboard/[id]/page.jsx":{"*":{"id":"(ssr)/./src/app/(pages)/instructor/dashboard/[id]/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/courses/page.jsx":{"*":{"id":"(ssr)/./src/app/(pages)/courses/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/instructor/dashboard/videos/page.jsx":{"*":{"id":"(ssr)/./src/app/(pages)/instructor/dashboard/videos/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/student/[id]/page.jsx":{"*":{"id":"(ssr)/./src/app/(pages)/student/[id]/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/instructor/[id]/page.jsx":{"*":{"id":"(ssr)/./src/app/(pages)/instructor/[id]/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/instructor/dashboard/[id]/add-lesson/page.jsx":{"*":{"id":"(ssr)/./src/app/(pages)/instructor/dashboard/[id]/add-lesson/page.jsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\route\\منصة\\رفع\\4\\manasa\\src\\app\\layout.jsx":{"id":"(app-pages-browser)/./src/app/layout.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\src\\app\\page.jsx":{"id":"(app-pages-browser)/./src/app/page.jsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\src\\app\\(pages)\\login\\page.jsx":{"id":"(app-pages-browser)/./src/app/(pages)/login/page.jsx","name":"*","chunks":[],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\src\\app\\(pages)\\signup\\page.jsx":{"id":"(app-pages-browser)/./src/app/(pages)/signup/page.jsx","name":"*","chunks":[],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\src\\app\\(pages)\\student\\course\\[id]\\page.jsx":{"id":"(app-pages-browser)/./src/app/(pages)/student/course/[id]/page.jsx","name":"*","chunks":["app/(pages)/student/course/[id]/page","static/chunks/app/(pages)/student/course/%5Bid%5D/page.js"],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\src\\app\\(pages)\\instructor\\dashboard\\layout.jsx":{"id":"(app-pages-browser)/./src/app/(pages)/instructor/dashboard/layout.jsx","name":"*","chunks":[],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\src\\app\\(pages)\\instructor\\dashboard\\not-found.jsx":{"id":"(app-pages-browser)/./src/app/(pages)/instructor/dashboard/not-found.jsx","name":"*","chunks":[],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\src\\app\\(pages)\\instructor\\dashboard\\page.jsx":{"id":"(app-pages-browser)/./src/app/(pages)/instructor/dashboard/page.jsx","name":"*","chunks":[],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\src\\app\\(pages)\\instructor\\dashboard\\new\\page.jsx":{"id":"(app-pages-browser)/./src/app/(pages)/instructor/dashboard/new/page.jsx","name":"*","chunks":[],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\src\\app\\(pages)\\instructor\\dashboard\\[id]\\page.jsx":{"id":"(app-pages-browser)/./src/app/(pages)/instructor/dashboard/[id]/page.jsx","name":"*","chunks":[],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\src\\app\\(pages)\\courses\\page.jsx":{"id":"(app-pages-browser)/./src/app/(pages)/courses/page.jsx","name":"*","chunks":[],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\src\\app\\(pages)\\instructor\\dashboard\\videos\\page.jsx":{"id":"(app-pages-browser)/./src/app/(pages)/instructor/dashboard/videos/page.jsx","name":"*","chunks":[],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\src\\app\\(pages)\\student\\[id]\\page.jsx":{"id":"(app-pages-browser)/./src/app/(pages)/student/[id]/page.jsx","name":"*","chunks":[],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\src\\app\\(pages)\\instructor\\[id]\\page.jsx":{"id":"(app-pages-browser)/./src/app/(pages)/instructor/[id]/page.jsx","name":"*","chunks":[],"async":false},"D:\\route\\منصة\\رفع\\4\\manasa\\src\\app\\(pages)\\instructor\\dashboard\\[id]\\add-lesson\\page.jsx":{"id":"(app-pages-browser)/./src/app/(pages)/instructor/dashboard/[id]/add-lesson/page.jsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\route\\منصة\\رفع\\4\\manasa\\src\\":[],"D:\\route\\منصة\\رفع\\4\\manasa\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"D:\\route\\منصة\\رفع\\4\\manasa\\src\\app\\page":[],"D:\\route\\منصة\\رفع\\4\\manasa\\src\\app\\(pages)\\student\\course\\[id]\\page":[{"inlined":false,"path":"static/css/app/(pages)/student/course/[id]/page.css"}]},"rscModuleMapping":{"(app-pages-browser)/./src/app/layout.jsx":{"*":{"id":"(rsc)/./src/app/layout.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.jsx":{"*":{"id":"(rsc)/./src/app/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/login/page.jsx":{"*":{"id":"(rsc)/./src/app/(pages)/login/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/signup/page.jsx":{"*":{"id":"(rsc)/./src/app/(pages)/signup/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/student/course/[id]/page.jsx":{"*":{"id":"(rsc)/./src/app/(pages)/student/course/[id]/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/instructor/dashboard/layout.jsx":{"*":{"id":"(rsc)/./src/app/(pages)/instructor/dashboard/layout.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/instructor/dashboard/not-found.jsx":{"*":{"id":"(rsc)/./src/app/(pages)/instructor/dashboard/not-found.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/instructor/dashboard/page.jsx":{"*":{"id":"(rsc)/./src/app/(pages)/instructor/dashboard/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/instructor/dashboard/new/page.jsx":{"*":{"id":"(rsc)/./src/app/(pages)/instructor/dashboard/new/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/instructor/dashboard/[id]/page.jsx":{"*":{"id":"(rsc)/./src/app/(pages)/instructor/dashboard/[id]/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/courses/page.jsx":{"*":{"id":"(rsc)/./src/app/(pages)/courses/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/instructor/dashboard/videos/page.jsx":{"*":{"id":"(rsc)/./src/app/(pages)/instructor/dashboard/videos/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/student/[id]/page.jsx":{"*":{"id":"(rsc)/./src/app/(pages)/student/[id]/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/instructor/[id]/page.jsx":{"*":{"id":"(rsc)/./src/app/(pages)/instructor/[id]/page.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(pages)/instructor/dashboard/[id]/add-lesson/page.jsx":{"*":{"id":"(rsc)/./src/app/(pages)/instructor/dashboard/[id]/add-lesson/page.jsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}