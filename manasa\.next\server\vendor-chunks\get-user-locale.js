"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/get-user-locale";
exports.ids = ["vendor-chunks/get-user-locale"];
exports.modules = {

/***/ "(ssr)/./node_modules/get-user-locale/dist/index.js":
/*!****************************************************!*\
  !*** ./node_modules/get-user-locale/dist/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getUserLocale: () => (/* binding */ getUserLocale),\n/* harmony export */   getUserLocales: () => (/* binding */ getUserLocales)\n/* harmony export */ });\n/* harmony import */ var memoize__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! memoize */ \"(ssr)/./node_modules/memoize/distribution/index.js\");\n\nfunction isString(el) {\n    return typeof el === 'string';\n}\nfunction isUnique(el, index, arr) {\n    return arr.indexOf(el) === index;\n}\nfunction isAllLowerCase(el) {\n    return el.toLowerCase() === el;\n}\nfunction fixCommas(el) {\n    return el.indexOf(',') === -1 ? el : el.split(',');\n}\nfunction normalizeLocale(locale) {\n    if (!locale) {\n        return locale;\n    }\n    if (locale === 'C' || locale === 'posix' || locale === 'POSIX') {\n        return 'en-US';\n    }\n    // If there's a dot (.) in the locale, it's likely in the format of \"en-US.UTF-8\", so we only take the first part\n    if (locale.indexOf('.') !== -1) {\n        var _a = locale.split('.')[0], actualLocale = _a === void 0 ? '' : _a;\n        return normalizeLocale(actualLocale);\n    }\n    // If there's an at sign (@) in the locale, it's likely in the format of \"en-US@posix\", so we only take the first part\n    if (locale.indexOf('@') !== -1) {\n        var _b = locale.split('@')[0], actualLocale = _b === void 0 ? '' : _b;\n        return normalizeLocale(actualLocale);\n    }\n    // If there's a dash (-) in the locale and it's not all lower case, it's already in the format of \"en-US\", so we return it\n    if (locale.indexOf('-') === -1 || !isAllLowerCase(locale)) {\n        return locale;\n    }\n    var _c = locale.split('-'), splitEl1 = _c[0], _d = _c[1], splitEl2 = _d === void 0 ? '' : _d;\n    return \"\".concat(splitEl1, \"-\").concat(splitEl2.toUpperCase());\n}\nfunction getUserLocalesInternal(_a) {\n    var _b = _a === void 0 ? {} : _a, _c = _b.useFallbackLocale, useFallbackLocale = _c === void 0 ? true : _c, _d = _b.fallbackLocale, fallbackLocale = _d === void 0 ? 'en-US' : _d;\n    var languageList = [];\n    if (typeof navigator !== 'undefined') {\n        var rawLanguages = navigator.languages || [];\n        var languages = [];\n        for (var _i = 0, rawLanguages_1 = rawLanguages; _i < rawLanguages_1.length; _i++) {\n            var rawLanguagesItem = rawLanguages_1[_i];\n            languages = languages.concat(fixCommas(rawLanguagesItem));\n        }\n        var rawLanguage = navigator.language;\n        var language = rawLanguage ? fixCommas(rawLanguage) : rawLanguage;\n        languageList = languageList.concat(languages, language);\n    }\n    if (useFallbackLocale) {\n        languageList.push(fallbackLocale);\n    }\n    return languageList.filter(isString).map(normalizeLocale).filter(isUnique);\n}\nvar getUserLocales = (0,memoize__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(getUserLocalesInternal, { cacheKey: JSON.stringify });\nfunction getUserLocaleInternal(options) {\n    return getUserLocales(options)[0] || null;\n}\nvar getUserLocale = (0,memoize__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(getUserLocaleInternal, { cacheKey: JSON.stringify });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getUserLocale);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/get-user-locale/dist/index.js\n");

/***/ })

};
;