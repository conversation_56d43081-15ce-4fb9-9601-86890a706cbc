{"version": "1.0", "examples": {"BatchDeleteImage": [{"input": {"imageIds": [{"imageTag": "precise"}], "repositoryName": "ubuntu"}, "output": {"failures": [], "imageIds": [{"imageDigest": "sha256:examplee6d1e504117a17000003d3753086354a38375961f2e665416ef4b1b2f", "imageTag": "precise"}]}, "comments": {}, "description": "This example deletes images with the tags precise and trusty in a repository called ubuntu in the default registry for an account.", "id": "batchdeleteimages-example-*************", "title": "To delete multiple images"}], "BatchGetImage": [{"input": {"imageIds": [{"imageTag": "precise"}], "repositoryName": "ubuntu"}, "output": {"failures": [], "images": [{"imageId": {"imageDigest": "sha256:example76bdff6d83a09ba2a818f0d00000063724a9ac3ba5019c56f74ebf42a", "imageTag": "precise"}, "imageManifest": "{\n \"schemaVersion\": 1,\n \"name\": \"ubuntu\",\n \"tag\": \"precise\",\n...", "registryId": "************", "repositoryName": "ubuntu"}]}, "comments": {"output": {"imageManifest": "In this example, the imageManifest in the output JSON has been truncated."}}, "description": "This example obtains information for an image with a specified image digest ID from the repository named ubuntu in the current account.", "id": "batchgetimage-example-*************", "title": "To obtain multiple images in a single request"}], "CreateRepository": [{"input": {"repositoryName": "project-a/nginx-web-app"}, "output": {"repository": {"registryId": "************", "repositoryArn": "arn:aws:ecr:us-west-2:************:repository/project-a/nginx-web-app", "repositoryName": "project-a/nginx-web-app"}}, "comments": {"output": {"imageManifest": "In this example, the imageManifest in the output JSON has been truncated."}}, "description": "This example creates a repository called nginx-web-app inside the project-a namespace in the default registry for an account.", "id": "createrepository-example-*************", "title": "To create a new repository"}], "DeleteRepository": [{"input": {"force": true, "repositoryName": "ubuntu"}, "output": {"repository": {"registryId": "************", "repositoryArn": "arn:aws:ecr:us-west-2:************:repository/ubuntu", "repositoryName": "ubuntu"}}, "comments": {"output": {"imageManifest": "In this example, the imageManifest in the output JSON has been truncated."}}, "description": "This example force deletes a repository named ubuntu in the default registry for an account. The force parameter is required if the repository contains images.", "id": "deleterepository-example-*************", "title": "To force delete a repository"}], "DeleteRepositoryPolicy": [{"input": {"repositoryName": "ubuntu"}, "output": {"policyText": "{ ... }", "registryId": "************", "repositoryName": "ubuntu"}, "comments": {}, "description": "This example deletes the policy associated with the repository named ubuntu in the current account.", "id": "deleterepositorypolicy-example-*************", "title": "To delete the policy associated with a repository"}], "DescribeRepositories": [{"input": {}, "output": {"repositories": [{"registryId": "************", "repositoryArn": "arn:aws:ecr:us-west-2:************:repository/ubuntu", "repositoryName": "ubuntu"}, {"registryId": "************", "repositoryArn": "arn:aws:ecr:us-west-2:************:repository/test", "repositoryName": "test"}]}, "comments": {"output": {}}, "description": "The following example obtains a list and description of all repositories in the default registry to which the current user has access.", "id": "describe-repositories-*************", "title": "To describe all repositories in the current account"}], "GetRepositoryPolicy": [{"input": {"repositoryName": "ubuntu"}, "output": {"policyText": "{\n  \"Version\" : \"2008-10-17\",\n  \"Statement\" : [ {\n    \"Sid\" : \"new statement\",\n    \"Effect\" : \"Allow\",\n    \"Principal\" : {\n     \"AWS\" : \"arn:aws:iam::************:role/CodeDeployDemo\"\n    },\n\"Action\" : [ \"ecr:GetDownloadUrlForLayer\", \"ecr:BatchGetImage\", \"ecr:BatchCheckLayerAvailability\" ]\n } ]\n}", "registryId": "************", "repositoryName": "ubuntu"}, "comments": {}, "description": "This example obtains the repository policy for the repository named ubuntu.", "id": "getrepositorypolicy-example-*************", "title": "To get the current policy for a repository"}], "ListImages": [{"input": {"repositoryName": "ubuntu"}, "output": {"imageIds": [{"imageDigest": "sha256:764f63476bdff6d83a09ba2a818f0d35757063724a9ac3ba5019c56f74ebf42a", "imageTag": "precise"}]}, "comments": {}, "description": "This example lists all of the images in the repository named ubuntu in the default registry in the current account. ", "id": "listimages-example-*************", "title": "To list all images in a repository"}]}}