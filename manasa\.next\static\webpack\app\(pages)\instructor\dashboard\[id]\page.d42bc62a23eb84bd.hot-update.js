"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(pages)/instructor/dashboard/[id]/page",{

/***/ "(app-pages-browser)/./src/app/_Components/pdf/QuizPDFGenerator.jsx":
/*!******************************************************!*\
  !*** ./src/app/_Components/pdf/QuizPDFGenerator.jsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateQuizPDF: () => (/* binding */ generateQuizPDF)\n/* harmony export */ });\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jspdf */ \"(app-pages-browser)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n/* harmony import */ var jspdf_autotable__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jspdf-autotable */ \"(app-pages-browser)/./node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.mjs\");\n/* harmony import */ var _fonts_Amiri_Regular_normal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../fonts/Amiri-Regular-normal */ \"(app-pages-browser)/./src/fonts/Amiri-Regular-normal.js\");\n// ✅ ملف: src/app/_Components/pdf/QuizPDFGenerator.jsx\n\n\n\nfunction toArabicNumber(number) {\n    const arabicDigits = [\n        \"٠\",\n        \"١\",\n        \"٢\",\n        \"٣\",\n        \"٤\",\n        \"٥\",\n        \"٦\",\n        \"٧\",\n        \"٨\",\n        \"٩\"\n    ];\n    return number.toString().replace(/\\d/g, (d)=>arabicDigits[d]);\n}\nconst toBase64 = (url)=>fetch(url).then((res)=>res.blob()).then((blob)=>new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.onloadend = ()=>resolve(reader.result);\n            reader.onerror = reject;\n            reader.readAsDataURL(blob);\n        }));\n// دالة لحساب أبعاد الصورة مع الحفاظ على النسبة الأصلية\nconst calculateImageDimensions = (originalWidth, originalHeight, maxWidth, maxHeight)=>{\n    const aspectRatio = originalWidth / originalHeight;\n    let newWidth = maxWidth;\n    let newHeight = maxWidth / aspectRatio;\n    // إذا كان الارتفاع أكبر من المسموح، نقلل العرض\n    if (newHeight > maxHeight) {\n        newHeight = maxHeight;\n        newWidth = maxHeight * aspectRatio;\n    }\n    return {\n        width: newWidth,\n        height: newHeight\n    };\n};\n// دالة لتحميل الصورة والحصول على أبعادها الأصلية\nconst loadImageWithDimensions = (url)=>{\n    return new Promise((resolve, reject)=>{\n        const img = new Image();\n        img.crossOrigin = \"anonymous\";\n        img.onload = ()=>{\n            const canvas = document.createElement('canvas');\n            const ctx = canvas.getContext('2d');\n            canvas.width = img.naturalWidth;\n            canvas.height = img.naturalHeight;\n            ctx.drawImage(img, 0, 0);\n            const base64 = canvas.toDataURL('image/jpeg', 0.9); // جودة عالية\n            resolve({\n                base64,\n                originalWidth: img.naturalWidth,\n                originalHeight: img.naturalHeight\n            });\n        };\n        img.onerror = reject;\n        img.src = url;\n    });\n};\nasync function generateQuizPDF(quiz, user) {\n    // تحسين إعدادات PDF - zakialkholy\n    const doc = new jspdf__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n        orientation: \"portrait\",\n        unit: \"mm\",\n        format: \"a4\",\n        compress: true // ضغط الملف لتقليل الحجم\n    });\n    doc.setFont(\"Amiri-Regular\", \"normal\");\n    doc.setFontSize(14);\n    const pageHeight = 297;\n    const pageWidth = 210;\n    const marginHorizontal = 15;\n    const contentMarginTop = 35; // زيادة المساحة العلوية - zakialkholy\n    const contentMarginBottom = 30; // زيادة المساحة السفلية - zakialkholy\n    const labels = [\n        \"أ\",\n        \"ب\",\n        \"ج\",\n        \"د\",\n        \"هـ\",\n        \"و\"\n    ];\n    let y = contentMarginTop;\n    const estimateQuestionHeight = (q)=>{\n        let height = 10;\n        // السؤال في سطر واحد\n        height += 10;\n        // الصورة تأخذ مساحة متغيرة حسب حجمها الأصلي (حد أقصى 85)\n        if (q.image_url) height += 85;\n        // الاختيارات تحت الصورة - حساب عدد الصفوف المطلوبة\n        const answersPerRow = 2; // عدد الاختيارات في كل صف\n        const answerRows = Math.ceil(q.answers.length / answersPerRow);\n        height += answerRows * 10;\n        return height + 15;\n    };\n    const renderHeader = ()=>{\n        var _quiz_questions;\n        const topY = 15;\n        // تحسين تصميم الهيدر - zakialkholy\n        doc.setFontSize(16);\n        doc.setFont(\"Amiri-Regular\", \"bold\");\n        doc.text(\"\".concat(quiz.title), pageWidth / 2, topY, {\n            align: \"center\"\n        });\n        doc.setFontSize(10);\n        doc.setFont(\"Amiri-Regular\", \"normal\");\n        doc.text(\"اسم المعلم: \".concat(user.first_name || user.username), marginHorizontal + 35, topY + 8, {\n            align: \"right\"\n        });\n        doc.text(\"رقم الهاتف: \".concat(user.phone_number || \"غير محدد\"), pageWidth - marginHorizontal - 35, topY + 8, {\n            align: \"left\"\n        });\n        // إضافة معلومات إضافية - zakialkholy\n        doc.setFontSize(9);\n        doc.text(\"نوع التقييم: \".concat(quiz.quiz_type === \"exam\" ? \"امتحان\" : \"واجب\"), marginHorizontal, topY + 16, {\n            align: \"right\"\n        });\n        doc.text(\"الدرجة النهائية: \".concat(quiz.max_score), pageWidth / 2, topY + 16, {\n            align: \"center\"\n        });\n        doc.text(\"عدد الأسئلة: \".concat(((_quiz_questions = quiz.questions) === null || _quiz_questions === void 0 ? void 0 : _quiz_questions.length) || 0), pageWidth - marginHorizontal, topY + 16, {\n            align: \"left\"\n        });\n        // خط فاصل محسن - zakialkholy\n        doc.setLineWidth(0.5);\n        doc.line(marginHorizontal, topY + 20, pageWidth - marginHorizontal, topY + 20);\n        doc.setLineWidth(0.2);\n        doc.setFontSize(14);\n        doc.setFont(\"Amiri-Regular\", \"normal\");\n    };\n    const renderFooter = ()=>{\n        const footerY = pageHeight - 10;\n        // ✅ خط فوق الفوتر\n        doc.line(marginHorizontal, footerY - 6, pageWidth - marginHorizontal, footerY - 6);\n        doc.setFontSize(10);\n        doc.text(\"اسم المعلم: \".concat(user.first_name), marginHorizontal + 35, footerY, {\n            align: \"right\"\n        });\n        doc.text(\"\".concat(quiz.title), pageWidth / 2, footerY, {\n            align: \"center\"\n        });\n        doc.text(\"رقم الهاتف: \".concat(user.phone_number), pageWidth - marginHorizontal - 35, footerY, {\n            align: \"left\"\n        });\n        doc.setFontSize(14);\n    };\n    renderHeader();\n    for(let i = 0; i < quiz.questions.length; i++){\n        const q = quiz.questions[i];\n        const qNumber = toArabicNumber(i + 1);\n        const estimatedHeight = estimateQuestionHeight(q);\n        if (y + estimatedHeight > pageHeight - contentMarginBottom) {\n            renderFooter();\n            doc.addPage();\n            renderHeader();\n            y = contentMarginTop;\n        }\n        const boxTopY = y;\n        let contentY = y + 5;\n        // السؤال في سطر واحد\n        const qText = \"\".concat(qNumber, \"- \").concat(q.text);\n        doc.text(qText, pageWidth - marginHorizontal - 5, contentY, {\n            align: \"right\"\n        });\n        contentY += 10;\n        let maxContentY = contentY;\n        // الصورة بجودة عالية مع الحفاظ على النسبة الأصلية\n        if (q.image_url) {\n            try {\n                const imageData = await loadImageWithDimensions(q.image_url);\n                // الحد الأقصى للعرض والارتفاع\n                const maxWidth = pageWidth - 2 * marginHorizontal - 10;\n                const maxHeight = 80; // ارتفاع أقصى معقول\n                // حساب الأبعاد مع الحفاظ على النسبة الأصلية\n                const { width: imgWidth, height: imgHeight } = calculateImageDimensions(imageData.originalWidth, imageData.originalHeight, maxWidth, maxHeight);\n                // توسيط الصورة أفقياً\n                const imgX = marginHorizontal + (maxWidth - imgWidth) / 2;\n                doc.addImage(imageData.base64, \"JPEG\", imgX, contentY, imgWidth, imgHeight);\n                contentY += imgHeight + 5; // مسافة بعد الصورة\n                maxContentY = contentY;\n            } catch (err) {\n                console.warn(\"⚠️ فشل تحميل الصورة:\", err);\n                // في حالة فشل التحميل، نستخدم الطريقة القديمة\n                try {\n                    const base64 = await toBase64(q.image_url);\n                    const imgWidth = 100; // حجم معقول\n                    const imgHeight = 60;\n                    const imgX = marginHorizontal + 5;\n                    doc.addImage(base64, \"JPEG\", imgX, contentY, imgWidth, imgHeight);\n                    contentY += imgHeight + 5;\n                    maxContentY = contentY;\n                } catch (fallbackErr) {\n                    console.warn(\"⚠️ فشل تحميل الصورة نهائياً:\", fallbackErr);\n                }\n            }\n        }\n        // الاختيارات تحت الصورة وجنب بعض\n        let answerY = contentY;\n        const answersPerRow = 2; // عدد الاختيارات في كل صف\n        const answerWidth = (pageWidth - 2 * marginHorizontal - 20) / answersPerRow; // عرض كل اختيار\n        for(let j = 0; j < q.answers.length; j += answersPerRow){\n            // الاختيار الأول (على اليمين)\n            if (q.answers[j]) {\n                const a1 = \"\".concat(labels[j] || toArabicNumber(j + 1), \"- \").concat(q.answers[j].text);\n                const a1X = pageWidth - marginHorizontal - 5;\n                doc.text(a1, a1X, answerY, {\n                    align: \"right\"\n                });\n            }\n            // الاختيار الثاني (على اليسار)\n            if (q.answers[j + 1]) {\n                const a2 = \"\".concat(labels[j + 1] || toArabicNumber(j + 2), \"- \").concat(q.answers[j + 1].text);\n                const a2X = pageWidth - marginHorizontal - answerWidth;\n                doc.text(a2, a2X, answerY, {\n                    align: \"right\"\n                });\n            }\n            answerY += 10;\n        }\n        const bottomY = Math.max(answerY, maxContentY);\n        const boxHeight = bottomY - boxTopY + 5;\n        doc.rect(marginHorizontal, boxTopY, pageWidth - 2 * marginHorizontal, boxHeight);\n        y = bottomY + 10;\n    }\n    renderFooter();\n    doc.save(\"\".concat(quiz.title, \".pdf\"));\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/_Components/pdf/QuizPDFGenerator.jsx\n"));

/***/ })

});