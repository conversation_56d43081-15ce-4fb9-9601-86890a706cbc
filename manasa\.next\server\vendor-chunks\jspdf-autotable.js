"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jspdf-autotable";
exports.ids = ["vendor-chunks/jspdf-autotable"];
exports.modules = {

/***/ "(ssr)/./node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Cell: () => (/* binding */ Cell),\n/* harmony export */   CellHookData: () => (/* binding */ CellHookData),\n/* harmony export */   Column: () => (/* binding */ Column),\n/* harmony export */   HookData: () => (/* binding */ HookData),\n/* harmony export */   Row: () => (/* binding */ Row),\n/* harmony export */   Table: () => (/* binding */ Table),\n/* harmony export */   __createTable: () => (/* binding */ __createTable),\n/* harmony export */   __drawTable: () => (/* binding */ __drawTable),\n/* harmony export */   applyPlugin: () => (/* binding */ applyPlugin),\n/* harmony export */   autoTable: () => (/* binding */ autoTable),\n/* harmony export */   \"default\": () => (/* binding */ autoTable)\n/* harmony export */ });\n/**\n * Improved text function with halign and valign support\n * Inspiration from: http://stackoverflow.com/questions/28327510/align-text-right-using-jspdf/28433113#28433113\n */\nfunction autoTableText (text, x, y, styles, doc) {\n    styles = styles || {};\n    var PHYSICAL_LINE_HEIGHT = 1.15;\n    var k = doc.internal.scaleFactor;\n    var fontSize = doc.internal.getFontSize() / k;\n    var lineHeightFactor = doc.getLineHeightFactor\n        ? doc.getLineHeightFactor()\n        : PHYSICAL_LINE_HEIGHT;\n    var lineHeight = fontSize * lineHeightFactor;\n    var splitRegex = /\\r\\n|\\r|\\n/g;\n    var splitText = '';\n    var lineCount = 1;\n    if (styles.valign === 'middle' ||\n        styles.valign === 'bottom' ||\n        styles.halign === 'center' ||\n        styles.halign === 'right') {\n        splitText = typeof text === 'string' ? text.split(splitRegex) : text;\n        lineCount = splitText.length || 1;\n    }\n    // Align the top\n    y += fontSize * (2 - PHYSICAL_LINE_HEIGHT);\n    if (styles.valign === 'middle')\n        y -= (lineCount / 2) * lineHeight;\n    else if (styles.valign === 'bottom')\n        y -= lineCount * lineHeight;\n    if (styles.halign === 'center' || styles.halign === 'right') {\n        var alignSize = fontSize;\n        if (styles.halign === 'center')\n            alignSize *= 0.5;\n        if (splitText && lineCount >= 1) {\n            for (var iLine = 0; iLine < splitText.length; iLine++) {\n                doc.text(splitText[iLine], x - doc.getStringUnitWidth(splitText[iLine]) * alignSize, y);\n                y += lineHeight;\n            }\n            return doc;\n        }\n        x -= doc.getStringUnitWidth(text) * alignSize;\n    }\n    if (styles.halign === 'justify') {\n        doc.text(text, x, y, { maxWidth: styles.maxWidth || 100, align: 'justify' });\n    }\n    else {\n        doc.text(text, x, y);\n    }\n    return doc;\n}\n\nvar globalDefaults = {};\nvar DocHandler = /** @class */ (function () {\n    function DocHandler(jsPDFDocument) {\n        this.jsPDFDocument = jsPDFDocument;\n        this.userStyles = {\n            // Black for versions of jspdf without getTextColor\n            textColor: jsPDFDocument.getTextColor\n                ? this.jsPDFDocument.getTextColor()\n                : 0,\n            fontSize: jsPDFDocument.internal.getFontSize(),\n            fontStyle: jsPDFDocument.internal.getFont().fontStyle,\n            font: jsPDFDocument.internal.getFont().fontName,\n            // 0 for versions of jspdf without getLineWidth\n            lineWidth: jsPDFDocument.getLineWidth\n                ? this.jsPDFDocument.getLineWidth()\n                : 0,\n            // Black for versions of jspdf without getDrawColor\n            lineColor: jsPDFDocument.getDrawColor\n                ? this.jsPDFDocument.getDrawColor()\n                : 0,\n        };\n    }\n    DocHandler.setDefaults = function (defaults, doc) {\n        if (doc === void 0) { doc = null; }\n        if (doc) {\n            doc.__autoTableDocumentDefaults = defaults;\n        }\n        else {\n            globalDefaults = defaults;\n        }\n    };\n    DocHandler.unifyColor = function (c) {\n        if (Array.isArray(c)) {\n            return c;\n        }\n        else if (typeof c === 'number') {\n            return [c, c, c];\n        }\n        else if (typeof c === 'string') {\n            return [c];\n        }\n        else {\n            return null;\n        }\n    };\n    DocHandler.prototype.applyStyles = function (styles, fontOnly) {\n        // Font style needs to be applied before font\n        // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/632\n        var _a, _b, _c;\n        if (fontOnly === void 0) { fontOnly = false; }\n        if (styles.fontStyle && this.jsPDFDocument.setFontStyle) {\n            this.jsPDFDocument.setFontStyle(styles.fontStyle);\n        }\n        var _d = this.jsPDFDocument.internal.getFont(), fontStyle = _d.fontStyle, fontName = _d.fontName;\n        if (styles.font)\n            fontName = styles.font;\n        if (styles.fontStyle) {\n            fontStyle = styles.fontStyle;\n            var availableFontStyles = this.getFontList()[fontName];\n            if (availableFontStyles &&\n                availableFontStyles.indexOf(fontStyle) === -1 &&\n                this.jsPDFDocument.setFontStyle) {\n                // Common issue was that the default bold in headers\n                // made custom fonts not work. For example:\n                // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/653\n                this.jsPDFDocument.setFontStyle(availableFontStyles[0]);\n                fontStyle = availableFontStyles[0];\n            }\n        }\n        this.jsPDFDocument.setFont(fontName, fontStyle);\n        if (styles.fontSize)\n            this.jsPDFDocument.setFontSize(styles.fontSize);\n        if (fontOnly) {\n            return; // Performance improvement\n        }\n        var color = DocHandler.unifyColor(styles.fillColor);\n        if (color)\n            (_a = this.jsPDFDocument).setFillColor.apply(_a, color);\n        color = DocHandler.unifyColor(styles.textColor);\n        if (color)\n            (_b = this.jsPDFDocument).setTextColor.apply(_b, color);\n        color = DocHandler.unifyColor(styles.lineColor);\n        if (color)\n            (_c = this.jsPDFDocument).setDrawColor.apply(_c, color);\n        if (typeof styles.lineWidth === 'number') {\n            this.jsPDFDocument.setLineWidth(styles.lineWidth);\n        }\n    };\n    DocHandler.prototype.splitTextToSize = function (text, size, opts) {\n        return this.jsPDFDocument.splitTextToSize(text, size, opts);\n    };\n    /**\n     * Adds a rectangle to the PDF\n     * @param x Coordinate (in units declared at inception of PDF document) against left edge of the page\n     * @param y Coordinate (in units declared at inception of PDF document) against upper edge of the page\n     * @param width Width (in units declared at inception of PDF document)\n     * @param height Height (in units declared at inception of PDF document)\n     * @param fillStyle A string specifying the painting style or null. Valid styles include: 'S' [default] - stroke, 'F' - fill, and 'DF' (or 'FD') - fill then stroke.\n     */\n    DocHandler.prototype.rect = function (x, y, width, height, fillStyle) {\n        // null is excluded from fillStyle possible values because it isn't needed\n        // and is prone to bugs as it's used to postpone setting the style\n        // https://rawgit.com/MrRio/jsPDF/master/docs/jsPDF.html#rect\n        return this.jsPDFDocument.rect(x, y, width, height, fillStyle);\n    };\n    DocHandler.prototype.getLastAutoTable = function () {\n        return this.jsPDFDocument.lastAutoTable || null;\n    };\n    DocHandler.prototype.getTextWidth = function (text) {\n        return this.jsPDFDocument.getTextWidth(text);\n    };\n    DocHandler.prototype.getDocument = function () {\n        return this.jsPDFDocument;\n    };\n    DocHandler.prototype.setPage = function (page) {\n        this.jsPDFDocument.setPage(page);\n    };\n    DocHandler.prototype.addPage = function () {\n        return this.jsPDFDocument.addPage();\n    };\n    DocHandler.prototype.getFontList = function () {\n        return this.jsPDFDocument.getFontList();\n    };\n    DocHandler.prototype.getGlobalOptions = function () {\n        return globalDefaults || {};\n    };\n    DocHandler.prototype.getDocumentOptions = function () {\n        return this.jsPDFDocument.__autoTableDocumentDefaults || {};\n    };\n    DocHandler.prototype.pageSize = function () {\n        var pageSize = this.jsPDFDocument.internal.pageSize;\n        // JSPDF 1.4 uses get functions instead of properties on pageSize\n        if (pageSize.width == null) {\n            pageSize = { width: pageSize.getWidth(), height: pageSize.getHeight() };\n        }\n        return pageSize;\n    };\n    DocHandler.prototype.scaleFactor = function () {\n        return this.jsPDFDocument.internal.scaleFactor;\n    };\n    DocHandler.prototype.getLineHeightFactor = function () {\n        var doc = this.jsPDFDocument;\n        return doc.getLineHeightFactor ? doc.getLineHeightFactor() : 1.15;\n    };\n    DocHandler.prototype.getLineHeight = function (fontSize) {\n        return (fontSize / this.scaleFactor()) * this.getLineHeightFactor();\n    };\n    DocHandler.prototype.pageNumber = function () {\n        var pageInfo = this.jsPDFDocument.internal.getCurrentPageInfo();\n        if (!pageInfo) {\n            // Only recent versions of jspdf has pageInfo\n            return this.jsPDFDocument.internal.getNumberOfPages();\n        }\n        return pageInfo.pageNumber;\n    };\n    return DocHandler;\n}());\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nfunction __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\nvar HtmlRowInput = /** @class */ (function (_super) {\n    __extends(HtmlRowInput, _super);\n    function HtmlRowInput(element) {\n        var _this = _super.call(this) || this;\n        _this._element = element;\n        return _this;\n    }\n    return HtmlRowInput;\n}(Array));\n// Base style for all themes\nfunction defaultStyles(scaleFactor) {\n    return {\n        font: 'helvetica', // helvetica, times, courier\n        fontStyle: 'normal', // normal, bold, italic, bolditalic\n        overflow: 'linebreak', // linebreak, ellipsize, visible or hidden\n        fillColor: false, // Either false for transparent, rbg array e.g. [255, 255, 255] or gray level e.g 200\n        textColor: 20,\n        halign: 'left', // left, center, right, justify\n        valign: 'top', // top, middle, bottom\n        fontSize: 10,\n        cellPadding: 5 / scaleFactor, // number or {top,left,right,left,vertical,horizontal}\n        lineColor: 200,\n        lineWidth: 0,\n        cellWidth: 'auto', // 'auto'|'wrap'|number\n        minCellHeight: 0,\n        minCellWidth: 0,\n    };\n}\nfunction getTheme(name) {\n    var themes = {\n        striped: {\n            table: { fillColor: 255, textColor: 80, fontStyle: 'normal' },\n            head: { textColor: 255, fillColor: [41, 128, 185], fontStyle: 'bold' },\n            body: {},\n            foot: { textColor: 255, fillColor: [41, 128, 185], fontStyle: 'bold' },\n            alternateRow: { fillColor: 245 },\n        },\n        grid: {\n            table: {\n                fillColor: 255,\n                textColor: 80,\n                fontStyle: 'normal',\n                lineWidth: 0.1,\n            },\n            head: {\n                textColor: 255,\n                fillColor: [26, 188, 156],\n                fontStyle: 'bold',\n                lineWidth: 0,\n            },\n            body: {},\n            foot: {\n                textColor: 255,\n                fillColor: [26, 188, 156],\n                fontStyle: 'bold',\n                lineWidth: 0,\n            },\n            alternateRow: {},\n        },\n        plain: { head: { fontStyle: 'bold' }, foot: { fontStyle: 'bold' } },\n    };\n    return themes[name];\n}\n\nfunction getStringWidth(text, styles, doc) {\n    doc.applyStyles(styles, true);\n    var textArr = Array.isArray(text) ? text : [text];\n    var widestLineWidth = textArr\n        .map(function (text) { return doc.getTextWidth(text); })\n        .reduce(function (a, b) { return Math.max(a, b); }, 0);\n    return widestLineWidth;\n}\nfunction addTableBorder(doc, table, startPos, cursor) {\n    var lineWidth = table.settings.tableLineWidth;\n    var lineColor = table.settings.tableLineColor;\n    doc.applyStyles({ lineWidth: lineWidth, lineColor: lineColor });\n    var fillStyle = getFillStyle(lineWidth, false);\n    if (fillStyle) {\n        doc.rect(startPos.x, startPos.y, table.getWidth(doc.pageSize().width), cursor.y - startPos.y, fillStyle);\n    }\n}\nfunction getFillStyle(lineWidth, fillColor) {\n    var drawLine = lineWidth > 0;\n    var drawBackground = fillColor || fillColor === 0;\n    if (drawLine && drawBackground) {\n        return 'DF'; // Fill then stroke\n    }\n    else if (drawLine) {\n        return 'S'; // Only stroke (transparent background)\n    }\n    else if (drawBackground) {\n        return 'F'; // Only fill, no stroke\n    }\n    else {\n        return null;\n    }\n}\nfunction parseSpacing(value, defaultValue) {\n    var _a, _b, _c, _d;\n    value = value || defaultValue;\n    if (Array.isArray(value)) {\n        if (value.length >= 4) {\n            return {\n                top: value[0],\n                right: value[1],\n                bottom: value[2],\n                left: value[3],\n            };\n        }\n        else if (value.length === 3) {\n            return {\n                top: value[0],\n                right: value[1],\n                bottom: value[2],\n                left: value[1],\n            };\n        }\n        else if (value.length === 2) {\n            return {\n                top: value[0],\n                right: value[1],\n                bottom: value[0],\n                left: value[1],\n            };\n        }\n        else if (value.length === 1) {\n            value = value[0];\n        }\n        else {\n            value = defaultValue;\n        }\n    }\n    if (typeof value === 'object') {\n        if (typeof value.vertical === 'number') {\n            value.top = value.vertical;\n            value.bottom = value.vertical;\n        }\n        if (typeof value.horizontal === 'number') {\n            value.right = value.horizontal;\n            value.left = value.horizontal;\n        }\n        return {\n            left: (_a = value.left) !== null && _a !== void 0 ? _a : defaultValue,\n            top: (_b = value.top) !== null && _b !== void 0 ? _b : defaultValue,\n            right: (_c = value.right) !== null && _c !== void 0 ? _c : defaultValue,\n            bottom: (_d = value.bottom) !== null && _d !== void 0 ? _d : defaultValue,\n        };\n    }\n    if (typeof value !== 'number') {\n        value = defaultValue;\n    }\n    return { top: value, right: value, bottom: value, left: value };\n}\nfunction getPageAvailableWidth(doc, table) {\n    var margins = parseSpacing(table.settings.margin, 0);\n    return doc.pageSize().width - (margins.left + margins.right);\n}\n\n// Limitations\n// - No support for border spacing\n// - No support for transparency\nfunction parseCss(supportedFonts, element, scaleFactor, style, window) {\n    var result = {};\n    var pxScaleFactor = 96 / 72;\n    var backgroundColor = parseColor(element, function (elem) {\n        return window.getComputedStyle(elem)['backgroundColor'];\n    });\n    if (backgroundColor != null)\n        result.fillColor = backgroundColor;\n    var textColor = parseColor(element, function (elem) {\n        return window.getComputedStyle(elem)['color'];\n    });\n    if (textColor != null)\n        result.textColor = textColor;\n    var padding = parsePadding(style, scaleFactor);\n    if (padding)\n        result.cellPadding = padding;\n    var borderColorSide = 'borderTopColor';\n    var finalScaleFactor = pxScaleFactor * scaleFactor;\n    var btw = style.borderTopWidth;\n    if (style.borderBottomWidth === btw &&\n        style.borderRightWidth === btw &&\n        style.borderLeftWidth === btw) {\n        var borderWidth = (parseFloat(btw) || 0) / finalScaleFactor;\n        if (borderWidth)\n            result.lineWidth = borderWidth;\n    }\n    else {\n        result.lineWidth = {\n            top: (parseFloat(style.borderTopWidth) || 0) / finalScaleFactor,\n            right: (parseFloat(style.borderRightWidth) || 0) / finalScaleFactor,\n            bottom: (parseFloat(style.borderBottomWidth) || 0) / finalScaleFactor,\n            left: (parseFloat(style.borderLeftWidth) || 0) / finalScaleFactor,\n        };\n        // Choose border color of first available side\n        // could be improved by supporting object as lineColor\n        if (!result.lineWidth.top) {\n            if (result.lineWidth.right) {\n                borderColorSide = 'borderRightColor';\n            }\n            else if (result.lineWidth.bottom) {\n                borderColorSide = 'borderBottomColor';\n            }\n            else if (result.lineWidth.left) {\n                borderColorSide = 'borderLeftColor';\n            }\n        }\n    }\n    var borderColor = parseColor(element, function (elem) {\n        return window.getComputedStyle(elem)[borderColorSide];\n    });\n    if (borderColor != null)\n        result.lineColor = borderColor;\n    var accepted = ['left', 'right', 'center', 'justify'];\n    if (accepted.indexOf(style.textAlign) !== -1) {\n        result.halign = style.textAlign;\n    }\n    accepted = ['middle', 'bottom', 'top'];\n    if (accepted.indexOf(style.verticalAlign) !== -1) {\n        result.valign = style.verticalAlign;\n    }\n    var res = parseInt(style.fontSize || '');\n    if (!isNaN(res))\n        result.fontSize = res / pxScaleFactor;\n    var fontStyle = parseFontStyle(style);\n    if (fontStyle)\n        result.fontStyle = fontStyle;\n    var font = (style.fontFamily || '').toLowerCase();\n    if (supportedFonts.indexOf(font) !== -1) {\n        result.font = font;\n    }\n    return result;\n}\nfunction parseFontStyle(style) {\n    var res = '';\n    if (style.fontWeight === 'bold' ||\n        style.fontWeight === 'bolder' ||\n        parseInt(style.fontWeight) >= 700) {\n        res = 'bold';\n    }\n    if (style.fontStyle === 'italic' || style.fontStyle === 'oblique') {\n        res += 'italic';\n    }\n    return res;\n}\nfunction parseColor(element, styleGetter) {\n    var cssColor = realColor(element, styleGetter);\n    if (!cssColor)\n        return null;\n    var rgba = cssColor.match(/^rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)(?:,\\s*(\\d*\\.?\\d*))?\\)$/);\n    if (!rgba || !Array.isArray(rgba)) {\n        return null;\n    }\n    var color = [\n        parseInt(rgba[1]),\n        parseInt(rgba[2]),\n        parseInt(rgba[3]),\n    ];\n    var alpha = parseInt(rgba[4]);\n    if (alpha === 0 || isNaN(color[0]) || isNaN(color[1]) || isNaN(color[2])) {\n        return null;\n    }\n    return color;\n}\nfunction realColor(elem, styleGetter) {\n    var bg = styleGetter(elem);\n    if (bg === 'rgba(0, 0, 0, 0)' ||\n        bg === 'transparent' ||\n        bg === 'initial' ||\n        bg === 'inherit') {\n        if (elem.parentElement == null) {\n            return null;\n        }\n        return realColor(elem.parentElement, styleGetter);\n    }\n    else {\n        return bg;\n    }\n}\nfunction parsePadding(style, scaleFactor) {\n    var val = [\n        style.paddingTop,\n        style.paddingRight,\n        style.paddingBottom,\n        style.paddingLeft,\n    ];\n    var pxScaleFactor = 96 / (72 / scaleFactor);\n    var linePadding = (parseInt(style.lineHeight) - parseInt(style.fontSize)) / scaleFactor / 2;\n    var inputPadding = val.map(function (n) {\n        return parseInt(n || '0') / pxScaleFactor;\n    });\n    var padding = parseSpacing(inputPadding, 0);\n    if (linePadding > padding.top) {\n        padding.top = linePadding;\n    }\n    if (linePadding > padding.bottom) {\n        padding.bottom = linePadding;\n    }\n    return padding;\n}\n\nfunction parseHtml(doc, input, window, includeHiddenHtml, useCss) {\n    var _a, _b;\n    if (includeHiddenHtml === void 0) { includeHiddenHtml = false; }\n    if (useCss === void 0) { useCss = false; }\n    var tableElement;\n    if (typeof input === 'string') {\n        tableElement = window.document.querySelector(input);\n    }\n    else {\n        tableElement = input;\n    }\n    var supportedFonts = Object.keys(doc.getFontList());\n    var scaleFactor = doc.scaleFactor();\n    var head = [], body = [], foot = [];\n    if (!tableElement) {\n        console.error('Html table could not be found with input: ', input);\n        return { head: head, body: body, foot: foot };\n    }\n    for (var i = 0; i < tableElement.rows.length; i++) {\n        var element = tableElement.rows[i];\n        var tagName = (_b = (_a = element === null || element === void 0 ? void 0 : element.parentElement) === null || _a === void 0 ? void 0 : _a.tagName) === null || _b === void 0 ? void 0 : _b.toLowerCase();\n        var row = parseRowContent(supportedFonts, scaleFactor, window, element, includeHiddenHtml, useCss);\n        if (!row)\n            continue;\n        if (tagName === 'thead') {\n            head.push(row);\n        }\n        else if (tagName === 'tfoot') {\n            foot.push(row);\n        }\n        else {\n            // Add to body both if parent is tbody or table\n            body.push(row);\n        }\n    }\n    return { head: head, body: body, foot: foot };\n}\nfunction parseRowContent(supportedFonts, scaleFactor, window, row, includeHidden, useCss) {\n    var resultRow = new HtmlRowInput(row);\n    for (var i = 0; i < row.cells.length; i++) {\n        var cell = row.cells[i];\n        var style_1 = window.getComputedStyle(cell);\n        if (includeHidden || style_1.display !== 'none') {\n            var cellStyles = void 0;\n            if (useCss) {\n                cellStyles = parseCss(supportedFonts, cell, scaleFactor, style_1, window);\n            }\n            resultRow.push({\n                rowSpan: cell.rowSpan,\n                colSpan: cell.colSpan,\n                styles: cellStyles,\n                _element: cell,\n                content: parseCellContent(cell),\n            });\n        }\n    }\n    var style = window.getComputedStyle(row);\n    if (resultRow.length > 0 && (includeHidden || style.display !== 'none')) {\n        return resultRow;\n    }\n}\nfunction parseCellContent(orgCell) {\n    // Work on cloned node to make sure no changes are applied to html table\n    var cell = orgCell.cloneNode(true);\n    // Remove extra space and line breaks in markup to make it more similar to\n    // what would be shown in html\n    cell.innerHTML = cell.innerHTML.replace(/\\n/g, '').replace(/ +/g, ' ');\n    // Preserve <br> tags as line breaks in the pdf\n    cell.innerHTML = cell.innerHTML\n        .split(/<br.*?>/) //start with '<br' and ends with '>'.\n        .map(function (part) { return part.trim(); })\n        .join('\\n');\n    // innerText for ie\n    return cell.innerText || cell.textContent || '';\n}\n\nfunction validateInput(global, document, current) {\n    for (var _i = 0, _a = [global, document, current]; _i < _a.length; _i++) {\n        var options = _a[_i];\n        if (options && typeof options !== 'object') {\n            console.error('The options parameter should be of type object, is: ' + typeof options);\n        }\n        if (options.startY && typeof options.startY !== 'number') {\n            console.error('Invalid value for startY option', options.startY);\n            delete options.startY;\n        }\n    }\n}\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\nfunction assign(target, s, s1, s2, s3) {\n    if (target == null) {\n        throw new TypeError('Cannot convert undefined or null to object');\n    }\n    var to = Object(target);\n    for (var index = 1; index < arguments.length; index++) {\n        // eslint-disable-next-line prefer-rest-params\n        var nextSource = arguments[index];\n        if (nextSource != null) {\n            // Skip over if undefined or null\n            for (var nextKey in nextSource) {\n                // Avoid bugs when hasOwnProperty is shadowed\n                if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {\n                    to[nextKey] = nextSource[nextKey];\n                }\n            }\n        }\n    }\n    return to;\n}\n\nfunction parseInput(d, current) {\n    var doc = new DocHandler(d);\n    var document = doc.getDocumentOptions();\n    var global = doc.getGlobalOptions();\n    validateInput(global, document, current);\n    var options = assign({}, global, document, current);\n    var win;\n    if (typeof window !== 'undefined') {\n        win = window;\n    }\n    var styles = parseStyles(global, document, current);\n    var hooks = parseHooks(global, document, current);\n    var settings = parseSettings(doc, options);\n    var content = parseContent$1(doc, options, win);\n    return { id: current.tableId, content: content, hooks: hooks, styles: styles, settings: settings };\n}\nfunction parseStyles(gInput, dInput, cInput) {\n    var styleOptions = {\n        styles: {},\n        headStyles: {},\n        bodyStyles: {},\n        footStyles: {},\n        alternateRowStyles: {},\n        columnStyles: {},\n    };\n    var _loop_1 = function (prop) {\n        if (prop === 'columnStyles') {\n            var global_1 = gInput[prop];\n            var document_1 = dInput[prop];\n            var current = cInput[prop];\n            styleOptions.columnStyles = assign({}, global_1, document_1, current);\n        }\n        else {\n            var allOptions = [gInput, dInput, cInput];\n            var styles = allOptions.map(function (opts) { return opts[prop] || {}; });\n            styleOptions[prop] = assign({}, styles[0], styles[1], styles[2]);\n        }\n    };\n    for (var _i = 0, _a = Object.keys(styleOptions); _i < _a.length; _i++) {\n        var prop = _a[_i];\n        _loop_1(prop);\n    }\n    return styleOptions;\n}\nfunction parseHooks(global, document, current) {\n    var allOptions = [global, document, current];\n    var result = {\n        didParseCell: [],\n        willDrawCell: [],\n        didDrawCell: [],\n        willDrawPage: [],\n        didDrawPage: [],\n    };\n    for (var _i = 0, allOptions_1 = allOptions; _i < allOptions_1.length; _i++) {\n        var options = allOptions_1[_i];\n        if (options.didParseCell)\n            result.didParseCell.push(options.didParseCell);\n        if (options.willDrawCell)\n            result.willDrawCell.push(options.willDrawCell);\n        if (options.didDrawCell)\n            result.didDrawCell.push(options.didDrawCell);\n        if (options.willDrawPage)\n            result.willDrawPage.push(options.willDrawPage);\n        if (options.didDrawPage)\n            result.didDrawPage.push(options.didDrawPage);\n    }\n    return result;\n}\nfunction parseSettings(doc, options) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;\n    var margin = parseSpacing(options.margin, 40 / doc.scaleFactor());\n    var startY = (_a = getStartY(doc, options.startY)) !== null && _a !== void 0 ? _a : margin.top;\n    var showFoot;\n    if (options.showFoot === true) {\n        showFoot = 'everyPage';\n    }\n    else if (options.showFoot === false) {\n        showFoot = 'never';\n    }\n    else {\n        showFoot = (_b = options.showFoot) !== null && _b !== void 0 ? _b : 'everyPage';\n    }\n    var showHead;\n    if (options.showHead === true) {\n        showHead = 'everyPage';\n    }\n    else if (options.showHead === false) {\n        showHead = 'never';\n    }\n    else {\n        showHead = (_c = options.showHead) !== null && _c !== void 0 ? _c : 'everyPage';\n    }\n    var useCss = (_d = options.useCss) !== null && _d !== void 0 ? _d : false;\n    var theme = options.theme || (useCss ? 'plain' : 'striped');\n    var horizontalPageBreak = !!options.horizontalPageBreak;\n    var horizontalPageBreakRepeat = (_e = options.horizontalPageBreakRepeat) !== null && _e !== void 0 ? _e : null;\n    return {\n        includeHiddenHtml: (_f = options.includeHiddenHtml) !== null && _f !== void 0 ? _f : false,\n        useCss: useCss,\n        theme: theme,\n        startY: startY,\n        margin: margin,\n        pageBreak: (_g = options.pageBreak) !== null && _g !== void 0 ? _g : 'auto',\n        rowPageBreak: (_h = options.rowPageBreak) !== null && _h !== void 0 ? _h : 'auto',\n        tableWidth: (_j = options.tableWidth) !== null && _j !== void 0 ? _j : 'auto',\n        showHead: showHead,\n        showFoot: showFoot,\n        tableLineWidth: (_k = options.tableLineWidth) !== null && _k !== void 0 ? _k : 0,\n        tableLineColor: (_l = options.tableLineColor) !== null && _l !== void 0 ? _l : 200,\n        horizontalPageBreak: horizontalPageBreak,\n        horizontalPageBreakRepeat: horizontalPageBreakRepeat,\n        horizontalPageBreakBehaviour: (_m = options.horizontalPageBreakBehaviour) !== null && _m !== void 0 ? _m : 'afterAllRows',\n    };\n}\nfunction getStartY(doc, userStartY) {\n    var previous = doc.getLastAutoTable();\n    var sf = doc.scaleFactor();\n    var currentPage = doc.pageNumber();\n    var isSamePageAsPreviousTable = false;\n    if (previous && previous.startPageNumber) {\n        var endingPage = previous.startPageNumber + previous.pageNumber - 1;\n        isSamePageAsPreviousTable = endingPage === currentPage;\n    }\n    if (typeof userStartY === 'number') {\n        return userStartY;\n    }\n    else if (userStartY == null || userStartY === false) {\n        if (isSamePageAsPreviousTable && (previous === null || previous === void 0 ? void 0 : previous.finalY) != null) {\n            // Some users had issues with overlapping tables when they used multiple\n            // tables without setting startY so setting it here to a sensible default.\n            return previous.finalY + 20 / sf;\n        }\n    }\n    return null;\n}\nfunction parseContent$1(doc, options, window) {\n    var head = options.head || [];\n    var body = options.body || [];\n    var foot = options.foot || [];\n    if (options.html) {\n        var hidden = options.includeHiddenHtml;\n        if (window) {\n            var htmlContent = parseHtml(doc, options.html, window, hidden, options.useCss) || {};\n            head = htmlContent.head || head;\n            body = htmlContent.body || head;\n            foot = htmlContent.foot || head;\n        }\n        else {\n            console.error('Cannot parse html in non browser environment');\n        }\n    }\n    var columns = options.columns || parseColumns(head, body, foot);\n    return { columns: columns, head: head, body: body, foot: foot };\n}\nfunction parseColumns(head, body, foot) {\n    var firstRow = head[0] || body[0] || foot[0] || [];\n    var result = [];\n    Object.keys(firstRow)\n        .filter(function (key) { return key !== '_element'; })\n        .forEach(function (key) {\n        var colSpan = 1;\n        var input;\n        if (Array.isArray(firstRow)) {\n            input = firstRow[parseInt(key)];\n        }\n        else {\n            input = firstRow[key];\n        }\n        if (typeof input === 'object' && !Array.isArray(input)) {\n            colSpan = (input === null || input === void 0 ? void 0 : input.colSpan) || 1;\n        }\n        for (var i = 0; i < colSpan; i++) {\n            var id = void 0;\n            if (Array.isArray(firstRow)) {\n                id = result.length;\n            }\n            else {\n                id = key + (i > 0 ? \"_\".concat(i) : '');\n            }\n            var rowResult = { dataKey: id };\n            result.push(rowResult);\n        }\n    });\n    return result;\n}\n\nvar HookData = /** @class */ (function () {\n    function HookData(doc, table, cursor) {\n        this.table = table;\n        this.pageNumber = table.pageNumber;\n        this.settings = table.settings;\n        this.cursor = cursor;\n        this.doc = doc.getDocument();\n    }\n    return HookData;\n}());\nvar CellHookData = /** @class */ (function (_super) {\n    __extends(CellHookData, _super);\n    function CellHookData(doc, table, cell, row, column, cursor) {\n        var _this = _super.call(this, doc, table, cursor) || this;\n        _this.cell = cell;\n        _this.row = row;\n        _this.column = column;\n        _this.section = row.section;\n        return _this;\n    }\n    return CellHookData;\n}(HookData));\n\nvar Table = /** @class */ (function () {\n    function Table(input, content) {\n        this.pageNumber = 1;\n        this.id = input.id;\n        this.settings = input.settings;\n        this.styles = input.styles;\n        this.hooks = input.hooks;\n        this.columns = content.columns;\n        this.head = content.head;\n        this.body = content.body;\n        this.foot = content.foot;\n    }\n    Table.prototype.getHeadHeight = function (columns) {\n        return this.head.reduce(function (acc, row) { return acc + row.getMaxCellHeight(columns); }, 0);\n    };\n    Table.prototype.getFootHeight = function (columns) {\n        return this.foot.reduce(function (acc, row) { return acc + row.getMaxCellHeight(columns); }, 0);\n    };\n    Table.prototype.allRows = function () {\n        return this.head.concat(this.body).concat(this.foot);\n    };\n    Table.prototype.callCellHooks = function (doc, handlers, cell, row, column, cursor) {\n        for (var _i = 0, handlers_1 = handlers; _i < handlers_1.length; _i++) {\n            var handler = handlers_1[_i];\n            var data = new CellHookData(doc, this, cell, row, column, cursor);\n            var result = handler(data) === false;\n            // Make sure text is always string[] since user can assign string\n            cell.text = Array.isArray(cell.text) ? cell.text : [cell.text];\n            if (result) {\n                return false;\n            }\n        }\n        return true;\n    };\n    Table.prototype.callEndPageHooks = function (doc, cursor) {\n        doc.applyStyles(doc.userStyles);\n        for (var _i = 0, _a = this.hooks.didDrawPage; _i < _a.length; _i++) {\n            var handler = _a[_i];\n            handler(new HookData(doc, this, cursor));\n        }\n    };\n    Table.prototype.callWillDrawPageHooks = function (doc, cursor) {\n        for (var _i = 0, _a = this.hooks.willDrawPage; _i < _a.length; _i++) {\n            var handler = _a[_i];\n            handler(new HookData(doc, this, cursor));\n        }\n    };\n    Table.prototype.getWidth = function (pageWidth) {\n        if (typeof this.settings.tableWidth === 'number') {\n            return this.settings.tableWidth;\n        }\n        else if (this.settings.tableWidth === 'wrap') {\n            var wrappedWidth = this.columns.reduce(function (total, col) { return total + col.wrappedWidth; }, 0);\n            return wrappedWidth;\n        }\n        else {\n            var margin = this.settings.margin;\n            return pageWidth - margin.left - margin.right;\n        }\n    };\n    return Table;\n}());\nvar Row = /** @class */ (function () {\n    function Row(raw, index, section, cells, spansMultiplePages) {\n        if (spansMultiplePages === void 0) { spansMultiplePages = false; }\n        this.height = 0;\n        this.raw = raw;\n        if (raw instanceof HtmlRowInput) {\n            this.raw = raw._element;\n            this.element = raw._element;\n        }\n        this.index = index;\n        this.section = section;\n        this.cells = cells;\n        this.spansMultiplePages = spansMultiplePages;\n    }\n    Row.prototype.getMaxCellHeight = function (columns) {\n        var _this = this;\n        return columns.reduce(function (acc, column) { var _a; return Math.max(acc, ((_a = _this.cells[column.index]) === null || _a === void 0 ? void 0 : _a.height) || 0); }, 0);\n    };\n    Row.prototype.hasRowSpan = function (columns) {\n        var _this = this;\n        return (columns.filter(function (column) {\n            var cell = _this.cells[column.index];\n            if (!cell)\n                return false;\n            return cell.rowSpan > 1;\n        }).length > 0);\n    };\n    Row.prototype.canEntireRowFit = function (height, columns) {\n        return this.getMaxCellHeight(columns) <= height;\n    };\n    Row.prototype.getMinimumRowHeight = function (columns, doc) {\n        var _this = this;\n        return columns.reduce(function (acc, column) {\n            var cell = _this.cells[column.index];\n            if (!cell)\n                return 0;\n            var lineHeight = doc.getLineHeight(cell.styles.fontSize);\n            var vPadding = cell.padding('vertical');\n            var oneRowHeight = vPadding + lineHeight;\n            return oneRowHeight > acc ? oneRowHeight : acc;\n        }, 0);\n    };\n    return Row;\n}());\nvar Cell = /** @class */ (function () {\n    function Cell(raw, styles, section) {\n        var _a;\n        this.contentHeight = 0;\n        this.contentWidth = 0;\n        this.wrappedWidth = 0;\n        this.minReadableWidth = 0;\n        this.minWidth = 0;\n        this.width = 0;\n        this.height = 0;\n        this.x = 0;\n        this.y = 0;\n        this.styles = styles;\n        this.section = section;\n        this.raw = raw;\n        var content = raw;\n        if (raw != null && typeof raw === 'object' && !Array.isArray(raw)) {\n            this.rowSpan = raw.rowSpan || 1;\n            this.colSpan = raw.colSpan || 1;\n            content = (_a = raw.content) !== null && _a !== void 0 ? _a : raw;\n            if (raw._element) {\n                this.raw = raw._element;\n            }\n        }\n        else {\n            this.rowSpan = 1;\n            this.colSpan = 1;\n        }\n        // Stringify 0 and false, but not undefined or null\n        var text = content != null ? '' + content : '';\n        var splitRegex = /\\r\\n|\\r|\\n/g;\n        this.text = text.split(splitRegex);\n    }\n    Cell.prototype.getTextPos = function () {\n        var y;\n        if (this.styles.valign === 'top') {\n            y = this.y + this.padding('top');\n        }\n        else if (this.styles.valign === 'bottom') {\n            y = this.y + this.height - this.padding('bottom');\n        }\n        else {\n            var netHeight = this.height - this.padding('vertical');\n            y = this.y + netHeight / 2 + this.padding('top');\n        }\n        var x;\n        if (this.styles.halign === 'right') {\n            x = this.x + this.width - this.padding('right');\n        }\n        else if (this.styles.halign === 'center') {\n            var netWidth = this.width - this.padding('horizontal');\n            x = this.x + netWidth / 2 + this.padding('left');\n        }\n        else {\n            x = this.x + this.padding('left');\n        }\n        return { x: x, y: y };\n    };\n    // TODO (v4): replace parameters with only (lineHeight)\n    Cell.prototype.getContentHeight = function (scaleFactor, lineHeightFactor) {\n        if (lineHeightFactor === void 0) { lineHeightFactor = 1.15; }\n        var lineCount = Array.isArray(this.text) ? this.text.length : 1;\n        var lineHeight = (this.styles.fontSize / scaleFactor) * lineHeightFactor;\n        var height = lineCount * lineHeight + this.padding('vertical');\n        return Math.max(height, this.styles.minCellHeight);\n    };\n    Cell.prototype.padding = function (name) {\n        var padding = parseSpacing(this.styles.cellPadding, 0);\n        if (name === 'vertical') {\n            return padding.top + padding.bottom;\n        }\n        else if (name === 'horizontal') {\n            return padding.left + padding.right;\n        }\n        else {\n            return padding[name];\n        }\n    };\n    return Cell;\n}());\nvar Column = /** @class */ (function () {\n    function Column(dataKey, raw, index) {\n        this.wrappedWidth = 0;\n        this.minReadableWidth = 0;\n        this.minWidth = 0;\n        this.width = 0;\n        this.dataKey = dataKey;\n        this.raw = raw;\n        this.index = index;\n    }\n    Column.prototype.getMaxCustomCellWidth = function (table) {\n        var max = 0;\n        for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n            var row = _a[_i];\n            var cell = row.cells[this.index];\n            if (cell && typeof cell.styles.cellWidth === 'number') {\n                max = Math.max(max, cell.styles.cellWidth);\n            }\n        }\n        return max;\n    };\n    return Column;\n}());\n\n/**\n * Calculate the column widths\n */\nfunction calculateWidths(doc, table) {\n    calculate(doc, table);\n    var resizableColumns = [];\n    var initialTableWidth = 0;\n    table.columns.forEach(function (column) {\n        var customWidth = column.getMaxCustomCellWidth(table);\n        if (customWidth) {\n            // final column width\n            column.width = customWidth;\n        }\n        else {\n            // initial column width (will be resized)\n            column.width = column.wrappedWidth;\n            resizableColumns.push(column);\n        }\n        initialTableWidth += column.width;\n    });\n    // width difference that needs to be distributed\n    var resizeWidth = table.getWidth(doc.pageSize().width) - initialTableWidth;\n    // first resize attempt: with respect to minReadableWidth and minWidth\n    if (resizeWidth) {\n        resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) {\n            return Math.max(column.minReadableWidth, column.minWidth);\n        });\n    }\n    // second resize attempt: ignore minReadableWidth but respect minWidth\n    if (resizeWidth) {\n        resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) { return column.minWidth; });\n    }\n    resizeWidth = Math.abs(resizeWidth);\n    if (!table.settings.horizontalPageBreak &&\n        resizeWidth > 0.1 / doc.scaleFactor()) {\n        // Table can't get smaller due to custom-width or minWidth restrictions\n        // We can't really do much here. Up to user to for example\n        // reduce font size, increase page size or remove custom cell widths\n        // to allow more columns to be reduced in size\n        resizeWidth = resizeWidth < 1 ? resizeWidth : Math.round(resizeWidth);\n        console.warn(\"Of the table content, \".concat(resizeWidth, \" units width could not fit page\"));\n    }\n    applyColSpans(table);\n    fitContent(table, doc);\n    applyRowSpans(table);\n}\nfunction calculate(doc, table) {\n    var sf = doc.scaleFactor();\n    var horizontalPageBreak = table.settings.horizontalPageBreak;\n    var availablePageWidth = getPageAvailableWidth(doc, table);\n    table.allRows().forEach(function (row) {\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var cell = row.cells[column.index];\n            if (!cell)\n                continue;\n            var hooks = table.hooks.didParseCell;\n            table.callCellHooks(doc, hooks, cell, row, column, null);\n            var padding = cell.padding('horizontal');\n            cell.contentWidth = getStringWidth(cell.text, cell.styles, doc) + padding;\n            // Using [^\\S\\u00A0] instead of \\s ensures that we split the text on all\n            // whitespace except non-breaking spaces (\\u00A0). We need to preserve\n            // them in the split process to ensure correct word separation and width\n            // calculation.\n            var longestWordWidth = getStringWidth(cell.text.join(' ').split(/[^\\S\\u00A0]+/), cell.styles, doc);\n            cell.minReadableWidth = longestWordWidth + cell.padding('horizontal');\n            if (typeof cell.styles.cellWidth === 'number') {\n                cell.minWidth = cell.styles.cellWidth;\n                cell.wrappedWidth = cell.styles.cellWidth;\n            }\n            else if (cell.styles.cellWidth === 'wrap' ||\n                horizontalPageBreak === true) {\n                // cell width should not be more than available page width\n                if (cell.contentWidth > availablePageWidth) {\n                    cell.minWidth = availablePageWidth;\n                    cell.wrappedWidth = availablePageWidth;\n                }\n                else {\n                    cell.minWidth = cell.contentWidth;\n                    cell.wrappedWidth = cell.contentWidth;\n                }\n            }\n            else {\n                // auto\n                var defaultMinWidth = 10 / sf;\n                cell.minWidth = cell.styles.minCellWidth || defaultMinWidth;\n                cell.wrappedWidth = cell.contentWidth;\n                if (cell.minWidth > cell.wrappedWidth) {\n                    cell.wrappedWidth = cell.minWidth;\n                }\n            }\n        }\n    });\n    table.allRows().forEach(function (row) {\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var cell = row.cells[column.index];\n            // For now we ignore the minWidth and wrappedWidth of colspan cells when calculating colspan widths.\n            // Could probably be improved upon however.\n            if (cell && cell.colSpan === 1) {\n                column.wrappedWidth = Math.max(column.wrappedWidth, cell.wrappedWidth);\n                column.minWidth = Math.max(column.minWidth, cell.minWidth);\n                column.minReadableWidth = Math.max(column.minReadableWidth, cell.minReadableWidth);\n            }\n            else {\n                // Respect cellWidth set in columnStyles even if there is no cells for this column\n                // or if the column only have colspan cells. Since the width of colspan cells\n                // does not affect the width of columns, setting columnStyles cellWidth enables the\n                // user to at least do it manually.\n                // Note that this is not perfect for now since for example row and table styles are\n                // not accounted for\n                var columnStyles = table.styles.columnStyles[column.dataKey] ||\n                    table.styles.columnStyles[column.index] ||\n                    {};\n                var cellWidth = columnStyles.cellWidth || columnStyles.minCellWidth;\n                if (cellWidth && typeof cellWidth === 'number') {\n                    column.minWidth = cellWidth;\n                    column.wrappedWidth = cellWidth;\n                }\n            }\n            if (cell) {\n                // Make sure all columns get at least min width even though width calculations are not based on them\n                if (cell.colSpan > 1 && !column.minWidth) {\n                    column.minWidth = cell.minWidth;\n                }\n                if (cell.colSpan > 1 && !column.wrappedWidth) {\n                    column.wrappedWidth = cell.minWidth;\n                }\n            }\n        }\n    });\n}\n/**\n * Distribute resizeWidth on passed resizable columns\n */\nfunction resizeColumns(columns, resizeWidth, getMinWidth) {\n    var initialResizeWidth = resizeWidth;\n    var sumWrappedWidth = columns.reduce(function (acc, column) { return acc + column.wrappedWidth; }, 0);\n    for (var i = 0; i < columns.length; i++) {\n        var column = columns[i];\n        var ratio = column.wrappedWidth / sumWrappedWidth;\n        var suggestedChange = initialResizeWidth * ratio;\n        var suggestedWidth = column.width + suggestedChange;\n        var minWidth = getMinWidth(column);\n        var newWidth = suggestedWidth < minWidth ? minWidth : suggestedWidth;\n        resizeWidth -= newWidth - column.width;\n        column.width = newWidth;\n    }\n    resizeWidth = Math.round(resizeWidth * 1e10) / 1e10;\n    // Run the resizer again if there's remaining width needs\n    // to be distributed and there're columns that can be resized\n    if (resizeWidth) {\n        var resizableColumns = columns.filter(function (column) {\n            return resizeWidth < 0\n                ? column.width > getMinWidth(column) // check if column can shrink\n                : true; // check if column can grow\n        });\n        if (resizableColumns.length) {\n            resizeWidth = resizeColumns(resizableColumns, resizeWidth, getMinWidth);\n        }\n    }\n    return resizeWidth;\n}\nfunction applyRowSpans(table) {\n    var rowSpanCells = {};\n    var colRowSpansLeft = 1;\n    var all = table.allRows();\n    for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n        var row = all[rowIndex];\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var data = rowSpanCells[column.index];\n            if (colRowSpansLeft > 1) {\n                colRowSpansLeft--;\n                delete row.cells[column.index];\n            }\n            else if (data) {\n                data.cell.height += row.height;\n                colRowSpansLeft = data.cell.colSpan;\n                delete row.cells[column.index];\n                data.left--;\n                if (data.left <= 1) {\n                    delete rowSpanCells[column.index];\n                }\n            }\n            else {\n                var cell = row.cells[column.index];\n                if (!cell) {\n                    continue;\n                }\n                cell.height = row.height;\n                if (cell.rowSpan > 1) {\n                    var remaining = all.length - rowIndex;\n                    var left = cell.rowSpan > remaining ? remaining : cell.rowSpan;\n                    rowSpanCells[column.index] = { cell: cell, left: left, row: row };\n                }\n            }\n        }\n    }\n}\nfunction applyColSpans(table) {\n    var all = table.allRows();\n    for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n        var row = all[rowIndex];\n        var colSpanCell = null;\n        var combinedColSpanWidth = 0;\n        var colSpansLeft = 0;\n        for (var columnIndex = 0; columnIndex < table.columns.length; columnIndex++) {\n            var column = table.columns[columnIndex];\n            // Width and colspan\n            colSpansLeft -= 1;\n            if (colSpansLeft > 1 && table.columns[columnIndex + 1]) {\n                combinedColSpanWidth += column.width;\n                delete row.cells[column.index];\n            }\n            else if (colSpanCell) {\n                var cell = colSpanCell;\n                delete row.cells[column.index];\n                colSpanCell = null;\n                cell.width = column.width + combinedColSpanWidth;\n            }\n            else {\n                var cell = row.cells[column.index];\n                if (!cell)\n                    continue;\n                colSpansLeft = cell.colSpan;\n                combinedColSpanWidth = 0;\n                if (cell.colSpan > 1) {\n                    colSpanCell = cell;\n                    combinedColSpanWidth += column.width;\n                    continue;\n                }\n                cell.width = column.width + combinedColSpanWidth;\n            }\n        }\n    }\n}\nfunction fitContent(table, doc) {\n    var rowSpanHeight = { count: 0, height: 0 };\n    for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n        var row = _a[_i];\n        for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n            var column = _c[_b];\n            var cell = row.cells[column.index];\n            if (!cell)\n                continue;\n            doc.applyStyles(cell.styles, true);\n            var textSpace = cell.width - cell.padding('horizontal');\n            if (cell.styles.overflow === 'linebreak') {\n                // Add one pt to textSpace to fix rounding error\n                cell.text = doc.splitTextToSize(cell.text, textSpace + 1 / doc.scaleFactor(), { fontSize: cell.styles.fontSize });\n            }\n            else if (cell.styles.overflow === 'ellipsize') {\n                cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '...');\n            }\n            else if (cell.styles.overflow === 'hidden') {\n                cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '');\n            }\n            else if (typeof cell.styles.overflow === 'function') {\n                var result = cell.styles.overflow(cell.text, textSpace);\n                if (typeof result === 'string') {\n                    cell.text = [result];\n                }\n                else {\n                    cell.text = result;\n                }\n            }\n            cell.contentHeight = cell.getContentHeight(doc.scaleFactor(), doc.getLineHeightFactor());\n            var realContentHeight = cell.contentHeight / cell.rowSpan;\n            if (cell.rowSpan > 1 &&\n                rowSpanHeight.count * rowSpanHeight.height <\n                    realContentHeight * cell.rowSpan) {\n                rowSpanHeight = { height: realContentHeight, count: cell.rowSpan };\n            }\n            else if (rowSpanHeight && rowSpanHeight.count > 0) {\n                if (rowSpanHeight.height > realContentHeight) {\n                    realContentHeight = rowSpanHeight.height;\n                }\n            }\n            if (realContentHeight > row.height) {\n                row.height = realContentHeight;\n            }\n        }\n        rowSpanHeight.count--;\n    }\n}\nfunction ellipsize(text, width, styles, doc, overflow) {\n    return text.map(function (str) { return ellipsizeStr(str, width, styles, doc, overflow); });\n}\nfunction ellipsizeStr(text, width, styles, doc, overflow) {\n    var precision = 10000 * doc.scaleFactor();\n    width = Math.ceil(width * precision) / precision;\n    if (width >= getStringWidth(text, styles, doc)) {\n        return text;\n    }\n    while (width < getStringWidth(text + overflow, styles, doc)) {\n        if (text.length <= 1) {\n            break;\n        }\n        text = text.substring(0, text.length - 1);\n    }\n    return text.trim() + overflow;\n}\n\nfunction createTable(jsPDFDoc, input) {\n    var doc = new DocHandler(jsPDFDoc);\n    var content = parseContent(input, doc.scaleFactor());\n    var table = new Table(input, content);\n    calculateWidths(doc, table);\n    doc.applyStyles(doc.userStyles);\n    return table;\n}\nfunction parseContent(input, sf) {\n    var content = input.content;\n    var columns = createColumns(content.columns);\n    // If no head or foot is set, try generating it with content from columns\n    if (content.head.length === 0) {\n        var sectionRow = generateSectionRow(columns, 'head');\n        if (sectionRow)\n            content.head.push(sectionRow);\n    }\n    if (content.foot.length === 0) {\n        var sectionRow = generateSectionRow(columns, 'foot');\n        if (sectionRow)\n            content.foot.push(sectionRow);\n    }\n    var theme = input.settings.theme;\n    var styles = input.styles;\n    return {\n        columns: columns,\n        head: parseSection('head', content.head, columns, styles, theme, sf),\n        body: parseSection('body', content.body, columns, styles, theme, sf),\n        foot: parseSection('foot', content.foot, columns, styles, theme, sf),\n    };\n}\nfunction parseSection(sectionName, sectionRows, columns, styleProps, theme, scaleFactor) {\n    var rowSpansLeftForColumn = {};\n    var result = sectionRows.map(function (rawRow, rowIndex) {\n        var skippedRowForRowSpans = 0;\n        var cells = {};\n        var colSpansAdded = 0;\n        var columnSpansLeft = 0;\n        for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n            var column = columns_1[_i];\n            if (rowSpansLeftForColumn[column.index] == null ||\n                rowSpansLeftForColumn[column.index].left === 0) {\n                if (columnSpansLeft === 0) {\n                    var rawCell = void 0;\n                    if (Array.isArray(rawRow)) {\n                        rawCell =\n                            rawRow[column.index - colSpansAdded - skippedRowForRowSpans];\n                    }\n                    else {\n                        rawCell = rawRow[column.dataKey];\n                    }\n                    var cellInputStyles = {};\n                    if (typeof rawCell === 'object' && !Array.isArray(rawCell)) {\n                        cellInputStyles = (rawCell === null || rawCell === void 0 ? void 0 : rawCell.styles) || {};\n                    }\n                    var styles = cellStyles(sectionName, column, rowIndex, theme, styleProps, scaleFactor, cellInputStyles);\n                    var cell = new Cell(rawCell, styles, sectionName);\n                    // dataKey is not used internally no more but keep for\n                    // backwards compat in hooks\n                    cells[column.dataKey] = cell;\n                    cells[column.index] = cell;\n                    columnSpansLeft = cell.colSpan - 1;\n                    rowSpansLeftForColumn[column.index] = {\n                        left: cell.rowSpan - 1,\n                        times: columnSpansLeft,\n                    };\n                }\n                else {\n                    columnSpansLeft--;\n                    colSpansAdded++;\n                }\n            }\n            else {\n                rowSpansLeftForColumn[column.index].left--;\n                columnSpansLeft = rowSpansLeftForColumn[column.index].times;\n                skippedRowForRowSpans++;\n            }\n        }\n        return new Row(rawRow, rowIndex, sectionName, cells);\n    });\n    return result;\n}\nfunction generateSectionRow(columns, section) {\n    var sectionRow = {};\n    columns.forEach(function (col) {\n        if (col.raw != null) {\n            var title = getSectionTitle(section, col.raw);\n            if (title != null)\n                sectionRow[col.dataKey] = title;\n        }\n    });\n    return Object.keys(sectionRow).length > 0 ? sectionRow : null;\n}\nfunction getSectionTitle(section, column) {\n    if (section === 'head') {\n        if (typeof column === 'object') {\n            return column.header || null;\n        }\n        else if (typeof column === 'string' || typeof column === 'number') {\n            return column;\n        }\n    }\n    else if (section === 'foot' && typeof column === 'object') {\n        return column.footer;\n    }\n    return null;\n}\nfunction createColumns(columns) {\n    return columns.map(function (input, index) {\n        var _a;\n        var key;\n        if (typeof input === 'object') {\n            key = (_a = input.dataKey) !== null && _a !== void 0 ? _a : index;\n        }\n        else {\n            key = index;\n        }\n        return new Column(key, input, index);\n    });\n}\nfunction cellStyles(sectionName, column, rowIndex, themeName, styles, scaleFactor, cellInputStyles) {\n    var theme = getTheme(themeName);\n    var sectionStyles;\n    if (sectionName === 'head') {\n        sectionStyles = styles.headStyles;\n    }\n    else if (sectionName === 'body') {\n        sectionStyles = styles.bodyStyles;\n    }\n    else if (sectionName === 'foot') {\n        sectionStyles = styles.footStyles;\n    }\n    var otherStyles = assign({}, theme.table, theme[sectionName], styles.styles, sectionStyles);\n    var columnStyles = styles.columnStyles[column.dataKey] ||\n        styles.columnStyles[column.index] ||\n        {};\n    var colStyles = sectionName === 'body' ? columnStyles : {};\n    var rowStyles = sectionName === 'body' && rowIndex % 2 === 0\n        ? assign({}, theme.alternateRow, styles.alternateRowStyles)\n        : {};\n    var defaultStyle = defaultStyles(scaleFactor);\n    var themeStyles = assign({}, defaultStyle, otherStyles, rowStyles, colStyles);\n    return assign(themeStyles, cellInputStyles);\n}\n\n// get columns can be fit into page\nfunction getColumnsCanFitInPage(doc, table, config) {\n    var _a;\n    if (config === void 0) { config = {}; }\n    // Get page width\n    var remainingWidth = getPageAvailableWidth(doc, table);\n    // Get column data key to repeat\n    var repeatColumnsMap = new Map();\n    var colIndexes = [];\n    var columns = [];\n    var horizontalPageBreakRepeat = [];\n    if (Array.isArray(table.settings.horizontalPageBreakRepeat)) {\n        horizontalPageBreakRepeat = table.settings.horizontalPageBreakRepeat;\n        // It can be a single value of type string or number (even number: 0)\n    }\n    else if (typeof table.settings.horizontalPageBreakRepeat === 'string' ||\n        typeof table.settings.horizontalPageBreakRepeat === 'number') {\n        horizontalPageBreakRepeat = [table.settings.horizontalPageBreakRepeat];\n    }\n    // Code to repeat the given column in split pages\n    horizontalPageBreakRepeat.forEach(function (field) {\n        var col = table.columns.find(function (item) { return item.dataKey === field || item.index === field; });\n        if (col && !repeatColumnsMap.has(col.index)) {\n            repeatColumnsMap.set(col.index, true);\n            colIndexes.push(col.index);\n            columns.push(table.columns[col.index]);\n            remainingWidth -= col.wrappedWidth;\n        }\n    });\n    var first = true;\n    var i = (_a = config === null || config === void 0 ? void 0 : config.start) !== null && _a !== void 0 ? _a : 0; // make sure couter is initiated outside the loop\n    while (i < table.columns.length) {\n        // Prevent duplicates\n        if (repeatColumnsMap.has(i)) {\n            i++;\n            continue;\n        }\n        var colWidth = table.columns[i].wrappedWidth;\n        // Take at least one column even if it doesn't fit\n        if (first || remainingWidth >= colWidth) {\n            first = false;\n            colIndexes.push(i);\n            columns.push(table.columns[i]);\n            remainingWidth -= colWidth;\n        }\n        else {\n            break;\n        }\n        i++;\n    }\n    return { colIndexes: colIndexes, columns: columns, lastIndex: i - 1 };\n}\nfunction calculateAllColumnsCanFitInPage(doc, table) {\n    var allResults = [];\n    for (var i = 0; i < table.columns.length; i++) {\n        var result = getColumnsCanFitInPage(doc, table, { start: i });\n        if (result.columns.length) {\n            allResults.push(result);\n            i = result.lastIndex;\n        }\n    }\n    return allResults;\n}\n\nfunction drawTable(jsPDFDoc, table) {\n    var settings = table.settings;\n    var startY = settings.startY;\n    var margin = settings.margin;\n    var cursor = { x: margin.left, y: startY };\n    var sectionsHeight = table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n    var minTableBottomPos = startY + margin.bottom + sectionsHeight;\n    if (settings.pageBreak === 'avoid') {\n        var rows = table.body;\n        var tableHeight = rows.reduce(function (acc, row) { return acc + row.height; }, 0);\n        minTableBottomPos += tableHeight;\n    }\n    var doc = new DocHandler(jsPDFDoc);\n    if (settings.pageBreak === 'always' ||\n        (settings.startY != null && minTableBottomPos > doc.pageSize().height)) {\n        nextPage(doc);\n        cursor.y = margin.top;\n    }\n    table.callWillDrawPageHooks(doc, cursor);\n    var startPos = assign({}, cursor);\n    table.startPageNumber = doc.pageNumber();\n    if (settings.horizontalPageBreak) {\n        // managed flow for split columns\n        printTableWithHorizontalPageBreak(doc, table, startPos, cursor);\n    }\n    else {\n        // normal flow\n        doc.applyStyles(doc.userStyles);\n        if (settings.showHead === 'firstPage' ||\n            settings.showHead === 'everyPage') {\n            table.head.forEach(function (row) {\n                return printRow(doc, table, row, cursor, table.columns);\n            });\n        }\n        doc.applyStyles(doc.userStyles);\n        table.body.forEach(function (row, index) {\n            var isLastRow = index === table.body.length - 1;\n            printFullRow(doc, table, row, isLastRow, startPos, cursor, table.columns);\n        });\n        doc.applyStyles(doc.userStyles);\n        if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n            table.foot.forEach(function (row) {\n                return printRow(doc, table, row, cursor, table.columns);\n            });\n        }\n    }\n    addTableBorder(doc, table, startPos, cursor);\n    table.callEndPageHooks(doc, cursor);\n    table.finalY = cursor.y;\n    jsPDFDoc.lastAutoTable = table;\n    doc.applyStyles(doc.userStyles);\n}\nfunction printTableWithHorizontalPageBreak(doc, table, startPos, cursor) {\n    // calculate width of columns and render only those which can fit into page\n    var allColumnsCanFitResult = calculateAllColumnsCanFitInPage(doc, table);\n    var settings = table.settings;\n    if (settings.horizontalPageBreakBehaviour === 'afterAllRows') {\n        allColumnsCanFitResult.forEach(function (colsAndIndexes, index) {\n            doc.applyStyles(doc.userStyles);\n            // add page to print next columns in new page\n            if (index > 0) {\n                // When adding a page here, make sure not to print the footers\n                // because they were already printed before on this same loop\n                addPage(doc, table, startPos, cursor, colsAndIndexes.columns, true);\n            }\n            else {\n                // print head for selected columns\n                printHead(doc, table, cursor, colsAndIndexes.columns);\n            }\n            // print body & footer for selected columns\n            printBody(doc, table, startPos, cursor, colsAndIndexes.columns);\n            printFoot(doc, table, cursor, colsAndIndexes.columns);\n        });\n    }\n    else {\n        var lastRowIndexOfLastPage_1 = -1;\n        var firstColumnsToFitResult = allColumnsCanFitResult[0];\n        var _loop_1 = function () {\n            // Print the first columns, taking note of the last row printed\n            var lastPrintedRowIndex = lastRowIndexOfLastPage_1;\n            if (firstColumnsToFitResult) {\n                doc.applyStyles(doc.userStyles);\n                var firstColumnsToFit = firstColumnsToFitResult.columns;\n                if (lastRowIndexOfLastPage_1 >= 0) {\n                    // When adding a page here, make sure not to print the footers\n                    // because they were already printed before on this same loop\n                    addPage(doc, table, startPos, cursor, firstColumnsToFit, true);\n                }\n                else {\n                    printHead(doc, table, cursor, firstColumnsToFit);\n                }\n                lastPrintedRowIndex = printBodyWithoutPageBreaks(doc, table, lastRowIndexOfLastPage_1 + 1, cursor, firstColumnsToFit);\n                printFoot(doc, table, cursor, firstColumnsToFit);\n            }\n            // Check how many rows were printed, so that the next columns would not print more rows than that\n            var maxNumberOfRows = lastPrintedRowIndex - lastRowIndexOfLastPage_1;\n            // Print the next columns, never exceding maxNumberOfRows\n            allColumnsCanFitResult.slice(1).forEach(function (colsAndIndexes) {\n                doc.applyStyles(doc.userStyles);\n                // When adding a page here, make sure not to print the footers\n                // because they were already printed before on this same loop\n                addPage(doc, table, startPos, cursor, colsAndIndexes.columns, true);\n                printBodyWithoutPageBreaks(doc, table, lastRowIndexOfLastPage_1 + 1, cursor, colsAndIndexes.columns, maxNumberOfRows);\n                printFoot(doc, table, cursor, colsAndIndexes.columns);\n            });\n            lastRowIndexOfLastPage_1 = lastPrintedRowIndex;\n        };\n        while (lastRowIndexOfLastPage_1 < table.body.length - 1) {\n            _loop_1();\n        }\n    }\n}\nfunction printHead(doc, table, cursor, columns) {\n    var settings = table.settings;\n    doc.applyStyles(doc.userStyles);\n    if (settings.showHead === 'firstPage' || settings.showHead === 'everyPage') {\n        table.head.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n}\nfunction printBody(doc, table, startPos, cursor, columns) {\n    doc.applyStyles(doc.userStyles);\n    table.body.forEach(function (row, index) {\n        var isLastRow = index === table.body.length - 1;\n        printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n    });\n}\nfunction printBodyWithoutPageBreaks(doc, table, startRowIndex, cursor, columns, maxNumberOfRows) {\n    doc.applyStyles(doc.userStyles);\n    maxNumberOfRows = maxNumberOfRows !== null && maxNumberOfRows !== void 0 ? maxNumberOfRows : table.body.length;\n    var endRowIndex = Math.min(startRowIndex + maxNumberOfRows, table.body.length);\n    var lastPrintedRowIndex = -1;\n    table.body.slice(startRowIndex, endRowIndex).forEach(function (row, index) {\n        var isLastRow = startRowIndex + index === table.body.length - 1;\n        var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\n        if (row.canEntireRowFit(remainingSpace, columns)) {\n            printRow(doc, table, row, cursor, columns);\n            lastPrintedRowIndex = startRowIndex + index;\n        }\n    });\n    return lastPrintedRowIndex;\n}\nfunction printFoot(doc, table, cursor, columns) {\n    var settings = table.settings;\n    doc.applyStyles(doc.userStyles);\n    if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n        table.foot.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n}\nfunction getRemainingLineCount(cell, remainingPageSpace, doc) {\n    var lineHeight = doc.getLineHeight(cell.styles.fontSize);\n    var vPadding = cell.padding('vertical');\n    var remainingLines = Math.floor((remainingPageSpace - vPadding) / lineHeight);\n    return Math.max(0, remainingLines);\n}\nfunction modifyRowToFit(row, remainingPageSpace, table, doc) {\n    var cells = {};\n    row.spansMultiplePages = true;\n    row.height = 0;\n    var rowHeight = 0;\n    for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n        var column = _a[_i];\n        var cell = row.cells[column.index];\n        if (!cell)\n            continue;\n        if (!Array.isArray(cell.text)) {\n            cell.text = [cell.text];\n        }\n        var remainderCell = new Cell(cell.raw, cell.styles, cell.section);\n        remainderCell = assign(remainderCell, cell);\n        remainderCell.text = [];\n        var remainingLineCount = getRemainingLineCount(cell, remainingPageSpace, doc);\n        if (cell.text.length > remainingLineCount) {\n            remainderCell.text = cell.text.splice(remainingLineCount, cell.text.length);\n        }\n        var scaleFactor = doc.scaleFactor();\n        var lineHeightFactor = doc.getLineHeightFactor();\n        cell.contentHeight = cell.getContentHeight(scaleFactor, lineHeightFactor);\n        if (cell.contentHeight >= remainingPageSpace) {\n            cell.contentHeight = remainingPageSpace;\n            remainderCell.styles.minCellHeight -= remainingPageSpace;\n        }\n        if (cell.contentHeight > row.height) {\n            row.height = cell.contentHeight;\n        }\n        remainderCell.contentHeight = remainderCell.getContentHeight(scaleFactor, lineHeightFactor);\n        if (remainderCell.contentHeight > rowHeight) {\n            rowHeight = remainderCell.contentHeight;\n        }\n        cells[column.index] = remainderCell;\n    }\n    var remainderRow = new Row(row.raw, -1, row.section, cells, true);\n    remainderRow.height = rowHeight;\n    for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n        var column = _c[_b];\n        var remainderCell = remainderRow.cells[column.index];\n        if (remainderCell) {\n            remainderCell.height = remainderRow.height;\n        }\n        var cell = row.cells[column.index];\n        if (cell) {\n            cell.height = row.height;\n        }\n    }\n    return remainderRow;\n}\nfunction shouldPrintOnCurrentPage(doc, row, remainingPageSpace, table) {\n    var pageHeight = doc.pageSize().height;\n    var margin = table.settings.margin;\n    var marginHeight = margin.top + margin.bottom;\n    var maxRowHeight = pageHeight - marginHeight;\n    if (row.section === 'body') {\n        // Should also take into account that head and foot is not\n        // on every page with some settings\n        maxRowHeight -=\n            table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n    }\n    var minRowHeight = row.getMinimumRowHeight(table.columns, doc);\n    var minRowFits = minRowHeight < remainingPageSpace;\n    if (minRowHeight > maxRowHeight) {\n        console.error(\"Will not be able to print row \".concat(row.index, \" correctly since it's minimum height is larger than page height\"));\n        return true;\n    }\n    if (!minRowFits) {\n        return false;\n    }\n    var rowHasRowSpanCell = row.hasRowSpan(table.columns);\n    var rowHigherThanPage = row.getMaxCellHeight(table.columns) > maxRowHeight;\n    if (rowHigherThanPage) {\n        if (rowHasRowSpanCell) {\n            console.error(\"The content of row \".concat(row.index, \" will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.\"));\n        }\n        return true;\n    }\n    if (rowHasRowSpanCell) {\n        // Currently a new page is required whenever a rowspan row don't fit a page.\n        return false;\n    }\n    if (table.settings.rowPageBreak === 'avoid') {\n        return false;\n    }\n    // In all other cases print the row on current page\n    return true;\n}\nfunction printFullRow(doc, table, row, isLastRow, startPos, cursor, columns) {\n    var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\n    if (row.canEntireRowFit(remainingSpace, columns)) {\n        // The row fits in the current page\n        printRow(doc, table, row, cursor, columns);\n    }\n    else if (shouldPrintOnCurrentPage(doc, row, remainingSpace, table)) {\n        // The row gets split in two here, each piece in one page\n        var remainderRow = modifyRowToFit(row, remainingSpace, table, doc);\n        printRow(doc, table, row, cursor, columns);\n        addPage(doc, table, startPos, cursor, columns);\n        printFullRow(doc, table, remainderRow, isLastRow, startPos, cursor, columns);\n    }\n    else {\n        // The row get printed entirelly on the next page\n        addPage(doc, table, startPos, cursor, columns);\n        printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n    }\n}\nfunction printRow(doc, table, row, cursor, columns) {\n    cursor.x = table.settings.margin.left;\n    for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n        var column = columns_1[_i];\n        var cell = row.cells[column.index];\n        if (!cell) {\n            cursor.x += column.width;\n            continue;\n        }\n        doc.applyStyles(cell.styles);\n        cell.x = cursor.x;\n        cell.y = cursor.y;\n        var result = table.callCellHooks(doc, table.hooks.willDrawCell, cell, row, column, cursor);\n        if (result === false) {\n            cursor.x += column.width;\n            continue;\n        }\n        drawCellRect(doc, cell, cursor);\n        var textPos = cell.getTextPos();\n        autoTableText(cell.text, textPos.x, textPos.y, {\n            halign: cell.styles.halign,\n            valign: cell.styles.valign,\n            maxWidth: Math.ceil(cell.width - cell.padding('left') - cell.padding('right')),\n        }, doc.getDocument());\n        table.callCellHooks(doc, table.hooks.didDrawCell, cell, row, column, cursor);\n        cursor.x += column.width;\n    }\n    cursor.y += row.height;\n}\nfunction drawCellRect(doc, cell, cursor) {\n    var cellStyles = cell.styles;\n    // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/774\n    // TODO (v4): better solution?\n    doc.getDocument().setFillColor(doc.getDocument().getFillColor());\n    if (typeof cellStyles.lineWidth === 'number') {\n        // Draw cell background with normal borders\n        var fillStyle = getFillStyle(cellStyles.lineWidth, cellStyles.fillColor);\n        if (fillStyle) {\n            doc.rect(cell.x, cursor.y, cell.width, cell.height, fillStyle);\n        }\n    }\n    else if (typeof cellStyles.lineWidth === 'object') {\n        // Draw cell background\n        if (cellStyles.fillColor) {\n            doc.rect(cell.x, cursor.y, cell.width, cell.height, 'F');\n        }\n        // Draw cell individual borders\n        drawCellBorders(doc, cell, cursor, cellStyles.lineWidth);\n    }\n}\n/**\n * Draw all specified borders. Borders are centered on cell's edge and lengthened\n * to overlap with neighbours to create sharp corners.\n * @param doc\n * @param cell\n * @param cursor\n * @param fillColor\n * @param lineWidth\n */\nfunction drawCellBorders(doc, cell, cursor, lineWidth) {\n    var x1, y1, x2, y2;\n    if (lineWidth.top) {\n        x1 = cursor.x;\n        y1 = cursor.y;\n        x2 = cursor.x + cell.width;\n        y2 = cursor.y;\n        if (lineWidth.right) {\n            x2 += 0.5 * lineWidth.right;\n        }\n        if (lineWidth.left) {\n            x1 -= 0.5 * lineWidth.left;\n        }\n        drawLine(lineWidth.top, x1, y1, x2, y2);\n    }\n    if (lineWidth.bottom) {\n        x1 = cursor.x;\n        y1 = cursor.y + cell.height;\n        x2 = cursor.x + cell.width;\n        y2 = cursor.y + cell.height;\n        if (lineWidth.right) {\n            x2 += 0.5 * lineWidth.right;\n        }\n        if (lineWidth.left) {\n            x1 -= 0.5 * lineWidth.left;\n        }\n        drawLine(lineWidth.bottom, x1, y1, x2, y2);\n    }\n    if (lineWidth.left) {\n        x1 = cursor.x;\n        y1 = cursor.y;\n        x2 = cursor.x;\n        y2 = cursor.y + cell.height;\n        if (lineWidth.top) {\n            y1 -= 0.5 * lineWidth.top;\n        }\n        if (lineWidth.bottom) {\n            y2 += 0.5 * lineWidth.bottom;\n        }\n        drawLine(lineWidth.left, x1, y1, x2, y2);\n    }\n    if (lineWidth.right) {\n        x1 = cursor.x + cell.width;\n        y1 = cursor.y;\n        x2 = cursor.x + cell.width;\n        y2 = cursor.y + cell.height;\n        if (lineWidth.top) {\n            y1 -= 0.5 * lineWidth.top;\n        }\n        if (lineWidth.bottom) {\n            y2 += 0.5 * lineWidth.bottom;\n        }\n        drawLine(lineWidth.right, x1, y1, x2, y2);\n    }\n    function drawLine(width, x1, y1, x2, y2) {\n        doc.getDocument().setLineWidth(width);\n        doc.getDocument().line(x1, y1, x2, y2, 'S');\n    }\n}\nfunction getRemainingPageSpace(doc, table, isLastRow, cursor) {\n    var bottomContentHeight = table.settings.margin.bottom;\n    var showFoot = table.settings.showFoot;\n    if (showFoot === 'everyPage' || (showFoot === 'lastPage' && isLastRow)) {\n        bottomContentHeight += table.getFootHeight(table.columns);\n    }\n    return doc.pageSize().height - cursor.y - bottomContentHeight;\n}\nfunction addPage(doc, table, startPos, cursor, columns, suppressFooter) {\n    if (columns === void 0) { columns = []; }\n    if (suppressFooter === void 0) { suppressFooter = false; }\n    doc.applyStyles(doc.userStyles);\n    if (table.settings.showFoot === 'everyPage' && !suppressFooter) {\n        table.foot.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n    // Add user content just before adding new page ensure it will\n    // be drawn above other things on the page\n    table.callEndPageHooks(doc, cursor);\n    var margin = table.settings.margin;\n    addTableBorder(doc, table, startPos, cursor);\n    nextPage(doc);\n    table.pageNumber++;\n    cursor.x = margin.left;\n    cursor.y = margin.top;\n    startPos.y = margin.top;\n    // call didAddPage hooks before any content is added to the page\n    table.callWillDrawPageHooks(doc, cursor);\n    if (table.settings.showHead === 'everyPage') {\n        table.head.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n        doc.applyStyles(doc.userStyles);\n    }\n}\nfunction nextPage(doc) {\n    var current = doc.pageNumber();\n    doc.setPage(current + 1);\n    var newCurrent = doc.pageNumber();\n    if (newCurrent === current) {\n        doc.addPage();\n        return true;\n    }\n    return false;\n}\n\nfunction applyPlugin(jsPDF) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    jsPDF.API.autoTable = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        var options = args[0];\n        var input = parseInput(this, options);\n        var table = createTable(this, input);\n        drawTable(this, table);\n        return this;\n    };\n    // Assign false to enable `doc.lastAutoTable.finalY || 40` sugar\n    jsPDF.API.lastAutoTable = false;\n    jsPDF.API.autoTableText = function (text, x, y, styles) {\n        autoTableText(text, x, y, styles, this);\n    };\n    jsPDF.API.autoTableSetDefaults = function (defaults) {\n        DocHandler.setDefaults(defaults, this);\n        return this;\n    };\n    jsPDF.autoTableSetDefaults = function (defaults, doc) {\n        DocHandler.setDefaults(defaults, doc);\n    };\n    jsPDF.API.autoTableHtmlToJson = function (tableElem, includeHiddenElements) {\n        var _a;\n        if (includeHiddenElements === void 0) { includeHiddenElements = false; }\n        if (typeof window === 'undefined') {\n            console.error('Cannot run autoTableHtmlToJson in non browser environment');\n            return null;\n        }\n        var doc = new DocHandler(this);\n        var _b = parseHtml(doc, tableElem, window, includeHiddenElements, false), head = _b.head, body = _b.body;\n        var columns = ((_a = head[0]) === null || _a === void 0 ? void 0 : _a.map(function (c) { return c.content; })) || [];\n        return { columns: columns, rows: body, data: body };\n    };\n}\n\nvar _a;\nfunction autoTable(d, options) {\n    var input = parseInput(d, options);\n    var table = createTable(d, input);\n    drawTable(d, table);\n}\n// Experimental export\nfunction __createTable(d, options) {\n    var input = parseInput(d, options);\n    return createTable(d, input);\n}\nfunction __drawTable(d, table) {\n    drawTable(d, table);\n}\ntry {\n    if (typeof window !== 'undefined' && window) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        var anyWindow = window;\n        var jsPDF = anyWindow.jsPDF || ((_a = anyWindow.jspdf) === null || _a === void 0 ? void 0 : _a.jsPDF);\n        if (jsPDF) {\n            applyPlugin(jsPDF);\n        }\n    }\n}\ncatch (error) {\n    console.error('Could not apply autoTable plugin', error);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.mjs\n");

/***/ })

};
;