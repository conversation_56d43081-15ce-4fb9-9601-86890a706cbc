"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/plyr";
exports.ids = ["vendor-chunks/plyr"];
exports.modules = {

/***/ "(ssr)/./node_modules/plyr/dist/plyr.css":
/*!*****************************************!*\
  !*** ./node_modules/plyr/dist/plyr.css ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"3e9561fb7a2a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGx5ci9kaXN0L3BseXIuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxccm91dGVcXNmF2YbYtdipXFzYsdmB2LlcXDRcXG1hbmFzYVxcbm9kZV9tb2R1bGVzXFxwbHlyXFxkaXN0XFxwbHlyLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjNlOTU2MWZiN2EyYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/plyr/dist/plyr.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/plyr/dist/plyr.min.mjs":
/*!*********************************************!*\
  !*** ./node_modules/plyr/dist/plyr.min.mjs ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Plyr)\n/* harmony export */ });\nfunction _defineProperty$1(e,t,i){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function _toPrimitive(e,t){if(\"object\"!=typeof e||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var s=i.call(e,t||\"default\");if(\"object\"!=typeof s)return s;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===t?String:Number)(e)}function _toPropertyKey(e){var t=_toPrimitive(e,\"string\");return\"symbol\"==typeof t?t:String(t)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError(\"Cannot call a class as a function\")}function _defineProperties(e,t){for(var i=0;i<t.length;i++){var s=t[i];s.enumerable=s.enumerable||!1,s.configurable=!0,\"value\"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}function _createClass(e,t,i){return t&&_defineProperties(e.prototype,t),i&&_defineProperties(e,i),e}function _defineProperty(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function ownKeys(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,s)}return i}function _objectSpread2(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(i),!0).forEach((function(t){_defineProperty(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):ownKeys(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}var defaults$1={addCSS:!0,thumbWidth:15,watch:!0};function matches$1(e,t){return function(){return Array.from(document.querySelectorAll(t)).includes(this)}.call(e,t)}function trigger(e,t){if(e&&t){var i=new Event(t,{bubbles:!0});e.dispatchEvent(i)}}var getConstructor$1=function(e){return null!=e?e.constructor:null},instanceOf$1=function(e,t){return!!(e&&t&&e instanceof t)},isNullOrUndefined$1=function(e){return null==e},isObject$1=function(e){return getConstructor$1(e)===Object},isNumber$1=function(e){return getConstructor$1(e)===Number&&!Number.isNaN(e)},isString$1=function(e){return getConstructor$1(e)===String},isBoolean$1=function(e){return getConstructor$1(e)===Boolean},isFunction$1=function(e){return getConstructor$1(e)===Function},isArray$1=function(e){return Array.isArray(e)},isNodeList$1=function(e){return instanceOf$1(e,NodeList)},isElement$1=function(e){return instanceOf$1(e,Element)},isEvent$1=function(e){return instanceOf$1(e,Event)},isEmpty$1=function(e){return isNullOrUndefined$1(e)||(isString$1(e)||isArray$1(e)||isNodeList$1(e))&&!e.length||isObject$1(e)&&!Object.keys(e).length},is$1={nullOrUndefined:isNullOrUndefined$1,object:isObject$1,number:isNumber$1,string:isString$1,boolean:isBoolean$1,function:isFunction$1,array:isArray$1,nodeList:isNodeList$1,element:isElement$1,event:isEvent$1,empty:isEmpty$1};function getDecimalPlaces(e){var t=\"\".concat(e).match(/(?:\\.(\\d+))?(?:[eE]([+-]?\\d+))?$/);return t?Math.max(0,(t[1]?t[1].length:0)-(t[2]?+t[2]:0)):0}function round(e,t){if(1>t){var i=getDecimalPlaces(t);return parseFloat(e.toFixed(i))}return Math.round(e/t)*t}var RangeTouch=function(){function e(t,i){_classCallCheck(this,e),is$1.element(t)?this.element=t:is$1.string(t)&&(this.element=document.querySelector(t)),is$1.element(this.element)&&is$1.empty(this.element.rangeTouch)&&(this.config=_objectSpread2({},defaults$1,{},i),this.init())}return _createClass(e,[{key:\"init\",value:function(){e.enabled&&(this.config.addCSS&&(this.element.style.userSelect=\"none\",this.element.style.webKitUserSelect=\"none\",this.element.style.touchAction=\"manipulation\"),this.listeners(!0),this.element.rangeTouch=this)}},{key:\"destroy\",value:function(){e.enabled&&(this.config.addCSS&&(this.element.style.userSelect=\"\",this.element.style.webKitUserSelect=\"\",this.element.style.touchAction=\"\"),this.listeners(!1),this.element.rangeTouch=null)}},{key:\"listeners\",value:function(e){var t=this,i=e?\"addEventListener\":\"removeEventListener\";[\"touchstart\",\"touchmove\",\"touchend\"].forEach((function(e){t.element[i](e,(function(e){return t.set(e)}),!1)}))}},{key:\"get\",value:function(t){if(!e.enabled||!is$1.event(t))return null;var i,s=t.target,n=t.changedTouches[0],r=parseFloat(s.getAttribute(\"min\"))||0,a=parseFloat(s.getAttribute(\"max\"))||100,o=parseFloat(s.getAttribute(\"step\"))||1,l=s.getBoundingClientRect(),c=100/l.width*(this.config.thumbWidth/2)/100;return 0>(i=100/l.width*(n.clientX-l.left))?i=0:100<i&&(i=100),50>i?i-=(100-2*i)*c:50<i&&(i+=2*(i-50)*c),r+round(i/100*(a-r),o)}},{key:\"set\",value:function(t){e.enabled&&is$1.event(t)&&!t.target.disabled&&(t.preventDefault(),t.target.value=this.get(t),trigger(t.target,\"touchend\"===t.type?\"change\":\"input\"))}}],[{key:\"setup\",value:function(t){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},s=null;if(is$1.empty(t)||is$1.string(t)?s=Array.from(document.querySelectorAll(is$1.string(t)?t:'input[type=\"range\"]')):is$1.element(t)?s=[t]:is$1.nodeList(t)?s=Array.from(t):is$1.array(t)&&(s=t.filter(is$1.element)),is$1.empty(s))return null;var n=_objectSpread2({},defaults$1,{},i);if(is$1.string(t)&&n.watch){var r=new MutationObserver((function(i){Array.from(i).forEach((function(i){Array.from(i.addedNodes).forEach((function(i){is$1.element(i)&&matches$1(i,t)&&new e(i,n)}))}))}));r.observe(document.body,{childList:!0,subtree:!0})}return s.map((function(t){return new e(t,i)}))}},{key:\"enabled\",get:function(){return\"ontouchstart\"in document.documentElement}}]),e}();const getConstructor=e=>null!=e?e.constructor:null,instanceOf=(e,t)=>Boolean(e&&t&&e instanceof t),isNullOrUndefined=e=>null==e,isObject=e=>getConstructor(e)===Object,isNumber=e=>getConstructor(e)===Number&&!Number.isNaN(e),isString=e=>getConstructor(e)===String,isBoolean=e=>getConstructor(e)===Boolean,isFunction=e=>\"function\"==typeof e,isArray=e=>Array.isArray(e),isWeakMap=e=>instanceOf(e,WeakMap),isNodeList=e=>instanceOf(e,NodeList),isTextNode=e=>getConstructor(e)===Text,isEvent=e=>instanceOf(e,Event),isKeyboardEvent=e=>instanceOf(e,KeyboardEvent),isCue=e=>instanceOf(e,window.TextTrackCue)||instanceOf(e,window.VTTCue),isTrack=e=>instanceOf(e,TextTrack)||!isNullOrUndefined(e)&&isString(e.kind),isPromise=e=>instanceOf(e,Promise)&&isFunction(e.then),isElement=e=>null!==e&&\"object\"==typeof e&&1===e.nodeType&&\"object\"==typeof e.style&&\"object\"==typeof e.ownerDocument,isEmpty=e=>isNullOrUndefined(e)||(isString(e)||isArray(e)||isNodeList(e))&&!e.length||isObject(e)&&!Object.keys(e).length,isUrl=e=>{if(instanceOf(e,window.URL))return!0;if(!isString(e))return!1;let t=e;e.startsWith(\"http://\")&&e.startsWith(\"https://\")||(t=`http://${e}`);try{return!isEmpty(new URL(t).hostname)}catch(e){return!1}};var is={nullOrUndefined:isNullOrUndefined,object:isObject,number:isNumber,string:isString,boolean:isBoolean,function:isFunction,array:isArray,weakMap:isWeakMap,nodeList:isNodeList,element:isElement,textNode:isTextNode,event:isEvent,keyboardEvent:isKeyboardEvent,cue:isCue,track:isTrack,promise:isPromise,url:isUrl,empty:isEmpty};const transitionEndEvent=(()=>{const e=document.createElement(\"span\"),t={WebkitTransition:\"webkitTransitionEnd\",MozTransition:\"transitionend\",OTransition:\"oTransitionEnd otransitionend\",transition:\"transitionend\"},i=Object.keys(t).find((t=>void 0!==e.style[t]));return!!is.string(i)&&t[i]})();function repaint(e,t){setTimeout((()=>{try{e.hidden=!0,e.offsetHeight,e.hidden=!1}catch(e){}}),t)}const isIE=Boolean(window.document.documentMode),isEdge=/Edge/g.test(navigator.userAgent),isWebKit=\"WebkitAppearance\"in document.documentElement.style&&!/Edge/g.test(navigator.userAgent),isIPhone=/iPhone|iPod/gi.test(navigator.userAgent)&&navigator.maxTouchPoints>1,isIPadOS=\"MacIntel\"===navigator.platform&&navigator.maxTouchPoints>1,isIos=/iPad|iPhone|iPod/gi.test(navigator.userAgent)&&navigator.maxTouchPoints>1;var browser={isIE:isIE,isEdge:isEdge,isWebKit:isWebKit,isIPhone:isIPhone,isIPadOS:isIPadOS,isIos:isIos};function cloneDeep(e){return JSON.parse(JSON.stringify(e))}function getDeep(e,t){return t.split(\".\").reduce(((e,t)=>e&&e[t]),e)}function extend(e={},...t){if(!t.length)return e;const i=t.shift();return is.object(i)?(Object.keys(i).forEach((t=>{is.object(i[t])?(Object.keys(e).includes(t)||Object.assign(e,{[t]:{}}),extend(e[t],i[t])):Object.assign(e,{[t]:i[t]})})),extend(e,...t)):e}function wrap(e,t){const i=e.length?e:[e];Array.from(i).reverse().forEach(((e,i)=>{const s=i>0?t.cloneNode(!0):t,n=e.parentNode,r=e.nextSibling;s.appendChild(e),r?n.insertBefore(s,r):n.appendChild(s)}))}function setAttributes(e,t){is.element(e)&&!is.empty(t)&&Object.entries(t).filter((([,e])=>!is.nullOrUndefined(e))).forEach((([t,i])=>e.setAttribute(t,i)))}function createElement(e,t,i){const s=document.createElement(e);return is.object(t)&&setAttributes(s,t),is.string(i)&&(s.innerText=i),s}function insertAfter(e,t){is.element(e)&&is.element(t)&&t.parentNode.insertBefore(e,t.nextSibling)}function insertElement(e,t,i,s){is.element(t)&&t.appendChild(createElement(e,i,s))}function removeElement(e){is.nodeList(e)||is.array(e)?Array.from(e).forEach(removeElement):is.element(e)&&is.element(e.parentNode)&&e.parentNode.removeChild(e)}function emptyElement(e){if(!is.element(e))return;let{length:t}=e.childNodes;for(;t>0;)e.removeChild(e.lastChild),t-=1}function replaceElement(e,t){return is.element(t)&&is.element(t.parentNode)&&is.element(e)?(t.parentNode.replaceChild(e,t),e):null}function getAttributesFromSelector(e,t){if(!is.string(e)||is.empty(e))return{};const i={},s=extend({},t);return e.split(\",\").forEach((e=>{const t=e.trim(),n=t.replace(\".\",\"\"),r=t.replace(/[[\\]]/g,\"\").split(\"=\"),[a]=r,o=r.length>1?r[1].replace(/[\"']/g,\"\"):\"\";switch(t.charAt(0)){case\".\":is.string(s.class)?i.class=`${s.class} ${n}`:i.class=n;break;case\"#\":i.id=t.replace(\"#\",\"\");break;case\"[\":i[a]=o}})),extend(s,i)}function toggleHidden(e,t){if(!is.element(e))return;let i=t;is.boolean(i)||(i=!e.hidden),e.hidden=i}function toggleClass(e,t,i){if(is.nodeList(e))return Array.from(e).map((e=>toggleClass(e,t,i)));if(is.element(e)){let s=\"toggle\";return void 0!==i&&(s=i?\"add\":\"remove\"),e.classList[s](t),e.classList.contains(t)}return!1}function hasClass(e,t){return is.element(e)&&e.classList.contains(t)}function matches(e,t){const{prototype:i}=Element;return(i.matches||i.webkitMatchesSelector||i.mozMatchesSelector||i.msMatchesSelector||function(){return Array.from(document.querySelectorAll(t)).includes(this)}).call(e,t)}function closest$1(e,t){const{prototype:i}=Element;return(i.closest||function(){let e=this;do{if(matches.matches(e,t))return e;e=e.parentElement||e.parentNode}while(null!==e&&1===e.nodeType);return null}).call(e,t)}function getElements(e){return this.elements.container.querySelectorAll(e)}function getElement(e){return this.elements.container.querySelector(e)}function setFocus(e=null,t=!1){is.element(e)&&e.focus({preventScroll:!0,focusVisible:t})}const defaultCodecs={\"audio/ogg\":\"vorbis\",\"audio/wav\":\"1\",\"video/webm\":\"vp8, vorbis\",\"video/mp4\":\"avc1.42E01E, mp4a.40.2\",\"video/ogg\":\"theora\"},support={audio:\"canPlayType\"in document.createElement(\"audio\"),video:\"canPlayType\"in document.createElement(\"video\"),check(e,t){const i=support[e]||\"html5\"!==t;return{api:i,ui:i&&support.rangeInput}},pip:!(browser.isIPhone||!is.function(createElement(\"video\").webkitSetPresentationMode)&&(!document.pictureInPictureEnabled||createElement(\"video\").disablePictureInPicture)),airplay:is.function(window.WebKitPlaybackTargetAvailabilityEvent),playsinline:\"playsInline\"in document.createElement(\"video\"),mime(e){if(is.empty(e))return!1;const[t]=e.split(\"/\");let i=e;if(!this.isHTML5||t!==this.type)return!1;Object.keys(defaultCodecs).includes(i)&&(i+=`; codecs=\"${defaultCodecs[e]}\"`);try{return Boolean(i&&this.media.canPlayType(i).replace(/no/,\"\"))}catch(e){return!1}},textTracks:\"textTracks\"in document.createElement(\"video\"),rangeInput:(()=>{const e=document.createElement(\"input\");return e.type=\"range\",\"range\"===e.type})(),touch:\"ontouchstart\"in document.documentElement,transitions:!1!==transitionEndEvent,reducedMotion:\"matchMedia\"in window&&window.matchMedia(\"(prefers-reduced-motion)\").matches},supportsPassiveListeners=(()=>{let e=!1;try{const t=Object.defineProperty({},\"passive\",{get:()=>(e=!0,null)});window.addEventListener(\"test\",null,t),window.removeEventListener(\"test\",null,t)}catch(e){}return e})();function toggleListener(e,t,i,s=!1,n=!0,r=!1){if(!e||!(\"addEventListener\"in e)||is.empty(t)||!is.function(i))return;const a=t.split(\" \");let o=r;supportsPassiveListeners&&(o={passive:n,capture:r}),a.forEach((t=>{this&&this.eventListeners&&s&&this.eventListeners.push({element:e,type:t,callback:i,options:o}),e[s?\"addEventListener\":\"removeEventListener\"](t,i,o)}))}function on(e,t=\"\",i,s=!0,n=!1){toggleListener.call(this,e,t,i,!0,s,n)}function off(e,t=\"\",i,s=!0,n=!1){toggleListener.call(this,e,t,i,!1,s,n)}function once(e,t=\"\",i,s=!0,n=!1){const r=(...a)=>{off(e,t,r,s,n),i.apply(this,a)};toggleListener.call(this,e,t,r,!0,s,n)}function triggerEvent(e,t=\"\",i=!1,s={}){if(!is.element(e)||is.empty(t))return;const n=new CustomEvent(t,{bubbles:i,detail:{...s,plyr:this}});e.dispatchEvent(n)}function unbindListeners(){this&&this.eventListeners&&(this.eventListeners.forEach((e=>{const{element:t,type:i,callback:s,options:n}=e;t.removeEventListener(i,s,n)})),this.eventListeners=[])}function ready(){return new Promise((e=>this.ready?setTimeout(e,0):on.call(this,this.elements.container,\"ready\",e))).then((()=>{}))}function silencePromise(e){is.promise(e)&&e.then(null,(()=>{}))}function dedupe(e){return is.array(e)?e.filter(((t,i)=>e.indexOf(t)===i)):e}function closest(e,t){return is.array(e)&&e.length?e.reduce(((e,i)=>Math.abs(i-t)<Math.abs(e-t)?i:e)):null}function supportsCSS(e){return!(!window||!window.CSS)&&window.CSS.supports(e)}const standardRatios=[[1,1],[4,3],[3,4],[5,4],[4,5],[3,2],[2,3],[16,10],[10,16],[16,9],[9,16],[21,9],[9,21],[32,9],[9,32]].reduce(((e,[t,i])=>({...e,[t/i]:[t,i]})),{});function validateAspectRatio(e){if(!(is.array(e)||is.string(e)&&e.includes(\":\")))return!1;return(is.array(e)?e:e.split(\":\")).map(Number).every(is.number)}function reduceAspectRatio(e){if(!is.array(e)||!e.every(is.number))return null;const[t,i]=e,s=(e,t)=>0===t?e:s(t,e%t),n=s(t,i);return[t/n,i/n]}function getAspectRatio(e){const t=e=>validateAspectRatio(e)?e.split(\":\").map(Number):null;let i=t(e);if(null===i&&(i=t(this.config.ratio)),null===i&&!is.empty(this.embed)&&is.array(this.embed.ratio)&&({ratio:i}=this.embed),null===i&&this.isHTML5){const{videoWidth:e,videoHeight:t}=this.media;i=[e,t]}return reduceAspectRatio(i)}function setAspectRatio(e){if(!this.isVideo)return{};const{wrapper:t}=this.elements,i=getAspectRatio.call(this,e);if(!is.array(i))return{};const[s,n]=reduceAspectRatio(i),r=100/s*n;if(supportsCSS(`aspect-ratio: ${s}/${n}`)?t.style.aspectRatio=`${s}/${n}`:t.style.paddingBottom=`${r}%`,this.isVimeo&&!this.config.vimeo.premium&&this.supported.ui){const e=100/this.media.offsetWidth*parseInt(window.getComputedStyle(this.media).paddingBottom,10),i=(e-r)/(e/50);this.fullscreen.active?t.style.paddingBottom=null:this.media.style.transform=`translateY(-${i}%)`}else this.isHTML5&&t.classList.add(this.config.classNames.videoFixedRatio);return{padding:r,ratio:i}}function roundAspectRatio(e,t,i=.05){const s=e/t,n=closest(Object.keys(standardRatios),s);return Math.abs(n-s)<=i?standardRatios[n]:[e,t]}function getViewportSize(){return[Math.max(document.documentElement.clientWidth||0,window.innerWidth||0),Math.max(document.documentElement.clientHeight||0,window.innerHeight||0)]}const html5={getSources(){if(!this.isHTML5)return[];return Array.from(this.media.querySelectorAll(\"source\")).filter((e=>{const t=e.getAttribute(\"type\");return!!is.empty(t)||support.mime.call(this,t)}))},getQualityOptions(){return this.config.quality.forced?this.config.quality.options:html5.getSources.call(this).map((e=>Number(e.getAttribute(\"size\")))).filter(Boolean)},setup(){if(!this.isHTML5)return;const e=this;e.options.speed=e.config.speed.options,is.empty(this.config.ratio)||setAspectRatio.call(e),Object.defineProperty(e.media,\"quality\",{get(){const t=html5.getSources.call(e).find((t=>t.getAttribute(\"src\")===e.source));return t&&Number(t.getAttribute(\"size\"))},set(t){if(e.quality!==t){if(e.config.quality.forced&&is.function(e.config.quality.onChange))e.config.quality.onChange(t);else{const i=html5.getSources.call(e).find((e=>Number(e.getAttribute(\"size\"))===t));if(!i)return;const{currentTime:s,paused:n,preload:r,readyState:a,playbackRate:o}=e.media;e.media.src=i.getAttribute(\"src\"),(\"none\"!==r||a)&&(e.once(\"loadedmetadata\",(()=>{e.speed=o,e.currentTime=s,n||silencePromise(e.play())})),e.media.load())}triggerEvent.call(e,e.media,\"qualitychange\",!1,{quality:t})}}})},cancelRequests(){this.isHTML5&&(removeElement(html5.getSources.call(this)),this.media.setAttribute(\"src\",this.config.blankVideo),this.media.load(),this.debug.log(\"Cancelled network requests\"))}};function generateId(e){return`${e}-${Math.floor(1e4*Math.random())}`}function format(e,...t){return is.empty(e)?e:e.toString().replace(/{(\\d+)}/g,((e,i)=>t[i].toString()))}function getPercentage(e,t){return 0===e||0===t||Number.isNaN(e)||Number.isNaN(t)?0:(e/t*100).toFixed(2)}const replaceAll=(e=\"\",t=\"\",i=\"\")=>e.replace(new RegExp(t.toString().replace(/([.*+?^=!:${}()|[\\]/\\\\])/g,\"\\\\$1\"),\"g\"),i.toString()),toTitleCase=(e=\"\")=>e.toString().replace(/\\w\\S*/g,(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()));function toPascalCase(e=\"\"){let t=e.toString();return t=replaceAll(t,\"-\",\" \"),t=replaceAll(t,\"_\",\" \"),t=toTitleCase(t),replaceAll(t,\" \",\"\")}function toCamelCase(e=\"\"){let t=e.toString();return t=toPascalCase(t),t.charAt(0).toLowerCase()+t.slice(1)}function stripHTML(e){const t=document.createDocumentFragment(),i=document.createElement(\"div\");return t.appendChild(i),i.innerHTML=e,t.firstChild.innerText}function getHTML(e){const t=document.createElement(\"div\");return t.appendChild(e),t.innerHTML}const resources={pip:\"PIP\",airplay:\"AirPlay\",html5:\"HTML5\",vimeo:\"Vimeo\",youtube:\"YouTube\"},i18n={get(e=\"\",t={}){if(is.empty(e)||is.empty(t))return\"\";let i=getDeep(t.i18n,e);if(is.empty(i))return Object.keys(resources).includes(e)?resources[e]:\"\";const s={\"{seektime}\":t.seekTime,\"{title}\":t.title};return Object.entries(s).forEach((([e,t])=>{i=replaceAll(i,e,t)})),i}};class Storage{constructor(e){_defineProperty$1(this,\"get\",(e=>{if(!Storage.supported||!this.enabled)return null;const t=window.localStorage.getItem(this.key);if(is.empty(t))return null;const i=JSON.parse(t);return is.string(e)&&e.length?i[e]:i})),_defineProperty$1(this,\"set\",(e=>{if(!Storage.supported||!this.enabled)return;if(!is.object(e))return;let t=this.get();is.empty(t)&&(t={}),extend(t,e);try{window.localStorage.setItem(this.key,JSON.stringify(t))}catch(e){}})),this.enabled=e.config.storage.enabled,this.key=e.config.storage.key}static get supported(){try{if(!(\"localStorage\"in window))return!1;const e=\"___test\";return window.localStorage.setItem(e,e),window.localStorage.removeItem(e),!0}catch(e){return!1}}}function fetch(e,t=\"text\"){return new Promise(((i,s)=>{try{const s=new XMLHttpRequest;if(!(\"withCredentials\"in s))return;s.addEventListener(\"load\",(()=>{if(\"text\"===t)try{i(JSON.parse(s.responseText))}catch(e){i(s.responseText)}else i(s.response)})),s.addEventListener(\"error\",(()=>{throw new Error(s.status)})),s.open(\"GET\",e,!0),s.responseType=t,s.send()}catch(e){s(e)}}))}function loadSprite(e,t){if(!is.string(e))return;const i=\"cache\",s=is.string(t);let n=!1;const r=()=>null!==document.getElementById(t),a=(e,t)=>{e.innerHTML=t,s&&r()||document.body.insertAdjacentElement(\"afterbegin\",e)};if(!s||!r()){const r=Storage.supported,o=document.createElement(\"div\");if(o.setAttribute(\"hidden\",\"\"),s&&o.setAttribute(\"id\",t),r){const e=window.localStorage.getItem(`${i}-${t}`);if(n=null!==e,n){const t=JSON.parse(e);a(o,t.content)}}fetch(e).then((e=>{if(!is.empty(e)){if(r)try{window.localStorage.setItem(`${i}-${t}`,JSON.stringify({content:e}))}catch(e){}a(o,e)}})).catch((()=>{}))}}const getHours=e=>Math.trunc(e/60/60%60,10),getMinutes=e=>Math.trunc(e/60%60,10),getSeconds=e=>Math.trunc(e%60,10);function formatTime(e=0,t=!1,i=!1){if(!is.number(e))return formatTime(void 0,t,i);const s=e=>`0${e}`.slice(-2);let n=getHours(e);const r=getMinutes(e),a=getSeconds(e);return n=t||n>0?`${n}:`:\"\",`${i&&e>0?\"-\":\"\"}${n}${s(r)}:${s(a)}`}const controls={getIconUrl(){const e=new URL(this.config.iconUrl,window.location),t=window.location.host?window.location.host:window.top.location.host,i=e.host!==t||browser.isIE&&!window.svg4everybody;return{url:this.config.iconUrl,cors:i}},findElements(){try{return this.elements.controls=getElement.call(this,this.config.selectors.controls.wrapper),this.elements.buttons={play:getElements.call(this,this.config.selectors.buttons.play),pause:getElement.call(this,this.config.selectors.buttons.pause),restart:getElement.call(this,this.config.selectors.buttons.restart),rewind:getElement.call(this,this.config.selectors.buttons.rewind),fastForward:getElement.call(this,this.config.selectors.buttons.fastForward),mute:getElement.call(this,this.config.selectors.buttons.mute),pip:getElement.call(this,this.config.selectors.buttons.pip),airplay:getElement.call(this,this.config.selectors.buttons.airplay),settings:getElement.call(this,this.config.selectors.buttons.settings),captions:getElement.call(this,this.config.selectors.buttons.captions),fullscreen:getElement.call(this,this.config.selectors.buttons.fullscreen)},this.elements.progress=getElement.call(this,this.config.selectors.progress),this.elements.inputs={seek:getElement.call(this,this.config.selectors.inputs.seek),volume:getElement.call(this,this.config.selectors.inputs.volume)},this.elements.display={buffer:getElement.call(this,this.config.selectors.display.buffer),currentTime:getElement.call(this,this.config.selectors.display.currentTime),duration:getElement.call(this,this.config.selectors.display.duration)},is.element(this.elements.progress)&&(this.elements.display.seekTooltip=this.elements.progress.querySelector(`.${this.config.classNames.tooltip}`)),!0}catch(e){return this.debug.warn(\"It looks like there is a problem with your custom controls HTML\",e),this.toggleNativeControls(!0),!1}},createIcon(e,t){const i=\"http://www.w3.org/2000/svg\",s=controls.getIconUrl.call(this),n=`${s.cors?\"\":s.url}#${this.config.iconPrefix}`,r=document.createElementNS(i,\"svg\");setAttributes(r,extend(t,{\"aria-hidden\":\"true\",focusable:\"false\"}));const a=document.createElementNS(i,\"use\"),o=`${n}-${e}`;return\"href\"in a&&a.setAttributeNS(\"http://www.w3.org/1999/xlink\",\"href\",o),a.setAttributeNS(\"http://www.w3.org/1999/xlink\",\"xlink:href\",o),r.appendChild(a),r},createLabel(e,t={}){const i=i18n.get(e,this.config);return createElement(\"span\",{...t,class:[t.class,this.config.classNames.hidden].filter(Boolean).join(\" \")},i)},createBadge(e){if(is.empty(e))return null;const t=createElement(\"span\",{class:this.config.classNames.menu.value});return t.appendChild(createElement(\"span\",{class:this.config.classNames.menu.badge},e)),t},createButton(e,t){const i=extend({},t);let s=toCamelCase(e);const n={element:\"button\",toggle:!1,label:null,icon:null,labelPressed:null,iconPressed:null};switch([\"element\",\"icon\",\"label\"].forEach((e=>{Object.keys(i).includes(e)&&(n[e]=i[e],delete i[e])})),\"button\"!==n.element||Object.keys(i).includes(\"type\")||(i.type=\"button\"),Object.keys(i).includes(\"class\")?i.class.split(\" \").some((e=>e===this.config.classNames.control))||extend(i,{class:`${i.class} ${this.config.classNames.control}`}):i.class=this.config.classNames.control,e){case\"play\":n.toggle=!0,n.label=\"play\",n.labelPressed=\"pause\",n.icon=\"play\",n.iconPressed=\"pause\";break;case\"mute\":n.toggle=!0,n.label=\"mute\",n.labelPressed=\"unmute\",n.icon=\"volume\",n.iconPressed=\"muted\";break;case\"captions\":n.toggle=!0,n.label=\"enableCaptions\",n.labelPressed=\"disableCaptions\",n.icon=\"captions-off\",n.iconPressed=\"captions-on\";break;case\"fullscreen\":n.toggle=!0,n.label=\"enterFullscreen\",n.labelPressed=\"exitFullscreen\",n.icon=\"enter-fullscreen\",n.iconPressed=\"exit-fullscreen\";break;case\"play-large\":i.class+=` ${this.config.classNames.control}--overlaid`,s=\"play\",n.label=\"play\",n.icon=\"play\";break;default:is.empty(n.label)&&(n.label=s),is.empty(n.icon)&&(n.icon=e)}const r=createElement(n.element);return n.toggle?(r.appendChild(controls.createIcon.call(this,n.iconPressed,{class:\"icon--pressed\"})),r.appendChild(controls.createIcon.call(this,n.icon,{class:\"icon--not-pressed\"})),r.appendChild(controls.createLabel.call(this,n.labelPressed,{class:\"label--pressed\"})),r.appendChild(controls.createLabel.call(this,n.label,{class:\"label--not-pressed\"}))):(r.appendChild(controls.createIcon.call(this,n.icon)),r.appendChild(controls.createLabel.call(this,n.label))),extend(i,getAttributesFromSelector(this.config.selectors.buttons[s],i)),setAttributes(r,i),\"play\"===s?(is.array(this.elements.buttons[s])||(this.elements.buttons[s]=[]),this.elements.buttons[s].push(r)):this.elements.buttons[s]=r,r},createRange(e,t){const i=createElement(\"input\",extend(getAttributesFromSelector(this.config.selectors.inputs[e]),{type:\"range\",min:0,max:100,step:.01,value:0,autocomplete:\"off\",role:\"slider\",\"aria-label\":i18n.get(e,this.config),\"aria-valuemin\":0,\"aria-valuemax\":100,\"aria-valuenow\":0},t));return this.elements.inputs[e]=i,controls.updateRangeFill.call(this,i),RangeTouch.setup(i),i},createProgress(e,t){const i=createElement(\"progress\",extend(getAttributesFromSelector(this.config.selectors.display[e]),{min:0,max:100,value:0,role:\"progressbar\",\"aria-hidden\":!0},t));if(\"volume\"!==e){i.appendChild(createElement(\"span\",null,\"0\"));const t={played:\"played\",buffer:\"buffered\"}[e],s=t?i18n.get(t,this.config):\"\";i.innerText=`% ${s.toLowerCase()}`}return this.elements.display[e]=i,i},createTime(e,t){const i=getAttributesFromSelector(this.config.selectors.display[e],t),s=createElement(\"div\",extend(i,{class:`${i.class?i.class:\"\"} ${this.config.classNames.display.time} `.trim(),\"aria-label\":i18n.get(e,this.config),role:\"timer\"}),\"00:00\");return this.elements.display[e]=s,s},bindMenuItemShortcuts(e,t){on.call(this,e,\"keydown keyup\",(i=>{if(![\" \",\"ArrowUp\",\"ArrowDown\",\"ArrowRight\"].includes(i.key))return;if(i.preventDefault(),i.stopPropagation(),\"keydown\"===i.type)return;const s=matches(e,'[role=\"menuitemradio\"]');if(!s&&[\" \",\"ArrowRight\"].includes(i.key))controls.showMenuPanel.call(this,t,!0);else{let t;\" \"!==i.key&&(\"ArrowDown\"===i.key||s&&\"ArrowRight\"===i.key?(t=e.nextElementSibling,is.element(t)||(t=e.parentNode.firstElementChild)):(t=e.previousElementSibling,is.element(t)||(t=e.parentNode.lastElementChild)),setFocus.call(this,t,!0))}}),!1),on.call(this,e,\"keyup\",(e=>{\"Return\"===e.key&&controls.focusFirstMenuItem.call(this,null,!0)}))},createMenuItem({value:e,list:t,type:i,title:s,badge:n=null,checked:r=!1}){const a=getAttributesFromSelector(this.config.selectors.inputs[i]),o=createElement(\"button\",extend(a,{type:\"button\",role:\"menuitemradio\",class:`${this.config.classNames.control} ${a.class?a.class:\"\"}`.trim(),\"aria-checked\":r,value:e})),l=createElement(\"span\");l.innerHTML=s,is.element(n)&&l.appendChild(n),o.appendChild(l),Object.defineProperty(o,\"checked\",{enumerable:!0,get:()=>\"true\"===o.getAttribute(\"aria-checked\"),set(e){e&&Array.from(o.parentNode.children).filter((e=>matches(e,'[role=\"menuitemradio\"]'))).forEach((e=>e.setAttribute(\"aria-checked\",\"false\"))),o.setAttribute(\"aria-checked\",e?\"true\":\"false\")}}),this.listeners.bind(o,\"click keyup\",(t=>{if(!is.keyboardEvent(t)||\" \"===t.key){switch(t.preventDefault(),t.stopPropagation(),o.checked=!0,i){case\"language\":this.currentTrack=Number(e);break;case\"quality\":this.quality=e;break;case\"speed\":this.speed=parseFloat(e)}controls.showMenuPanel.call(this,\"home\",is.keyboardEvent(t))}}),i,!1),controls.bindMenuItemShortcuts.call(this,o,i),t.appendChild(o)},formatTime(e=0,t=!1){if(!is.number(e))return e;return formatTime(e,getHours(this.duration)>0,t)},updateTimeDisplay(e=null,t=0,i=!1){is.element(e)&&is.number(t)&&(e.innerText=controls.formatTime(t,i))},updateVolume(){this.supported.ui&&(is.element(this.elements.inputs.volume)&&controls.setRange.call(this,this.elements.inputs.volume,this.muted?0:this.volume),is.element(this.elements.buttons.mute)&&(this.elements.buttons.mute.pressed=this.muted||0===this.volume))},setRange(e,t=0){is.element(e)&&(e.value=t,controls.updateRangeFill.call(this,e))},updateProgress(e){if(!this.supported.ui||!is.event(e))return;let t=0;const i=(e,t)=>{const i=is.number(t)?t:0,s=is.element(e)?e:this.elements.display.buffer;if(is.element(s)){s.value=i;const e=s.getElementsByTagName(\"span\")[0];is.element(e)&&(e.childNodes[0].nodeValue=i)}};if(e)switch(e.type){case\"timeupdate\":case\"seeking\":case\"seeked\":t=getPercentage(this.currentTime,this.duration),\"timeupdate\"===e.type&&controls.setRange.call(this,this.elements.inputs.seek,t);break;case\"playing\":case\"progress\":i(this.elements.display.buffer,100*this.buffered)}},updateRangeFill(e){const t=is.event(e)?e.target:e;if(is.element(t)&&\"range\"===t.getAttribute(\"type\")){if(matches(t,this.config.selectors.inputs.seek)){t.setAttribute(\"aria-valuenow\",this.currentTime);const e=controls.formatTime(this.currentTime),i=controls.formatTime(this.duration),s=i18n.get(\"seekLabel\",this.config);t.setAttribute(\"aria-valuetext\",s.replace(\"{currentTime}\",e).replace(\"{duration}\",i))}else if(matches(t,this.config.selectors.inputs.volume)){const e=100*t.value;t.setAttribute(\"aria-valuenow\",e),t.setAttribute(\"aria-valuetext\",`${e.toFixed(1)}%`)}else t.setAttribute(\"aria-valuenow\",t.value);(browser.isWebKit||browser.isIPadOS)&&t.style.setProperty(\"--value\",t.value/t.max*100+\"%\")}},updateSeekTooltip(e){var t,i;if(!this.config.tooltips.seek||!is.element(this.elements.inputs.seek)||!is.element(this.elements.display.seekTooltip)||0===this.duration)return;const s=this.elements.display.seekTooltip,n=`${this.config.classNames.tooltip}--visible`,r=e=>toggleClass(s,n,e);if(this.touch)return void r(!1);let a=0;const o=this.elements.progress.getBoundingClientRect();if(is.event(e))a=100/o.width*(e.pageX-o.left);else{if(!hasClass(s,n))return;a=parseFloat(s.style.left,10)}a<0?a=0:a>100&&(a=100);const l=this.duration/100*a;s.innerText=controls.formatTime(l);const c=null===(t=this.config.markers)||void 0===t||null===(i=t.points)||void 0===i?void 0:i.find((({time:e})=>e===Math.round(l)));c&&s.insertAdjacentHTML(\"afterbegin\",`${c.label}<br>`),s.style.left=`${a}%`,is.event(e)&&[\"mouseenter\",\"mouseleave\"].includes(e.type)&&r(\"mouseenter\"===e.type)},timeUpdate(e){const t=!is.element(this.elements.display.duration)&&this.config.invertTime;controls.updateTimeDisplay.call(this,this.elements.display.currentTime,t?this.duration-this.currentTime:this.currentTime,t),e&&\"timeupdate\"===e.type&&this.media.seeking||controls.updateProgress.call(this,e)},durationUpdate(){if(!this.supported.ui||!this.config.invertTime&&this.currentTime)return;if(this.duration>=2**32)return toggleHidden(this.elements.display.currentTime,!0),void toggleHidden(this.elements.progress,!0);is.element(this.elements.inputs.seek)&&this.elements.inputs.seek.setAttribute(\"aria-valuemax\",this.duration);const e=is.element(this.elements.display.duration);!e&&this.config.displayDuration&&this.paused&&controls.updateTimeDisplay.call(this,this.elements.display.currentTime,this.duration),e&&controls.updateTimeDisplay.call(this,this.elements.display.duration,this.duration),this.config.markers.enabled&&controls.setMarkers.call(this),controls.updateSeekTooltip.call(this)},toggleMenuButton(e,t){toggleHidden(this.elements.settings.buttons[e],!t)},updateSetting(e,t,i){const s=this.elements.settings.panels[e];let n=null,r=t;if(\"captions\"===e)n=this.currentTrack;else{if(n=is.empty(i)?this[e]:i,is.empty(n)&&(n=this.config[e].default),!is.empty(this.options[e])&&!this.options[e].includes(n))return void this.debug.warn(`Unsupported value of '${n}' for ${e}`);if(!this.config[e].options.includes(n))return void this.debug.warn(`Disabled value of '${n}' for ${e}`)}if(is.element(r)||(r=s&&s.querySelector('[role=\"menu\"]')),!is.element(r))return;this.elements.settings.buttons[e].querySelector(`.${this.config.classNames.menu.value}`).innerHTML=controls.getLabel.call(this,e,n);const a=r&&r.querySelector(`[value=\"${n}\"]`);is.element(a)&&(a.checked=!0)},getLabel(e,t){switch(e){case\"speed\":return 1===t?i18n.get(\"normal\",this.config):`${t}&times;`;case\"quality\":if(is.number(t)){const e=i18n.get(`qualityLabel.${t}`,this.config);return e.length?e:`${t}p`}return toTitleCase(t);case\"captions\":return captions.getLabel.call(this);default:return null}},setQualityMenu(e){if(!is.element(this.elements.settings.panels.quality))return;const t=\"quality\",i=this.elements.settings.panels.quality.querySelector('[role=\"menu\"]');is.array(e)&&(this.options.quality=dedupe(e).filter((e=>this.config.quality.options.includes(e))));const s=!is.empty(this.options.quality)&&this.options.quality.length>1;if(controls.toggleMenuButton.call(this,t,s),emptyElement(i),controls.checkMenu.call(this),!s)return;const n=e=>{const t=i18n.get(`qualityBadge.${e}`,this.config);return t.length?controls.createBadge.call(this,t):null};this.options.quality.sort(((e,t)=>{const i=this.config.quality.options;return i.indexOf(e)>i.indexOf(t)?1:-1})).forEach((e=>{controls.createMenuItem.call(this,{value:e,list:i,type:t,title:controls.getLabel.call(this,\"quality\",e),badge:n(e)})})),controls.updateSetting.call(this,t,i)},setCaptionsMenu(){if(!is.element(this.elements.settings.panels.captions))return;const e=\"captions\",t=this.elements.settings.panels.captions.querySelector('[role=\"menu\"]'),i=captions.getTracks.call(this),s=Boolean(i.length);if(controls.toggleMenuButton.call(this,e,s),emptyElement(t),controls.checkMenu.call(this),!s)return;const n=i.map(((e,i)=>({value:i,checked:this.captions.toggled&&this.currentTrack===i,title:captions.getLabel.call(this,e),badge:e.language&&controls.createBadge.call(this,e.language.toUpperCase()),list:t,type:\"language\"})));n.unshift({value:-1,checked:!this.captions.toggled,title:i18n.get(\"disabled\",this.config),list:t,type:\"language\"}),n.forEach(controls.createMenuItem.bind(this)),controls.updateSetting.call(this,e,t)},setSpeedMenu(){if(!is.element(this.elements.settings.panels.speed))return;const e=\"speed\",t=this.elements.settings.panels.speed.querySelector('[role=\"menu\"]');this.options.speed=this.options.speed.filter((e=>e>=this.minimumSpeed&&e<=this.maximumSpeed));const i=!is.empty(this.options.speed)&&this.options.speed.length>1;controls.toggleMenuButton.call(this,e,i),emptyElement(t),controls.checkMenu.call(this),i&&(this.options.speed.forEach((i=>{controls.createMenuItem.call(this,{value:i,list:t,type:e,title:controls.getLabel.call(this,\"speed\",i)})})),controls.updateSetting.call(this,e,t))},checkMenu(){const{buttons:e}=this.elements.settings,t=!is.empty(e)&&Object.values(e).some((e=>!e.hidden));toggleHidden(this.elements.settings.menu,!t)},focusFirstMenuItem(e,t=!1){if(this.elements.settings.popup.hidden)return;let i=e;is.element(i)||(i=Object.values(this.elements.settings.panels).find((e=>!e.hidden)));const s=i.querySelector('[role^=\"menuitem\"]');setFocus.call(this,s,t)},toggleMenu(e){const{popup:t}=this.elements.settings,i=this.elements.buttons.settings;if(!is.element(t)||!is.element(i))return;const{hidden:s}=t;let n=s;if(is.boolean(e))n=e;else if(is.keyboardEvent(e)&&\"Escape\"===e.key)n=!1;else if(is.event(e)){const s=is.function(e.composedPath)?e.composedPath()[0]:e.target,r=t.contains(s);if(r||!r&&e.target!==i&&n)return}i.setAttribute(\"aria-expanded\",n),toggleHidden(t,!n),toggleClass(this.elements.container,this.config.classNames.menu.open,n),n&&is.keyboardEvent(e)?controls.focusFirstMenuItem.call(this,null,!0):n||s||setFocus.call(this,i,is.keyboardEvent(e))},getMenuSize(e){const t=e.cloneNode(!0);t.style.position=\"absolute\",t.style.opacity=0,t.removeAttribute(\"hidden\"),e.parentNode.appendChild(t);const i=t.scrollWidth,s=t.scrollHeight;return removeElement(t),{width:i,height:s}},showMenuPanel(e=\"\",t=!1){const i=this.elements.container.querySelector(`#plyr-settings-${this.id}-${e}`);if(!is.element(i))return;const s=i.parentNode,n=Array.from(s.children).find((e=>!e.hidden));if(support.transitions&&!support.reducedMotion){s.style.width=`${n.scrollWidth}px`,s.style.height=`${n.scrollHeight}px`;const e=controls.getMenuSize.call(this,i),t=e=>{e.target===s&&[\"width\",\"height\"].includes(e.propertyName)&&(s.style.width=\"\",s.style.height=\"\",off.call(this,s,transitionEndEvent,t))};on.call(this,s,transitionEndEvent,t),s.style.width=`${e.width}px`,s.style.height=`${e.height}px`}toggleHidden(n,!0),toggleHidden(i,!1),controls.focusFirstMenuItem.call(this,i,t)},setDownloadUrl(){const e=this.elements.buttons.download;is.element(e)&&e.setAttribute(\"href\",this.download)},create(e){const{bindMenuItemShortcuts:t,createButton:i,createProgress:s,createRange:n,createTime:r,setQualityMenu:a,setSpeedMenu:o,showMenuPanel:l}=controls;this.elements.controls=null,is.array(this.config.controls)&&this.config.controls.includes(\"play-large\")&&this.elements.container.appendChild(i.call(this,\"play-large\"));const c=createElement(\"div\",getAttributesFromSelector(this.config.selectors.controls.wrapper));this.elements.controls=c;const u={class:\"plyr__controls__item\"};return dedupe(is.array(this.config.controls)?this.config.controls:[]).forEach((a=>{if(\"restart\"===a&&c.appendChild(i.call(this,\"restart\",u)),\"rewind\"===a&&c.appendChild(i.call(this,\"rewind\",u)),\"play\"===a&&c.appendChild(i.call(this,\"play\",u)),\"fast-forward\"===a&&c.appendChild(i.call(this,\"fast-forward\",u)),\"progress\"===a){const t=createElement(\"div\",{class:`${u.class} plyr__progress__container`}),i=createElement(\"div\",getAttributesFromSelector(this.config.selectors.progress));if(i.appendChild(n.call(this,\"seek\",{id:`plyr-seek-${e.id}`})),i.appendChild(s.call(this,\"buffer\")),this.config.tooltips.seek){const e=createElement(\"span\",{class:this.config.classNames.tooltip},\"00:00\");i.appendChild(e),this.elements.display.seekTooltip=e}this.elements.progress=i,t.appendChild(this.elements.progress),c.appendChild(t)}if(\"current-time\"===a&&c.appendChild(r.call(this,\"currentTime\",u)),\"duration\"===a&&c.appendChild(r.call(this,\"duration\",u)),\"mute\"===a||\"volume\"===a){let{volume:t}=this.elements;if(is.element(t)&&c.contains(t)||(t=createElement(\"div\",extend({},u,{class:`${u.class} plyr__volume`.trim()})),this.elements.volume=t,c.appendChild(t)),\"mute\"===a&&t.appendChild(i.call(this,\"mute\")),\"volume\"===a&&!browser.isIos&&!browser.isIPadOS){const i={max:1,step:.05,value:this.config.volume};t.appendChild(n.call(this,\"volume\",extend(i,{id:`plyr-volume-${e.id}`})))}}if(\"captions\"===a&&c.appendChild(i.call(this,\"captions\",u)),\"settings\"===a&&!is.empty(this.config.settings)){const s=createElement(\"div\",extend({},u,{class:`${u.class} plyr__menu`.trim(),hidden:\"\"}));s.appendChild(i.call(this,\"settings\",{\"aria-haspopup\":!0,\"aria-controls\":`plyr-settings-${e.id}`,\"aria-expanded\":!1}));const n=createElement(\"div\",{class:\"plyr__menu__container\",id:`plyr-settings-${e.id}`,hidden:\"\"}),r=createElement(\"div\"),a=createElement(\"div\",{id:`plyr-settings-${e.id}-home`}),o=createElement(\"div\",{role:\"menu\"});a.appendChild(o),r.appendChild(a),this.elements.settings.panels.home=a,this.config.settings.forEach((i=>{const s=createElement(\"button\",extend(getAttributesFromSelector(this.config.selectors.buttons.settings),{type:\"button\",class:`${this.config.classNames.control} ${this.config.classNames.control}--forward`,role:\"menuitem\",\"aria-haspopup\":!0,hidden:\"\"}));t.call(this,s,i),on.call(this,s,\"click\",(()=>{l.call(this,i,!1)}));const n=createElement(\"span\",null,i18n.get(i,this.config)),a=createElement(\"span\",{class:this.config.classNames.menu.value});a.innerHTML=e[i],n.appendChild(a),s.appendChild(n),o.appendChild(s);const c=createElement(\"div\",{id:`plyr-settings-${e.id}-${i}`,hidden:\"\"}),u=createElement(\"button\",{type:\"button\",class:`${this.config.classNames.control} ${this.config.classNames.control}--back`});u.appendChild(createElement(\"span\",{\"aria-hidden\":!0},i18n.get(i,this.config))),u.appendChild(createElement(\"span\",{class:this.config.classNames.hidden},i18n.get(\"menuBack\",this.config))),on.call(this,c,\"keydown\",(e=>{\"ArrowLeft\"===e.key&&(e.preventDefault(),e.stopPropagation(),l.call(this,\"home\",!0))}),!1),on.call(this,u,\"click\",(()=>{l.call(this,\"home\",!1)})),c.appendChild(u),c.appendChild(createElement(\"div\",{role:\"menu\"})),r.appendChild(c),this.elements.settings.buttons[i]=s,this.elements.settings.panels[i]=c})),n.appendChild(r),s.appendChild(n),c.appendChild(s),this.elements.settings.popup=n,this.elements.settings.menu=s}if(\"pip\"===a&&support.pip&&c.appendChild(i.call(this,\"pip\",u)),\"airplay\"===a&&support.airplay&&c.appendChild(i.call(this,\"airplay\",u)),\"download\"===a){const e=extend({},u,{element:\"a\",href:this.download,target:\"_blank\"});this.isHTML5&&(e.download=\"\");const{download:t}=this.config.urls;!is.url(t)&&this.isEmbed&&extend(e,{icon:`logo-${this.provider}`,label:this.provider}),c.appendChild(i.call(this,\"download\",e))}\"fullscreen\"===a&&c.appendChild(i.call(this,\"fullscreen\",u))})),this.isHTML5&&a.call(this,html5.getQualityOptions.call(this)),o.call(this),c},inject(){if(this.config.loadSprite){const e=controls.getIconUrl.call(this);e.cors&&loadSprite(e.url,\"sprite-plyr\")}this.id=Math.floor(1e4*Math.random());let e=null;this.elements.controls=null;const t={id:this.id,seektime:this.config.seekTime,title:this.config.title};let i=!0;is.function(this.config.controls)&&(this.config.controls=this.config.controls.call(this,t)),this.config.controls||(this.config.controls=[]),is.element(this.config.controls)||is.string(this.config.controls)?e=this.config.controls:(e=controls.create.call(this,{id:this.id,seektime:this.config.seekTime,speed:this.speed,quality:this.quality,captions:captions.getLabel.call(this)}),i=!1);let s;i&&is.string(this.config.controls)&&(e=(e=>{let i=e;return Object.entries(t).forEach((([e,t])=>{i=replaceAll(i,`{${e}}`,t)})),i})(e)),is.string(this.config.selectors.controls.container)&&(s=document.querySelector(this.config.selectors.controls.container)),is.element(s)||(s=this.elements.container);if(s[is.element(e)?\"insertAdjacentElement\":\"insertAdjacentHTML\"](\"afterbegin\",e),is.element(this.elements.controls)||controls.findElements.call(this),!is.empty(this.elements.buttons)){const e=e=>{const t=this.config.classNames.controlPressed;e.setAttribute(\"aria-pressed\",\"false\"),Object.defineProperty(e,\"pressed\",{configurable:!0,enumerable:!0,get:()=>hasClass(e,t),set(i=!1){toggleClass(e,t,i),e.setAttribute(\"aria-pressed\",i?\"true\":\"false\")}})};Object.values(this.elements.buttons).filter(Boolean).forEach((t=>{is.array(t)||is.nodeList(t)?Array.from(t).filter(Boolean).forEach(e):e(t)}))}if(browser.isEdge&&repaint(s),this.config.tooltips.controls){const{classNames:e,selectors:t}=this.config,i=`${t.controls.wrapper} ${t.labels} .${e.hidden}`,s=getElements.call(this,i);Array.from(s).forEach((e=>{toggleClass(e,this.config.classNames.hidden,!1),toggleClass(e,this.config.classNames.tooltip,!0)}))}},setMediaMetadata(){try{\"mediaSession\"in navigator&&(navigator.mediaSession.metadata=new window.MediaMetadata({title:this.config.mediaMetadata.title,artist:this.config.mediaMetadata.artist,album:this.config.mediaMetadata.album,artwork:this.config.mediaMetadata.artwork}))}catch(e){}},setMarkers(){var e,t;if(!this.duration||this.elements.markers)return;const i=null===(e=this.config.markers)||void 0===e||null===(t=e.points)||void 0===t?void 0:t.filter((({time:e})=>e>0&&e<this.duration));if(null==i||!i.length)return;const s=document.createDocumentFragment(),n=document.createDocumentFragment();let r=null;const a=`${this.config.classNames.tooltip}--visible`,o=e=>toggleClass(r,a,e);i.forEach((e=>{const t=createElement(\"span\",{class:this.config.classNames.marker},\"\"),i=e.time/this.duration*100+\"%\";r&&(t.addEventListener(\"mouseenter\",(()=>{e.label||(r.style.left=i,r.innerHTML=e.label,o(!0))})),t.addEventListener(\"mouseleave\",(()=>{o(!1)}))),t.addEventListener(\"click\",(()=>{this.currentTime=e.time})),t.style.left=i,n.appendChild(t)})),s.appendChild(n),this.config.tooltips.seek||(r=createElement(\"span\",{class:this.config.classNames.tooltip},\"\"),s.appendChild(r)),this.elements.markers={points:n,tip:r},this.elements.progress.appendChild(s)}};function parseUrl(e,t=!0){let i=e;if(t){const e=document.createElement(\"a\");e.href=i,i=e.href}try{return new URL(i)}catch(e){return null}}function buildUrlParams(e){const t=new URLSearchParams;return is.object(e)&&Object.entries(e).forEach((([e,i])=>{t.set(e,i)})),t}const captions={setup(){if(!this.supported.ui)return;if(!this.isVideo||this.isYouTube||this.isHTML5&&!support.textTracks)return void(is.array(this.config.controls)&&this.config.controls.includes(\"settings\")&&this.config.settings.includes(\"captions\")&&controls.setCaptionsMenu.call(this));if(is.element(this.elements.captions)||(this.elements.captions=createElement(\"div\",getAttributesFromSelector(this.config.selectors.captions)),this.elements.captions.setAttribute(\"dir\",\"auto\"),insertAfter(this.elements.captions,this.elements.wrapper)),browser.isIE&&window.URL){const e=this.media.querySelectorAll(\"track\");Array.from(e).forEach((e=>{const t=e.getAttribute(\"src\"),i=parseUrl(t);null!==i&&i.hostname!==window.location.href.hostname&&[\"http:\",\"https:\"].includes(i.protocol)&&fetch(t,\"blob\").then((t=>{e.setAttribute(\"src\",window.URL.createObjectURL(t))})).catch((()=>{removeElement(e)}))}))}const e=dedupe((navigator.languages||[navigator.language||navigator.userLanguage||\"en\"]).map((e=>e.split(\"-\")[0])));let t=(this.storage.get(\"language\")||this.config.captions.language||\"auto\").toLowerCase();\"auto\"===t&&([t]=e);let i=this.storage.get(\"captions\");if(is.boolean(i)||({active:i}=this.config.captions),Object.assign(this.captions,{toggled:!1,active:i,language:t,languages:e}),this.isHTML5){const e=this.config.captions.update?\"addtrack removetrack\":\"removetrack\";on.call(this,this.media.textTracks,e,captions.update.bind(this))}setTimeout(captions.update.bind(this),0)},update(){const e=captions.getTracks.call(this,!0),{active:t,language:i,meta:s,currentTrackNode:n}=this.captions,r=Boolean(e.find((e=>e.language===i)));this.isHTML5&&this.isVideo&&e.filter((e=>!s.get(e))).forEach((e=>{this.debug.log(\"Track added\",e),s.set(e,{default:\"showing\"===e.mode}),\"showing\"===e.mode&&(e.mode=\"hidden\"),on.call(this,e,\"cuechange\",(()=>captions.updateCues.call(this)))})),(r&&this.language!==i||!e.includes(n))&&(captions.setLanguage.call(this,i),captions.toggle.call(this,t&&r)),this.elements&&toggleClass(this.elements.container,this.config.classNames.captions.enabled,!is.empty(e)),is.array(this.config.controls)&&this.config.controls.includes(\"settings\")&&this.config.settings.includes(\"captions\")&&controls.setCaptionsMenu.call(this)},toggle(e,t=!0){if(!this.supported.ui)return;const{toggled:i}=this.captions,s=this.config.classNames.captions.active,n=is.nullOrUndefined(e)?!i:e;if(n!==i){if(t||(this.captions.active=n,this.storage.set({captions:n})),!this.language&&n&&!t){const e=captions.getTracks.call(this),t=captions.findTrack.call(this,[this.captions.language,...this.captions.languages],!0);return this.captions.language=t.language,void captions.set.call(this,e.indexOf(t))}this.elements.buttons.captions&&(this.elements.buttons.captions.pressed=n),toggleClass(this.elements.container,s,n),this.captions.toggled=n,controls.updateSetting.call(this,\"captions\"),triggerEvent.call(this,this.media,n?\"captionsenabled\":\"captionsdisabled\")}setTimeout((()=>{n&&this.captions.toggled&&(this.captions.currentTrackNode.mode=\"hidden\")}))},set(e,t=!0){const i=captions.getTracks.call(this);if(-1!==e)if(is.number(e))if(e in i){if(this.captions.currentTrack!==e){this.captions.currentTrack=e;const s=i[e],{language:n}=s||{};this.captions.currentTrackNode=s,controls.updateSetting.call(this,\"captions\"),t||(this.captions.language=n,this.storage.set({language:n})),this.isVimeo&&this.embed.enableTextTrack(n),triggerEvent.call(this,this.media,\"languagechange\")}captions.toggle.call(this,!0,t),this.isHTML5&&this.isVideo&&captions.updateCues.call(this)}else this.debug.warn(\"Track not found\",e);else this.debug.warn(\"Invalid caption argument\",e);else captions.toggle.call(this,!1,t)},setLanguage(e,t=!0){if(!is.string(e))return void this.debug.warn(\"Invalid language argument\",e);const i=e.toLowerCase();this.captions.language=i;const s=captions.getTracks.call(this),n=captions.findTrack.call(this,[i]);captions.set.call(this,s.indexOf(n),t)},getTracks(e=!1){return Array.from((this.media||{}).textTracks||[]).filter((t=>!this.isHTML5||e||this.captions.meta.has(t))).filter((e=>[\"captions\",\"subtitles\"].includes(e.kind)))},findTrack(e,t=!1){const i=captions.getTracks.call(this),s=e=>Number((this.captions.meta.get(e)||{}).default),n=Array.from(i).sort(((e,t)=>s(t)-s(e)));let r;return e.every((e=>(r=n.find((t=>t.language===e)),!r))),r||(t?n[0]:void 0)},getCurrentTrack(){return captions.getTracks.call(this)[this.currentTrack]},getLabel(e){let t=e;return!is.track(t)&&support.textTracks&&this.captions.toggled&&(t=captions.getCurrentTrack.call(this)),is.track(t)?is.empty(t.label)?is.empty(t.language)?i18n.get(\"enabled\",this.config):e.language.toUpperCase():t.label:i18n.get(\"disabled\",this.config)},updateCues(e){if(!this.supported.ui)return;if(!is.element(this.elements.captions))return void this.debug.warn(\"No captions element to render to\");if(!is.nullOrUndefined(e)&&!Array.isArray(e))return void this.debug.warn(\"updateCues: Invalid input\",e);let t=e;if(!t){const e=captions.getCurrentTrack.call(this);t=Array.from((e||{}).activeCues||[]).map((e=>e.getCueAsHTML())).map(getHTML)}const i=t.map((e=>e.trim())).join(\"\\n\");if(i!==this.elements.captions.innerHTML){emptyElement(this.elements.captions);const e=createElement(\"span\",getAttributesFromSelector(this.config.selectors.caption));e.innerHTML=i,this.elements.captions.appendChild(e),triggerEvent.call(this,this.media,\"cuechange\")}}},defaults={enabled:!0,title:\"\",debug:!1,autoplay:!1,autopause:!0,playsinline:!0,seekTime:10,volume:1,muted:!1,duration:null,displayDuration:!0,invertTime:!0,toggleInvert:!0,ratio:null,clickToPlay:!0,hideControls:!0,resetOnEnd:!1,disableContextMenu:!0,loadSprite:!0,iconPrefix:\"plyr\",iconUrl:\"https://cdn.plyr.io/3.7.8/plyr.svg\",blankVideo:\"https://cdn.plyr.io/static/blank.mp4\",quality:{default:576,options:[4320,2880,2160,1440,1080,720,576,480,360,240],forced:!1,onChange:null},loop:{active:!1},speed:{selected:1,options:[.5,.75,1,1.25,1.5,1.75,2,4]},keyboard:{focused:!0,global:!1},tooltips:{controls:!1,seek:!0},captions:{active:!1,language:\"auto\",update:!1},fullscreen:{enabled:!0,fallback:!0,iosNative:!1},storage:{enabled:!0,key:\"plyr\"},controls:[\"play-large\",\"play\",\"progress\",\"current-time\",\"mute\",\"volume\",\"captions\",\"settings\",\"pip\",\"airplay\",\"fullscreen\"],settings:[\"captions\",\"quality\",\"speed\"],i18n:{restart:\"Restart\",rewind:\"Rewind {seektime}s\",play:\"Play\",pause:\"Pause\",fastForward:\"Forward {seektime}s\",seek:\"Seek\",seekLabel:\"{currentTime} of {duration}\",played:\"Played\",buffered:\"Buffered\",currentTime:\"Current time\",duration:\"Duration\",volume:\"Volume\",mute:\"Mute\",unmute:\"Unmute\",enableCaptions:\"Enable captions\",disableCaptions:\"Disable captions\",download:\"Download\",enterFullscreen:\"Enter fullscreen\",exitFullscreen:\"Exit fullscreen\",frameTitle:\"Player for {title}\",captions:\"Captions\",settings:\"Settings\",pip:\"PIP\",menuBack:\"Go back to previous menu\",speed:\"Speed\",normal:\"Normal\",quality:\"Quality\",loop:\"Loop\",start:\"Start\",end:\"End\",all:\"All\",reset:\"Reset\",disabled:\"Disabled\",enabled:\"Enabled\",advertisement:\"Ad\",qualityBadge:{2160:\"4K\",1440:\"HD\",1080:\"HD\",720:\"HD\",576:\"SD\",480:\"SD\"}},urls:{download:null,vimeo:{sdk:\"https://player.vimeo.com/api/player.js\",iframe:\"https://player.vimeo.com/video/{0}?{1}\",api:\"https://vimeo.com/api/oembed.json?url={0}\"},youtube:{sdk:\"https://www.youtube.com/iframe_api\",api:\"https://noembed.com/embed?url=https://www.youtube.com/watch?v={0}\"},googleIMA:{sdk:\"https://imasdk.googleapis.com/js/sdkloader/ima3.js\"}},listeners:{seek:null,play:null,pause:null,restart:null,rewind:null,fastForward:null,mute:null,volume:null,captions:null,download:null,fullscreen:null,pip:null,airplay:null,speed:null,quality:null,loop:null,language:null},events:[\"ended\",\"progress\",\"stalled\",\"playing\",\"waiting\",\"canplay\",\"canplaythrough\",\"loadstart\",\"loadeddata\",\"loadedmetadata\",\"timeupdate\",\"volumechange\",\"play\",\"pause\",\"error\",\"seeking\",\"seeked\",\"emptied\",\"ratechange\",\"cuechange\",\"download\",\"enterfullscreen\",\"exitfullscreen\",\"captionsenabled\",\"captionsdisabled\",\"languagechange\",\"controlshidden\",\"controlsshown\",\"ready\",\"statechange\",\"qualitychange\",\"adsloaded\",\"adscontentpause\",\"adscontentresume\",\"adstarted\",\"adsmidpoint\",\"adscomplete\",\"adsallcomplete\",\"adsimpression\",\"adsclick\"],selectors:{editable:\"input, textarea, select, [contenteditable]\",container:\".plyr\",controls:{container:null,wrapper:\".plyr__controls\"},labels:\"[data-plyr]\",buttons:{play:'[data-plyr=\"play\"]',pause:'[data-plyr=\"pause\"]',restart:'[data-plyr=\"restart\"]',rewind:'[data-plyr=\"rewind\"]',fastForward:'[data-plyr=\"fast-forward\"]',mute:'[data-plyr=\"mute\"]',captions:'[data-plyr=\"captions\"]',download:'[data-plyr=\"download\"]',fullscreen:'[data-plyr=\"fullscreen\"]',pip:'[data-plyr=\"pip\"]',airplay:'[data-plyr=\"airplay\"]',settings:'[data-plyr=\"settings\"]',loop:'[data-plyr=\"loop\"]'},inputs:{seek:'[data-plyr=\"seek\"]',volume:'[data-plyr=\"volume\"]',speed:'[data-plyr=\"speed\"]',language:'[data-plyr=\"language\"]',quality:'[data-plyr=\"quality\"]'},display:{currentTime:\".plyr__time--current\",duration:\".plyr__time--duration\",buffer:\".plyr__progress__buffer\",loop:\".plyr__progress__loop\",volume:\".plyr__volume--display\"},progress:\".plyr__progress\",captions:\".plyr__captions\",caption:\".plyr__caption\"},classNames:{type:\"plyr--{0}\",provider:\"plyr--{0}\",video:\"plyr__video-wrapper\",embed:\"plyr__video-embed\",videoFixedRatio:\"plyr__video-wrapper--fixed-ratio\",embedContainer:\"plyr__video-embed__container\",poster:\"plyr__poster\",posterEnabled:\"plyr__poster-enabled\",ads:\"plyr__ads\",control:\"plyr__control\",controlPressed:\"plyr__control--pressed\",playing:\"plyr--playing\",paused:\"plyr--paused\",stopped:\"plyr--stopped\",loading:\"plyr--loading\",hover:\"plyr--hover\",tooltip:\"plyr__tooltip\",cues:\"plyr__cues\",marker:\"plyr__progress__marker\",hidden:\"plyr__sr-only\",hideControls:\"plyr--hide-controls\",isTouch:\"plyr--is-touch\",uiSupported:\"plyr--full-ui\",noTransition:\"plyr--no-transition\",display:{time:\"plyr__time\"},menu:{value:\"plyr__menu__value\",badge:\"plyr__badge\",open:\"plyr--menu-open\"},captions:{enabled:\"plyr--captions-enabled\",active:\"plyr--captions-active\"},fullscreen:{enabled:\"plyr--fullscreen-enabled\",fallback:\"plyr--fullscreen-fallback\"},pip:{supported:\"plyr--pip-supported\",active:\"plyr--pip-active\"},airplay:{supported:\"plyr--airplay-supported\",active:\"plyr--airplay-active\"},previewThumbnails:{thumbContainer:\"plyr__preview-thumb\",thumbContainerShown:\"plyr__preview-thumb--is-shown\",imageContainer:\"plyr__preview-thumb__image-container\",timeContainer:\"plyr__preview-thumb__time-container\",scrubbingContainer:\"plyr__preview-scrubbing\",scrubbingContainerShown:\"plyr__preview-scrubbing--is-shown\"}},attributes:{embed:{provider:\"data-plyr-provider\",id:\"data-plyr-embed-id\",hash:\"data-plyr-embed-hash\"}},ads:{enabled:!1,publisherId:\"\",tagUrl:\"\"},previewThumbnails:{enabled:!1,src:\"\"},vimeo:{byline:!1,portrait:!1,title:!1,speed:!0,transparent:!1,customControls:!0,referrerPolicy:null,premium:!1},youtube:{rel:0,showinfo:0,iv_load_policy:3,modestbranding:1,customControls:!0,noCookie:!1},mediaMetadata:{title:\"\",artist:\"\",album:\"\",artwork:[]},markers:{enabled:!1,points:[]}},pip={active:\"picture-in-picture\",inactive:\"inline\"},providers={html5:\"html5\",youtube:\"youtube\",vimeo:\"vimeo\"},types={audio:\"audio\",video:\"video\"};function getProviderByUrl(e){return/^(https?:\\/\\/)?(www\\.)?(youtube\\.com|youtube-nocookie\\.com|youtu\\.?be)\\/.+$/.test(e)?providers.youtube:/^https?:\\/\\/player.vimeo.com\\/video\\/\\d{0,9}(?=\\b|\\/)/.test(e)?providers.vimeo:null}const noop=()=>{};class Console{constructor(e=!1){this.enabled=window.console&&e,this.enabled&&this.log(\"Debugging enabled\")}get log(){return this.enabled?Function.prototype.bind.call(console.log,console):noop}get warn(){return this.enabled?Function.prototype.bind.call(console.warn,console):noop}get error(){return this.enabled?Function.prototype.bind.call(console.error,console):noop}}class Fullscreen{constructor(e){_defineProperty$1(this,\"onChange\",(()=>{if(!this.supported)return;const e=this.player.elements.buttons.fullscreen;is.element(e)&&(e.pressed=this.active);const t=this.target===this.player.media?this.target:this.player.elements.container;triggerEvent.call(this.player,t,this.active?\"enterfullscreen\":\"exitfullscreen\",!0)})),_defineProperty$1(this,\"toggleFallback\",((e=!1)=>{if(e?this.scrollPosition={x:window.scrollX??0,y:window.scrollY??0}:window.scrollTo(this.scrollPosition.x,this.scrollPosition.y),document.body.style.overflow=e?\"hidden\":\"\",toggleClass(this.target,this.player.config.classNames.fullscreen.fallback,e),browser.isIos){let t=document.head.querySelector('meta[name=\"viewport\"]');const i=\"viewport-fit=cover\";t||(t=document.createElement(\"meta\"),t.setAttribute(\"name\",\"viewport\"));const s=is.string(t.content)&&t.content.includes(i);e?(this.cleanupViewport=!s,s||(t.content+=`,${i}`)):this.cleanupViewport&&(t.content=t.content.split(\",\").filter((e=>e.trim()!==i)).join(\",\"))}this.onChange()})),_defineProperty$1(this,\"trapFocus\",(e=>{if(browser.isIos||browser.isIPadOS||!this.active||\"Tab\"!==e.key)return;const t=document.activeElement,i=getElements.call(this.player,\"a[href], button:not(:disabled), input:not(:disabled), [tabindex]\"),[s]=i,n=i[i.length-1];t!==n||e.shiftKey?t===s&&e.shiftKey&&(n.focus(),e.preventDefault()):(s.focus(),e.preventDefault())})),_defineProperty$1(this,\"update\",(()=>{if(this.supported){let e;e=this.forceFallback?\"Fallback (forced)\":Fullscreen.nativeSupported?\"Native\":\"Fallback\",this.player.debug.log(`${e} fullscreen enabled`)}else this.player.debug.log(\"Fullscreen not supported and fallback disabled\");toggleClass(this.player.elements.container,this.player.config.classNames.fullscreen.enabled,this.supported)})),_defineProperty$1(this,\"enter\",(()=>{this.supported&&(browser.isIos&&this.player.config.fullscreen.iosNative?this.player.isVimeo?this.player.embed.requestFullscreen():this.target.webkitEnterFullscreen():!Fullscreen.nativeSupported||this.forceFallback?this.toggleFallback(!0):this.prefix?is.empty(this.prefix)||this.target[`${this.prefix}Request${this.property}`]():this.target.requestFullscreen({navigationUI:\"hide\"}))})),_defineProperty$1(this,\"exit\",(()=>{if(this.supported)if(browser.isIos&&this.player.config.fullscreen.iosNative)this.player.isVimeo?this.player.embed.exitFullscreen():this.target.webkitEnterFullscreen(),silencePromise(this.player.play());else if(!Fullscreen.nativeSupported||this.forceFallback)this.toggleFallback(!1);else if(this.prefix){if(!is.empty(this.prefix)){const e=\"moz\"===this.prefix?\"Cancel\":\"Exit\";document[`${this.prefix}${e}${this.property}`]()}}else(document.cancelFullScreen||document.exitFullscreen).call(document)})),_defineProperty$1(this,\"toggle\",(()=>{this.active?this.exit():this.enter()})),this.player=e,this.prefix=Fullscreen.prefix,this.property=Fullscreen.property,this.scrollPosition={x:0,y:0},this.forceFallback=\"force\"===e.config.fullscreen.fallback,this.player.elements.fullscreen=e.config.fullscreen.container&&closest$1(this.player.elements.container,e.config.fullscreen.container),on.call(this.player,document,\"ms\"===this.prefix?\"MSFullscreenChange\":`${this.prefix}fullscreenchange`,(()=>{this.onChange()})),on.call(this.player,this.player.elements.container,\"dblclick\",(e=>{is.element(this.player.elements.controls)&&this.player.elements.controls.contains(e.target)||this.player.listeners.proxy(e,this.toggle,\"fullscreen\")})),on.call(this,this.player.elements.container,\"keydown\",(e=>this.trapFocus(e))),this.update()}static get nativeSupported(){return!!(document.fullscreenEnabled||document.webkitFullscreenEnabled||document.mozFullScreenEnabled||document.msFullscreenEnabled)}get useNative(){return Fullscreen.nativeSupported&&!this.forceFallback}static get prefix(){if(is.function(document.exitFullscreen))return\"\";let e=\"\";return[\"webkit\",\"moz\",\"ms\"].some((t=>!(!is.function(document[`${t}ExitFullscreen`])&&!is.function(document[`${t}CancelFullScreen`]))&&(e=t,!0))),e}static get property(){return\"moz\"===this.prefix?\"FullScreen\":\"Fullscreen\"}get supported(){return[this.player.config.fullscreen.enabled,this.player.isVideo,Fullscreen.nativeSupported||this.player.config.fullscreen.fallback,!this.player.isYouTube||Fullscreen.nativeSupported||!browser.isIos||this.player.config.playsinline&&!this.player.config.fullscreen.iosNative].every(Boolean)}get active(){if(!this.supported)return!1;if(!Fullscreen.nativeSupported||this.forceFallback)return hasClass(this.target,this.player.config.classNames.fullscreen.fallback);const e=this.prefix?this.target.getRootNode()[`${this.prefix}${this.property}Element`]:this.target.getRootNode().fullscreenElement;return e&&e.shadowRoot?e===this.target.getRootNode().host:e===this.target}get target(){return browser.isIos&&this.player.config.fullscreen.iosNative?this.player.media:this.player.elements.fullscreen??this.player.elements.container}}function loadImage(e,t=1){return new Promise(((i,s)=>{const n=new Image,r=()=>{delete n.onload,delete n.onerror,(n.naturalWidth>=t?i:s)(n)};Object.assign(n,{onload:r,onerror:r,src:e})}))}const ui={addStyleHook(){toggleClass(this.elements.container,this.config.selectors.container.replace(\".\",\"\"),!0),toggleClass(this.elements.container,this.config.classNames.uiSupported,this.supported.ui)},toggleNativeControls(e=!1){e&&this.isHTML5?this.media.setAttribute(\"controls\",\"\"):this.media.removeAttribute(\"controls\")},build(){if(this.listeners.media(),!this.supported.ui)return this.debug.warn(`Basic support only for ${this.provider} ${this.type}`),void ui.toggleNativeControls.call(this,!0);is.element(this.elements.controls)||(controls.inject.call(this),this.listeners.controls()),ui.toggleNativeControls.call(this),this.isHTML5&&captions.setup.call(this),this.volume=null,this.muted=null,this.loop=null,this.quality=null,this.speed=null,controls.updateVolume.call(this),controls.timeUpdate.call(this),controls.durationUpdate.call(this),ui.checkPlaying.call(this),toggleClass(this.elements.container,this.config.classNames.pip.supported,support.pip&&this.isHTML5&&this.isVideo),toggleClass(this.elements.container,this.config.classNames.airplay.supported,support.airplay&&this.isHTML5),toggleClass(this.elements.container,this.config.classNames.isTouch,this.touch),this.ready=!0,setTimeout((()=>{triggerEvent.call(this,this.media,\"ready\")}),0),ui.setTitle.call(this),this.poster&&ui.setPoster.call(this,this.poster,!1).catch((()=>{})),this.config.duration&&controls.durationUpdate.call(this),this.config.mediaMetadata&&controls.setMediaMetadata.call(this)},setTitle(){let e=i18n.get(\"play\",this.config);if(is.string(this.config.title)&&!is.empty(this.config.title)&&(e+=`, ${this.config.title}`),Array.from(this.elements.buttons.play||[]).forEach((t=>{t.setAttribute(\"aria-label\",e)})),this.isEmbed){const e=getElement.call(this,\"iframe\");if(!is.element(e))return;const t=is.empty(this.config.title)?\"video\":this.config.title,i=i18n.get(\"frameTitle\",this.config);e.setAttribute(\"title\",i.replace(\"{title}\",t))}},togglePoster(e){toggleClass(this.elements.container,this.config.classNames.posterEnabled,e)},setPoster(e,t=!0){return t&&this.poster?Promise.reject(new Error(\"Poster already set\")):(this.media.setAttribute(\"data-poster\",e),this.elements.poster.removeAttribute(\"hidden\"),ready.call(this).then((()=>loadImage(e))).catch((t=>{throw e===this.poster&&ui.togglePoster.call(this,!1),t})).then((()=>{if(e!==this.poster)throw new Error(\"setPoster cancelled by later call to setPoster\")})).then((()=>(Object.assign(this.elements.poster.style,{backgroundImage:`url('${e}')`,backgroundSize:\"\"}),ui.togglePoster.call(this,!0),e))))},checkPlaying(e){toggleClass(this.elements.container,this.config.classNames.playing,this.playing),toggleClass(this.elements.container,this.config.classNames.paused,this.paused),toggleClass(this.elements.container,this.config.classNames.stopped,this.stopped),Array.from(this.elements.buttons.play||[]).forEach((e=>{Object.assign(e,{pressed:this.playing}),e.setAttribute(\"aria-label\",i18n.get(this.playing?\"pause\":\"play\",this.config))})),is.event(e)&&\"timeupdate\"===e.type||ui.toggleControls.call(this)},checkLoading(e){this.loading=[\"stalled\",\"waiting\"].includes(e.type),clearTimeout(this.timers.loading),this.timers.loading=setTimeout((()=>{toggleClass(this.elements.container,this.config.classNames.loading,this.loading),ui.toggleControls.call(this)}),this.loading?250:0)},toggleControls(e){const{controls:t}=this.elements;if(t&&this.config.hideControls){const i=this.touch&&this.lastSeekTime+2e3>Date.now();this.toggleControls(Boolean(e||this.loading||this.paused||t.pressed||t.hover||i))}},migrateStyles(){Object.values({...this.media.style}).filter((e=>!is.empty(e)&&is.string(e)&&e.startsWith(\"--plyr\"))).forEach((e=>{this.elements.container.style.setProperty(e,this.media.style.getPropertyValue(e)),this.media.style.removeProperty(e)})),is.empty(this.media.style)&&this.media.removeAttribute(\"style\")}};class Listeners{constructor(e){_defineProperty$1(this,\"firstTouch\",(()=>{const{player:e}=this,{elements:t}=e;e.touch=!0,toggleClass(t.container,e.config.classNames.isTouch,!0)})),_defineProperty$1(this,\"global\",((e=!0)=>{const{player:t}=this;t.config.keyboard.global&&toggleListener.call(t,window,\"keydown keyup\",this.handleKey,e,!1),toggleListener.call(t,document.body,\"click\",this.toggleMenu,e),once.call(t,document.body,\"touchstart\",this.firstTouch)})),_defineProperty$1(this,\"container\",(()=>{const{player:e}=this,{config:t,elements:i,timers:s}=e;!t.keyboard.global&&t.keyboard.focused&&on.call(e,i.container,\"keydown keyup\",this.handleKey,!1),on.call(e,i.container,\"mousemove mouseleave touchstart touchmove enterfullscreen exitfullscreen\",(t=>{const{controls:n}=i;n&&\"enterfullscreen\"===t.type&&(n.pressed=!1,n.hover=!1);let r=0;[\"touchstart\",\"touchmove\",\"mousemove\"].includes(t.type)&&(ui.toggleControls.call(e,!0),r=e.touch?3e3:2e3),clearTimeout(s.controls),s.controls=setTimeout((()=>ui.toggleControls.call(e,!1)),r)}));const n=()=>{if(!e.isVimeo||e.config.vimeo.premium)return;const t=i.wrapper,{active:s}=e.fullscreen,[n,r]=getAspectRatio.call(e),a=supportsCSS(`aspect-ratio: ${n} / ${r}`);if(!s)return void(a?(t.style.width=null,t.style.height=null):(t.style.maxWidth=null,t.style.margin=null));const[o,l]=getViewportSize(),c=o/l>n/r;a?(t.style.width=c?\"auto\":\"100%\",t.style.height=c?\"100%\":\"auto\"):(t.style.maxWidth=c?l/r*n+\"px\":null,t.style.margin=c?\"0 auto\":null)},r=()=>{clearTimeout(s.resized),s.resized=setTimeout(n,50)};on.call(e,i.container,\"enterfullscreen exitfullscreen\",(t=>{const{target:s}=e.fullscreen;if(s!==i.container)return;if(!e.isEmbed&&is.empty(e.config.ratio))return;n();(\"enterfullscreen\"===t.type?on:off).call(e,window,\"resize\",r)}))})),_defineProperty$1(this,\"media\",(()=>{const{player:e}=this,{elements:t}=e;if(on.call(e,e.media,\"timeupdate seeking seeked\",(t=>controls.timeUpdate.call(e,t))),on.call(e,e.media,\"durationchange loadeddata loadedmetadata\",(t=>controls.durationUpdate.call(e,t))),on.call(e,e.media,\"ended\",(()=>{e.isHTML5&&e.isVideo&&e.config.resetOnEnd&&(e.restart(),e.pause())})),on.call(e,e.media,\"progress playing seeking seeked\",(t=>controls.updateProgress.call(e,t))),on.call(e,e.media,\"volumechange\",(t=>controls.updateVolume.call(e,t))),on.call(e,e.media,\"playing play pause ended emptied timeupdate\",(t=>ui.checkPlaying.call(e,t))),on.call(e,e.media,\"waiting canplay seeked playing\",(t=>ui.checkLoading.call(e,t))),e.supported.ui&&e.config.clickToPlay&&!e.isAudio){const i=getElement.call(e,`.${e.config.classNames.video}`);if(!is.element(i))return;on.call(e,t.container,\"click\",(s=>{([t.container,i].includes(s.target)||i.contains(s.target))&&(e.touch&&e.config.hideControls||(e.ended?(this.proxy(s,e.restart,\"restart\"),this.proxy(s,(()=>{silencePromise(e.play())}),\"play\")):this.proxy(s,(()=>{silencePromise(e.togglePlay())}),\"play\")))}))}e.supported.ui&&e.config.disableContextMenu&&on.call(e,t.wrapper,\"contextmenu\",(e=>{e.preventDefault()}),!1),on.call(e,e.media,\"volumechange\",(()=>{e.storage.set({volume:e.volume,muted:e.muted})})),on.call(e,e.media,\"ratechange\",(()=>{controls.updateSetting.call(e,\"speed\"),e.storage.set({speed:e.speed})})),on.call(e,e.media,\"qualitychange\",(t=>{controls.updateSetting.call(e,\"quality\",null,t.detail.quality)})),on.call(e,e.media,\"ready qualitychange\",(()=>{controls.setDownloadUrl.call(e)}));const i=e.config.events.concat([\"keyup\",\"keydown\"]).join(\" \");on.call(e,e.media,i,(i=>{let{detail:s={}}=i;\"error\"===i.type&&(s=e.media.error),triggerEvent.call(e,t.container,i.type,!0,s)}))})),_defineProperty$1(this,\"proxy\",((e,t,i)=>{const{player:s}=this,n=s.config.listeners[i];let r=!0;is.function(n)&&(r=n.call(s,e)),!1!==r&&is.function(t)&&t.call(s,e)})),_defineProperty$1(this,\"bind\",((e,t,i,s,n=!0)=>{const{player:r}=this,a=r.config.listeners[s],o=is.function(a);on.call(r,e,t,(e=>this.proxy(e,i,s)),n&&!o)})),_defineProperty$1(this,\"controls\",(()=>{const{player:e}=this,{elements:t}=e,i=browser.isIE?\"change\":\"input\";if(t.buttons.play&&Array.from(t.buttons.play).forEach((t=>{this.bind(t,\"click\",(()=>{silencePromise(e.togglePlay())}),\"play\")})),this.bind(t.buttons.restart,\"click\",e.restart,\"restart\"),this.bind(t.buttons.rewind,\"click\",(()=>{e.lastSeekTime=Date.now(),e.rewind()}),\"rewind\"),this.bind(t.buttons.fastForward,\"click\",(()=>{e.lastSeekTime=Date.now(),e.forward()}),\"fastForward\"),this.bind(t.buttons.mute,\"click\",(()=>{e.muted=!e.muted}),\"mute\"),this.bind(t.buttons.captions,\"click\",(()=>e.toggleCaptions())),this.bind(t.buttons.download,\"click\",(()=>{triggerEvent.call(e,e.media,\"download\")}),\"download\"),this.bind(t.buttons.fullscreen,\"click\",(()=>{e.fullscreen.toggle()}),\"fullscreen\"),this.bind(t.buttons.pip,\"click\",(()=>{e.pip=\"toggle\"}),\"pip\"),this.bind(t.buttons.airplay,\"click\",e.airplay,\"airplay\"),this.bind(t.buttons.settings,\"click\",(t=>{t.stopPropagation(),t.preventDefault(),controls.toggleMenu.call(e,t)}),null,!1),this.bind(t.buttons.settings,\"keyup\",(t=>{[\" \",\"Enter\"].includes(t.key)&&(\"Enter\"!==t.key?(t.preventDefault(),t.stopPropagation(),controls.toggleMenu.call(e,t)):controls.focusFirstMenuItem.call(e,null,!0))}),null,!1),this.bind(t.settings.menu,\"keydown\",(t=>{\"Escape\"===t.key&&controls.toggleMenu.call(e,t)})),this.bind(t.inputs.seek,\"mousedown mousemove\",(e=>{const i=t.progress.getBoundingClientRect(),s=100/i.width*(e.pageX-i.left);e.currentTarget.setAttribute(\"seek-value\",s)})),this.bind(t.inputs.seek,\"mousedown mouseup keydown keyup touchstart touchend\",(t=>{const i=t.currentTarget,s=\"play-on-seeked\";if(is.keyboardEvent(t)&&![\"ArrowLeft\",\"ArrowRight\"].includes(t.key))return;e.lastSeekTime=Date.now();const n=i.hasAttribute(s),r=[\"mouseup\",\"touchend\",\"keyup\"].includes(t.type);n&&r?(i.removeAttribute(s),silencePromise(e.play())):!r&&e.playing&&(i.setAttribute(s,\"\"),e.pause())})),browser.isIos){const t=getElements.call(e,'input[type=\"range\"]');Array.from(t).forEach((e=>this.bind(e,i,(e=>repaint(e.target)))))}this.bind(t.inputs.seek,i,(t=>{const i=t.currentTarget;let s=i.getAttribute(\"seek-value\");is.empty(s)&&(s=i.value),i.removeAttribute(\"seek-value\"),e.currentTime=s/i.max*e.duration}),\"seek\"),this.bind(t.progress,\"mouseenter mouseleave mousemove\",(t=>controls.updateSeekTooltip.call(e,t))),this.bind(t.progress,\"mousemove touchmove\",(t=>{const{previewThumbnails:i}=e;i&&i.loaded&&i.startMove(t)})),this.bind(t.progress,\"mouseleave touchend click\",(()=>{const{previewThumbnails:t}=e;t&&t.loaded&&t.endMove(!1,!0)})),this.bind(t.progress,\"mousedown touchstart\",(t=>{const{previewThumbnails:i}=e;i&&i.loaded&&i.startScrubbing(t)})),this.bind(t.progress,\"mouseup touchend\",(t=>{const{previewThumbnails:i}=e;i&&i.loaded&&i.endScrubbing(t)})),browser.isWebKit&&Array.from(getElements.call(e,'input[type=\"range\"]')).forEach((t=>{this.bind(t,\"input\",(t=>controls.updateRangeFill.call(e,t.target)))})),e.config.toggleInvert&&!is.element(t.display.duration)&&this.bind(t.display.currentTime,\"click\",(()=>{0!==e.currentTime&&(e.config.invertTime=!e.config.invertTime,controls.timeUpdate.call(e))})),this.bind(t.inputs.volume,i,(t=>{e.volume=t.target.value}),\"volume\"),this.bind(t.controls,\"mouseenter mouseleave\",(i=>{t.controls.hover=!e.touch&&\"mouseenter\"===i.type})),t.fullscreen&&Array.from(t.fullscreen.children).filter((e=>!e.contains(t.container))).forEach((i=>{this.bind(i,\"mouseenter mouseleave\",(i=>{t.controls&&(t.controls.hover=!e.touch&&\"mouseenter\"===i.type)}))})),this.bind(t.controls,\"mousedown mouseup touchstart touchend touchcancel\",(e=>{t.controls.pressed=[\"mousedown\",\"touchstart\"].includes(e.type)})),this.bind(t.controls,\"focusin\",(()=>{const{config:i,timers:s}=e;toggleClass(t.controls,i.classNames.noTransition,!0),ui.toggleControls.call(e,!0),setTimeout((()=>{toggleClass(t.controls,i.classNames.noTransition,!1)}),0);const n=this.touch?3e3:4e3;clearTimeout(s.controls),s.controls=setTimeout((()=>ui.toggleControls.call(e,!1)),n)})),this.bind(t.inputs.volume,\"wheel\",(t=>{const i=t.webkitDirectionInvertedFromDevice,[s,n]=[t.deltaX,-t.deltaY].map((e=>i?-e:e)),r=Math.sign(Math.abs(s)>Math.abs(n)?s:n);e.increaseVolume(r/50);const{volume:a}=e.media;(1===r&&a<1||-1===r&&a>0)&&t.preventDefault()}),\"volume\",!1)})),this.player=e,this.lastKey=null,this.focusTimer=null,this.lastKeyDown=null,this.handleKey=this.handleKey.bind(this),this.toggleMenu=this.toggleMenu.bind(this),this.firstTouch=this.firstTouch.bind(this)}handleKey(e){const{player:t}=this,{elements:i}=t,{key:s,type:n,altKey:r,ctrlKey:a,metaKey:o,shiftKey:l}=e,c=\"keydown\"===n,u=c&&s===this.lastKey;if(r||a||o||l)return;if(!s)return;if(c){const n=document.activeElement;if(is.element(n)){const{editable:s}=t.config.selectors,{seek:r}=i.inputs;if(n!==r&&matches(n,s))return;if(\" \"===e.key&&matches(n,'button, [role^=\"menuitem\"]'))return}switch([\" \",\"ArrowLeft\",\"ArrowUp\",\"ArrowRight\",\"ArrowDown\",\"0\",\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"c\",\"f\",\"k\",\"l\",\"m\"].includes(s)&&(e.preventDefault(),e.stopPropagation()),s){case\"0\":case\"1\":case\"2\":case\"3\":case\"4\":case\"5\":case\"6\":case\"7\":case\"8\":case\"9\":u||(d=parseInt(s,10),t.currentTime=t.duration/10*d);break;case\" \":case\"k\":u||silencePromise(t.togglePlay());break;case\"ArrowUp\":t.increaseVolume(.1);break;case\"ArrowDown\":t.decreaseVolume(.1);break;case\"m\":u||(t.muted=!t.muted);break;case\"ArrowRight\":t.forward();break;case\"ArrowLeft\":t.rewind();break;case\"f\":t.fullscreen.toggle();break;case\"c\":u||t.toggleCaptions();break;case\"l\":t.loop=!t.loop}\"Escape\"===s&&!t.fullscreen.usingNative&&t.fullscreen.active&&t.fullscreen.toggle(),this.lastKey=s}else this.lastKey=null;var d}toggleMenu(e){controls.toggleMenu.call(this.player,e)}}var commonjsGlobal=\"undefined\"!=typeof globalThis?globalThis:\"undefined\"!=typeof window?window:\"undefined\"!=typeof global?global:\"undefined\"!=typeof self?self:{};function createCommonjsModule(e,t){return e(t={exports:{}},t.exports),t.exports}var loadjs_umd=createCommonjsModule((function(e,t){e.exports=function(){var e=function(){},t={},i={},s={};function n(e,t){e=e.push?e:[e];var n,r,a,o=[],l=e.length,c=l;for(n=function(e,i){i.length&&o.push(e),--c||t(o)};l--;)r=e[l],(a=i[r])?n(r,a):(s[r]=s[r]||[]).push(n)}function r(e,t){if(e){var n=s[e];if(i[e]=t,n)for(;n.length;)n[0](e,t),n.splice(0,1)}}function a(t,i){t.call&&(t={success:t}),i.length?(t.error||e)(i):(t.success||e)(t)}function o(t,i,s,n){var r,a,l=document,c=s.async,u=(s.numRetries||0)+1,d=s.before||e,h=t.replace(/[\\?|#].*$/,\"\"),m=t.replace(/^(css|img)!/,\"\");n=n||0,/(^css!|\\.css$)/.test(h)?((a=l.createElement(\"link\")).rel=\"stylesheet\",a.href=m,(r=\"hideFocus\"in a)&&a.relList&&(r=0,a.rel=\"preload\",a.as=\"style\")):/(^img!|\\.(png|gif|jpg|svg|webp)$)/.test(h)?(a=l.createElement(\"img\")).src=m:((a=l.createElement(\"script\")).src=t,a.async=void 0===c||c),a.onload=a.onerror=a.onbeforeload=function(e){var l=e.type[0];if(r)try{a.sheet.cssText.length||(l=\"e\")}catch(e){18!=e.code&&(l=\"e\")}if(\"e\"==l){if((n+=1)<u)return o(t,i,s,n)}else if(\"preload\"==a.rel&&\"style\"==a.as)return a.rel=\"stylesheet\";i(t,l,e.defaultPrevented)},!1!==d(t,a)&&l.head.appendChild(a)}function l(e,t,i){var s,n,r=(e=e.push?e:[e]).length,a=r,l=[];for(s=function(e,i,s){if(\"e\"==i&&l.push(e),\"b\"==i){if(!s)return;l.push(e)}--r||t(l)},n=0;n<a;n++)o(e[n],s,i)}function c(e,i,s){var n,o;if(i&&i.trim&&(n=i),o=(n?s:i)||{},n){if(n in t)throw\"LoadJS\";t[n]=!0}function c(t,i){l(e,(function(e){a(o,e),t&&a({success:t,error:i},e),r(n,e)}),o)}if(o.returnPromise)return new Promise(c);c()}return c.ready=function(e,t){return n(e,(function(e){a(t,e)})),c},c.done=function(e){r(e,[])},c.reset=function(){t={},i={},s={}},c.isDefined=function(e){return e in t},c}()}));function loadScript(e){return new Promise(((t,i)=>{loadjs_umd(e,{success:t,error:i})}))}function parseId$1(e){if(is.empty(e))return null;if(is.number(Number(e)))return e;return e.match(/^.*(vimeo.com\\/|video\\/)(\\d+).*/)?RegExp.$2:e}function parseHash(e){const t=e.match(/^.*(vimeo.com\\/|video\\/)(\\d+)(\\?.*&*h=|\\/)+([\\d,a-f]+)/);return t&&5===t.length?t[4]:null}function assurePlaybackState$1(e){e&&!this.embed.hasPlayed&&(this.embed.hasPlayed=!0),this.media.paused===e&&(this.media.paused=!e,triggerEvent.call(this,this.media,e?\"play\":\"pause\"))}const vimeo={setup(){const e=this;toggleClass(e.elements.wrapper,e.config.classNames.embed,!0),e.options.speed=e.config.speed.options,setAspectRatio.call(e),is.object(window.Vimeo)?vimeo.ready.call(e):loadScript(e.config.urls.vimeo.sdk).then((()=>{vimeo.ready.call(e)})).catch((t=>{e.debug.warn(\"Vimeo SDK (player.js) failed to load\",t)}))},ready(){const e=this,t=e.config.vimeo,{premium:i,referrerPolicy:s,...n}=t;let r=e.media.getAttribute(\"src\"),a=\"\";is.empty(r)?(r=e.media.getAttribute(e.config.attributes.embed.id),a=e.media.getAttribute(e.config.attributes.embed.hash)):a=parseHash(r);const o=a?{h:a}:{};i&&Object.assign(n,{controls:!1,sidedock:!1});const l=buildUrlParams({loop:e.config.loop.active,autoplay:e.autoplay,muted:e.muted,gesture:\"media\",playsinline:e.config.playsinline,...o,...n}),c=parseId$1(r),u=createElement(\"iframe\"),d=format(e.config.urls.vimeo.iframe,c,l);if(u.setAttribute(\"src\",d),u.setAttribute(\"allowfullscreen\",\"\"),u.setAttribute(\"allow\",[\"autoplay\",\"fullscreen\",\"picture-in-picture\",\"encrypted-media\",\"accelerometer\",\"gyroscope\"].join(\"; \")),is.empty(s)||u.setAttribute(\"referrerPolicy\",s),i||!t.customControls)u.setAttribute(\"data-poster\",e.poster),e.media=replaceElement(u,e.media);else{const t=createElement(\"div\",{class:e.config.classNames.embedContainer,\"data-poster\":e.poster});t.appendChild(u),e.media=replaceElement(t,e.media)}t.customControls||fetch(format(e.config.urls.vimeo.api,d)).then((t=>{!is.empty(t)&&t.thumbnail_url&&ui.setPoster.call(e,t.thumbnail_url).catch((()=>{}))})),e.embed=new window.Vimeo.Player(u,{autopause:e.config.autopause,muted:e.muted}),e.media.paused=!0,e.media.currentTime=0,e.supported.ui&&e.embed.disableTextTrack(),e.media.play=()=>(assurePlaybackState$1.call(e,!0),e.embed.play()),e.media.pause=()=>(assurePlaybackState$1.call(e,!1),e.embed.pause()),e.media.stop=()=>{e.pause(),e.currentTime=0};let{currentTime:h}=e.media;Object.defineProperty(e.media,\"currentTime\",{get:()=>h,set(t){const{embed:i,media:s,paused:n,volume:r}=e,a=n&&!i.hasPlayed;s.seeking=!0,triggerEvent.call(e,s,\"seeking\"),Promise.resolve(a&&i.setVolume(0)).then((()=>i.setCurrentTime(t))).then((()=>a&&i.pause())).then((()=>a&&i.setVolume(r))).catch((()=>{}))}});let m=e.config.speed.selected;Object.defineProperty(e.media,\"playbackRate\",{get:()=>m,set(t){e.embed.setPlaybackRate(t).then((()=>{m=t,triggerEvent.call(e,e.media,\"ratechange\")})).catch((()=>{e.options.speed=[1]}))}});let{volume:p}=e.config;Object.defineProperty(e.media,\"volume\",{get:()=>p,set(t){e.embed.setVolume(t).then((()=>{p=t,triggerEvent.call(e,e.media,\"volumechange\")}))}});let{muted:g}=e.config;Object.defineProperty(e.media,\"muted\",{get:()=>g,set(t){const i=!!is.boolean(t)&&t;e.embed.setMuted(!!i||e.config.muted).then((()=>{g=i,triggerEvent.call(e,e.media,\"volumechange\")}))}});let f,{loop:y}=e.config;Object.defineProperty(e.media,\"loop\",{get:()=>y,set(t){const i=is.boolean(t)?t:e.config.loop.active;e.embed.setLoop(i).then((()=>{y=i}))}}),e.embed.getVideoUrl().then((t=>{f=t,controls.setDownloadUrl.call(e)})).catch((e=>{this.debug.warn(e)})),Object.defineProperty(e.media,\"currentSrc\",{get:()=>f}),Object.defineProperty(e.media,\"ended\",{get:()=>e.currentTime===e.duration}),Promise.all([e.embed.getVideoWidth(),e.embed.getVideoHeight()]).then((t=>{const[i,s]=t;e.embed.ratio=roundAspectRatio(i,s),setAspectRatio.call(this)})),e.embed.setAutopause(e.config.autopause).then((t=>{e.config.autopause=t})),e.embed.getVideoTitle().then((t=>{e.config.title=t,ui.setTitle.call(this)})),e.embed.getCurrentTime().then((t=>{h=t,triggerEvent.call(e,e.media,\"timeupdate\")})),e.embed.getDuration().then((t=>{e.media.duration=t,triggerEvent.call(e,e.media,\"durationchange\")})),e.embed.getTextTracks().then((t=>{e.media.textTracks=t,captions.setup.call(e)})),e.embed.on(\"cuechange\",(({cues:t=[]})=>{const i=t.map((e=>stripHTML(e.text)));captions.updateCues.call(e,i)})),e.embed.on(\"loaded\",(()=>{if(e.embed.getPaused().then((t=>{assurePlaybackState$1.call(e,!t),t||triggerEvent.call(e,e.media,\"playing\")})),is.element(e.embed.element)&&e.supported.ui){e.embed.element.setAttribute(\"tabindex\",-1)}})),e.embed.on(\"bufferstart\",(()=>{triggerEvent.call(e,e.media,\"waiting\")})),e.embed.on(\"bufferend\",(()=>{triggerEvent.call(e,e.media,\"playing\")})),e.embed.on(\"play\",(()=>{assurePlaybackState$1.call(e,!0),triggerEvent.call(e,e.media,\"playing\")})),e.embed.on(\"pause\",(()=>{assurePlaybackState$1.call(e,!1)})),e.embed.on(\"timeupdate\",(t=>{e.media.seeking=!1,h=t.seconds,triggerEvent.call(e,e.media,\"timeupdate\")})),e.embed.on(\"progress\",(t=>{e.media.buffered=t.percent,triggerEvent.call(e,e.media,\"progress\"),1===parseInt(t.percent,10)&&triggerEvent.call(e,e.media,\"canplaythrough\"),e.embed.getDuration().then((t=>{t!==e.media.duration&&(e.media.duration=t,triggerEvent.call(e,e.media,\"durationchange\"))}))})),e.embed.on(\"seeked\",(()=>{e.media.seeking=!1,triggerEvent.call(e,e.media,\"seeked\")})),e.embed.on(\"ended\",(()=>{e.media.paused=!0,triggerEvent.call(e,e.media,\"ended\")})),e.embed.on(\"error\",(t=>{e.media.error=t,triggerEvent.call(e,e.media,\"error\")})),t.customControls&&setTimeout((()=>ui.build.call(e)),0)}};function parseId(e){if(is.empty(e))return null;return e.match(/^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|&v=)([^#&?]*).*/)?RegExp.$2:e}function assurePlaybackState(e){e&&!this.embed.hasPlayed&&(this.embed.hasPlayed=!0),this.media.paused===e&&(this.media.paused=!e,triggerEvent.call(this,this.media,e?\"play\":\"pause\"))}function getHost(e){return e.noCookie?\"https://www.youtube-nocookie.com\":\"http:\"===window.location.protocol?\"http://www.youtube.com\":void 0}const youtube={setup(){if(toggleClass(this.elements.wrapper,this.config.classNames.embed,!0),is.object(window.YT)&&is.function(window.YT.Player))youtube.ready.call(this);else{const e=window.onYouTubeIframeAPIReady;window.onYouTubeIframeAPIReady=()=>{is.function(e)&&e(),youtube.ready.call(this)},loadScript(this.config.urls.youtube.sdk).catch((e=>{this.debug.warn(\"YouTube API failed to load\",e)}))}},getTitle(e){fetch(format(this.config.urls.youtube.api,e)).then((e=>{if(is.object(e)){const{title:t,height:i,width:s}=e;this.config.title=t,ui.setTitle.call(this),this.embed.ratio=roundAspectRatio(s,i)}setAspectRatio.call(this)})).catch((()=>{setAspectRatio.call(this)}))},ready(){const e=this,t=e.config.youtube,i=e.media&&e.media.getAttribute(\"id\");if(!is.empty(i)&&i.startsWith(\"youtube-\"))return;let s=e.media.getAttribute(\"src\");is.empty(s)&&(s=e.media.getAttribute(this.config.attributes.embed.id));const n=parseId(s),r=createElement(\"div\",{id:generateId(e.provider),\"data-poster\":t.customControls?e.poster:void 0});if(e.media=replaceElement(r,e.media),t.customControls){const t=e=>`https://i.ytimg.com/vi/${n}/${e}default.jpg`;loadImage(t(\"maxres\"),121).catch((()=>loadImage(t(\"sd\"),121))).catch((()=>loadImage(t(\"hq\")))).then((t=>ui.setPoster.call(e,t.src))).then((t=>{t.includes(\"maxres\")||(e.elements.poster.style.backgroundSize=\"cover\")})).catch((()=>{}))}e.embed=new window.YT.Player(e.media,{videoId:n,host:getHost(t),playerVars:extend({},{autoplay:e.config.autoplay?1:0,hl:e.config.hl,controls:e.supported.ui&&t.customControls?0:1,disablekb:1,playsinline:e.config.playsinline&&!e.config.fullscreen.iosNative?1:0,cc_load_policy:e.captions.active?1:0,cc_lang_pref:e.config.captions.language,widget_referrer:window?window.location.href:null},t),events:{onError(t){if(!e.media.error){const i=t.data,s={2:\"The request contains an invalid parameter value. For example, this error occurs if you specify a video ID that does not have 11 characters, or if the video ID contains invalid characters, such as exclamation points or asterisks.\",5:\"The requested content cannot be played in an HTML5 player or another error related to the HTML5 player has occurred.\",100:\"The video requested was not found. This error occurs when a video has been removed (for any reason) or has been marked as private.\",101:\"The owner of the requested video does not allow it to be played in embedded players.\",150:\"The owner of the requested video does not allow it to be played in embedded players.\"}[i]||\"An unknown error occurred\";e.media.error={code:i,message:s},triggerEvent.call(e,e.media,\"error\")}},onPlaybackRateChange(t){const i=t.target;e.media.playbackRate=i.getPlaybackRate(),triggerEvent.call(e,e.media,\"ratechange\")},onReady(i){if(is.function(e.media.play))return;const s=i.target;youtube.getTitle.call(e,n),e.media.play=()=>{assurePlaybackState.call(e,!0),s.playVideo()},e.media.pause=()=>{assurePlaybackState.call(e,!1),s.pauseVideo()},e.media.stop=()=>{s.stopVideo()},e.media.duration=s.getDuration(),e.media.paused=!0,e.media.currentTime=0,Object.defineProperty(e.media,\"currentTime\",{get:()=>Number(s.getCurrentTime()),set(t){e.paused&&!e.embed.hasPlayed&&e.embed.mute(),e.media.seeking=!0,triggerEvent.call(e,e.media,\"seeking\"),s.seekTo(t)}}),Object.defineProperty(e.media,\"playbackRate\",{get:()=>s.getPlaybackRate(),set(e){s.setPlaybackRate(e)}});let{volume:r}=e.config;Object.defineProperty(e.media,\"volume\",{get:()=>r,set(t){r=t,s.setVolume(100*r),triggerEvent.call(e,e.media,\"volumechange\")}});let{muted:a}=e.config;Object.defineProperty(e.media,\"muted\",{get:()=>a,set(t){const i=is.boolean(t)?t:a;a=i,s[i?\"mute\":\"unMute\"](),s.setVolume(100*r),triggerEvent.call(e,e.media,\"volumechange\")}}),Object.defineProperty(e.media,\"currentSrc\",{get:()=>s.getVideoUrl()}),Object.defineProperty(e.media,\"ended\",{get:()=>e.currentTime===e.duration});const o=s.getAvailablePlaybackRates();e.options.speed=o.filter((t=>e.config.speed.options.includes(t))),e.supported.ui&&t.customControls&&e.media.setAttribute(\"tabindex\",-1),triggerEvent.call(e,e.media,\"timeupdate\"),triggerEvent.call(e,e.media,\"durationchange\"),clearInterval(e.timers.buffering),e.timers.buffering=setInterval((()=>{e.media.buffered=s.getVideoLoadedFraction(),(null===e.media.lastBuffered||e.media.lastBuffered<e.media.buffered)&&triggerEvent.call(e,e.media,\"progress\"),e.media.lastBuffered=e.media.buffered,1===e.media.buffered&&(clearInterval(e.timers.buffering),triggerEvent.call(e,e.media,\"canplaythrough\"))}),200),t.customControls&&setTimeout((()=>ui.build.call(e)),50)},onStateChange(i){const s=i.target;clearInterval(e.timers.playing);switch(e.media.seeking&&[1,2].includes(i.data)&&(e.media.seeking=!1,triggerEvent.call(e,e.media,\"seeked\")),i.data){case-1:triggerEvent.call(e,e.media,\"timeupdate\"),e.media.buffered=s.getVideoLoadedFraction(),triggerEvent.call(e,e.media,\"progress\");break;case 0:assurePlaybackState.call(e,!1),e.media.loop?(s.stopVideo(),s.playVideo()):triggerEvent.call(e,e.media,\"ended\");break;case 1:t.customControls&&!e.config.autoplay&&e.media.paused&&!e.embed.hasPlayed?e.media.pause():(assurePlaybackState.call(e,!0),triggerEvent.call(e,e.media,\"playing\"),e.timers.playing=setInterval((()=>{triggerEvent.call(e,e.media,\"timeupdate\")}),50),e.media.duration!==s.getDuration()&&(e.media.duration=s.getDuration(),triggerEvent.call(e,e.media,\"durationchange\")));break;case 2:e.muted||e.embed.unMute(),assurePlaybackState.call(e,!1);break;case 3:triggerEvent.call(e,e.media,\"waiting\")}triggerEvent.call(e,e.elements.container,\"statechange\",!1,{code:i.data})}}})}},media={setup(){this.media?(toggleClass(this.elements.container,this.config.classNames.type.replace(\"{0}\",this.type),!0),toggleClass(this.elements.container,this.config.classNames.provider.replace(\"{0}\",this.provider),!0),this.isEmbed&&toggleClass(this.elements.container,this.config.classNames.type.replace(\"{0}\",\"video\"),!0),this.isVideo&&(this.elements.wrapper=createElement(\"div\",{class:this.config.classNames.video}),wrap(this.media,this.elements.wrapper),this.elements.poster=createElement(\"div\",{class:this.config.classNames.poster}),this.elements.wrapper.appendChild(this.elements.poster)),this.isHTML5?html5.setup.call(this):this.isYouTube?youtube.setup.call(this):this.isVimeo&&vimeo.setup.call(this)):this.debug.warn(\"No media element found!\")}},destroy=e=>{e.manager&&e.manager.destroy(),e.elements.displayContainer&&e.elements.displayContainer.destroy(),e.elements.container.remove()};class Ads{constructor(e){_defineProperty$1(this,\"load\",(()=>{this.enabled&&(is.object(window.google)&&is.object(window.google.ima)?this.ready():loadScript(this.player.config.urls.googleIMA.sdk).then((()=>{this.ready()})).catch((()=>{this.trigger(\"error\",new Error(\"Google IMA SDK failed to load\"))})))})),_defineProperty$1(this,\"ready\",(()=>{var e;this.enabled||((e=this).manager&&e.manager.destroy(),e.elements.displayContainer&&e.elements.displayContainer.destroy(),e.elements.container.remove()),this.startSafetyTimer(12e3,\"ready()\"),this.managerPromise.then((()=>{this.clearSafetyTimer(\"onAdsManagerLoaded()\")})),this.listeners(),this.setupIMA()})),_defineProperty$1(this,\"setupIMA\",(()=>{this.elements.container=createElement(\"div\",{class:this.player.config.classNames.ads}),this.player.elements.container.appendChild(this.elements.container),google.ima.settings.setVpaidMode(google.ima.ImaSdkSettings.VpaidMode.ENABLED),google.ima.settings.setLocale(this.player.config.ads.language),google.ima.settings.setDisableCustomPlaybackForIOS10Plus(this.player.config.playsinline),this.elements.displayContainer=new google.ima.AdDisplayContainer(this.elements.container,this.player.media),this.loader=new google.ima.AdsLoader(this.elements.displayContainer),this.loader.addEventListener(google.ima.AdsManagerLoadedEvent.Type.ADS_MANAGER_LOADED,(e=>this.onAdsManagerLoaded(e)),!1),this.loader.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,(e=>this.onAdError(e)),!1),this.requestAds()})),_defineProperty$1(this,\"requestAds\",(()=>{const{container:e}=this.player.elements;try{const t=new google.ima.AdsRequest;t.adTagUrl=this.tagUrl,t.linearAdSlotWidth=e.offsetWidth,t.linearAdSlotHeight=e.offsetHeight,t.nonLinearAdSlotWidth=e.offsetWidth,t.nonLinearAdSlotHeight=e.offsetHeight,t.forceNonLinearFullSlot=!1,t.setAdWillPlayMuted(!this.player.muted),this.loader.requestAds(t)}catch(e){this.onAdError(e)}})),_defineProperty$1(this,\"pollCountdown\",((e=!1)=>{if(!e)return clearInterval(this.countdownTimer),void this.elements.container.removeAttribute(\"data-badge-text\");this.countdownTimer=setInterval((()=>{const e=formatTime(Math.max(this.manager.getRemainingTime(),0)),t=`${i18n.get(\"advertisement\",this.player.config)} - ${e}`;this.elements.container.setAttribute(\"data-badge-text\",t)}),100)})),_defineProperty$1(this,\"onAdsManagerLoaded\",(e=>{if(!this.enabled)return;const t=new google.ima.AdsRenderingSettings;t.restoreCustomPlaybackStateOnAdBreakComplete=!0,t.enablePreloading=!0,this.manager=e.getAdsManager(this.player,t),this.cuePoints=this.manager.getCuePoints(),this.manager.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,(e=>this.onAdError(e))),Object.keys(google.ima.AdEvent.Type).forEach((e=>{this.manager.addEventListener(google.ima.AdEvent.Type[e],(e=>this.onAdEvent(e)))})),this.trigger(\"loaded\")})),_defineProperty$1(this,\"addCuePoints\",(()=>{is.empty(this.cuePoints)||this.cuePoints.forEach((e=>{if(0!==e&&-1!==e&&e<this.player.duration){const t=this.player.elements.progress;if(is.element(t)){const i=100/this.player.duration*e,s=createElement(\"span\",{class:this.player.config.classNames.cues});s.style.left=`${i.toString()}%`,t.appendChild(s)}}}))})),_defineProperty$1(this,\"onAdEvent\",(e=>{const{container:t}=this.player.elements,i=e.getAd(),s=e.getAdData();switch((e=>{triggerEvent.call(this.player,this.player.media,`ads${e.replace(/_/g,\"\").toLowerCase()}`)})(e.type),e.type){case google.ima.AdEvent.Type.LOADED:this.trigger(\"loaded\"),this.pollCountdown(!0),i.isLinear()||(i.width=t.offsetWidth,i.height=t.offsetHeight);break;case google.ima.AdEvent.Type.STARTED:this.manager.setVolume(this.player.volume);break;case google.ima.AdEvent.Type.ALL_ADS_COMPLETED:this.player.ended?this.loadAds():this.loader.contentComplete();break;case google.ima.AdEvent.Type.CONTENT_PAUSE_REQUESTED:this.pauseContent();break;case google.ima.AdEvent.Type.CONTENT_RESUME_REQUESTED:this.pollCountdown(),this.resumeContent();break;case google.ima.AdEvent.Type.LOG:s.adError&&this.player.debug.warn(`Non-fatal ad error: ${s.adError.getMessage()}`)}})),_defineProperty$1(this,\"onAdError\",(e=>{this.cancel(),this.player.debug.warn(\"Ads error\",e)})),_defineProperty$1(this,\"listeners\",(()=>{const{container:e}=this.player.elements;let t;this.player.on(\"canplay\",(()=>{this.addCuePoints()})),this.player.on(\"ended\",(()=>{this.loader.contentComplete()})),this.player.on(\"timeupdate\",(()=>{t=this.player.currentTime})),this.player.on(\"seeked\",(()=>{const e=this.player.currentTime;is.empty(this.cuePoints)||this.cuePoints.forEach(((i,s)=>{t<i&&i<e&&(this.manager.discardAdBreak(),this.cuePoints.splice(s,1))}))})),window.addEventListener(\"resize\",(()=>{this.manager&&this.manager.resize(e.offsetWidth,e.offsetHeight,google.ima.ViewMode.NORMAL)}))})),_defineProperty$1(this,\"play\",(()=>{const{container:e}=this.player.elements;this.managerPromise||this.resumeContent(),this.managerPromise.then((()=>{this.manager.setVolume(this.player.volume),this.elements.displayContainer.initialize();try{this.initialized||(this.manager.init(e.offsetWidth,e.offsetHeight,google.ima.ViewMode.NORMAL),this.manager.start()),this.initialized=!0}catch(e){this.onAdError(e)}})).catch((()=>{}))})),_defineProperty$1(this,\"resumeContent\",(()=>{this.elements.container.style.zIndex=\"\",this.playing=!1,silencePromise(this.player.media.play())})),_defineProperty$1(this,\"pauseContent\",(()=>{this.elements.container.style.zIndex=3,this.playing=!0,this.player.media.pause()})),_defineProperty$1(this,\"cancel\",(()=>{this.initialized&&this.resumeContent(),this.trigger(\"error\"),this.loadAds()})),_defineProperty$1(this,\"loadAds\",(()=>{this.managerPromise.then((()=>{this.manager&&this.manager.destroy(),this.managerPromise=new Promise((e=>{this.on(\"loaded\",e),this.player.debug.log(this.manager)})),this.initialized=!1,this.requestAds()})).catch((()=>{}))})),_defineProperty$1(this,\"trigger\",((e,...t)=>{const i=this.events[e];is.array(i)&&i.forEach((e=>{is.function(e)&&e.apply(this,t)}))})),_defineProperty$1(this,\"on\",((e,t)=>(is.array(this.events[e])||(this.events[e]=[]),this.events[e].push(t),this))),_defineProperty$1(this,\"startSafetyTimer\",((e,t)=>{this.player.debug.log(`Safety timer invoked from: ${t}`),this.safetyTimer=setTimeout((()=>{this.cancel(),this.clearSafetyTimer(\"startSafetyTimer()\")}),e)})),_defineProperty$1(this,\"clearSafetyTimer\",(e=>{is.nullOrUndefined(this.safetyTimer)||(this.player.debug.log(`Safety timer cleared from: ${e}`),clearTimeout(this.safetyTimer),this.safetyTimer=null)})),this.player=e,this.config=e.config.ads,this.playing=!1,this.initialized=!1,this.elements={container:null,displayContainer:null},this.manager=null,this.loader=null,this.cuePoints=null,this.events={},this.safetyTimer=null,this.countdownTimer=null,this.managerPromise=new Promise(((e,t)=>{this.on(\"loaded\",e),this.on(\"error\",t)})),this.load()}get enabled(){const{config:e}=this;return this.player.isHTML5&&this.player.isVideo&&e.enabled&&(!is.empty(e.publisherId)||is.url(e.tagUrl))}get tagUrl(){const{config:e}=this;if(is.url(e.tagUrl))return e.tagUrl;return`https://go.aniview.com/api/adserver6/vast/?${buildUrlParams({AV_PUBLISHERID:\"58c25bb0073ef448b1087ad6\",AV_CHANNELID:\"5a0458dc28a06145e4519d21\",AV_URL:window.location.hostname,cb:Date.now(),AV_WIDTH:640,AV_HEIGHT:480,AV_CDIM2:e.publisherId})}`}}function clamp(e=0,t=0,i=255){return Math.min(Math.max(e,t),i)}const parseVtt=e=>{const t=[];return e.split(/\\r\\n\\r\\n|\\n\\n|\\r\\r/).forEach((e=>{const i={};e.split(/\\r\\n|\\n|\\r/).forEach((e=>{if(is.number(i.startTime)){if(!is.empty(e.trim())&&is.empty(i.text)){const t=e.trim().split(\"#xywh=\");[i.text]=t,t[1]&&([i.x,i.y,i.w,i.h]=t[1].split(\",\"))}}else{const t=e.match(/([0-9]{2})?:?([0-9]{2}):([0-9]{2}).([0-9]{2,3})( ?--> ?)([0-9]{2})?:?([0-9]{2}):([0-9]{2}).([0-9]{2,3})/);t&&(i.startTime=60*Number(t[1]||0)*60+60*Number(t[2])+Number(t[3])+Number(`0.${t[4]}`),i.endTime=60*Number(t[6]||0)*60+60*Number(t[7])+Number(t[8])+Number(`0.${t[9]}`))}})),i.text&&t.push(i)})),t},fitRatio=(e,t)=>{const i={};return e>t.width/t.height?(i.width=t.width,i.height=1/e*t.width):(i.height=t.height,i.width=e*t.height),i};class PreviewThumbnails{constructor(e){_defineProperty$1(this,\"load\",(()=>{this.player.elements.display.seekTooltip&&(this.player.elements.display.seekTooltip.hidden=this.enabled),this.enabled&&this.getThumbnails().then((()=>{this.enabled&&(this.render(),this.determineContainerAutoSizing(),this.listeners(),this.loaded=!0)}))})),_defineProperty$1(this,\"getThumbnails\",(()=>new Promise((e=>{const{src:t}=this.player.config.previewThumbnails;if(is.empty(t))throw new Error(\"Missing previewThumbnails.src config attribute\");const i=()=>{this.thumbnails.sort(((e,t)=>e.height-t.height)),this.player.debug.log(\"Preview thumbnails\",this.thumbnails),e()};if(is.function(t))t((e=>{this.thumbnails=e,i()}));else{const e=(is.string(t)?[t]:t).map((e=>this.getThumbnail(e)));Promise.all(e).then(i)}})))),_defineProperty$1(this,\"getThumbnail\",(e=>new Promise((t=>{fetch(e).then((i=>{const s={frames:parseVtt(i),height:null,urlPrefix:\"\"};s.frames[0].text.startsWith(\"/\")||s.frames[0].text.startsWith(\"http://\")||s.frames[0].text.startsWith(\"https://\")||(s.urlPrefix=e.substring(0,e.lastIndexOf(\"/\")+1));const n=new Image;n.onload=()=>{s.height=n.naturalHeight,s.width=n.naturalWidth,this.thumbnails.push(s),t()},n.src=s.urlPrefix+s.frames[0].text}))})))),_defineProperty$1(this,\"startMove\",(e=>{if(this.loaded&&is.event(e)&&[\"touchmove\",\"mousemove\"].includes(e.type)&&this.player.media.duration){if(\"touchmove\"===e.type)this.seekTime=this.player.media.duration*(this.player.elements.inputs.seek.value/100);else{var t,i;const s=this.player.elements.progress.getBoundingClientRect(),n=100/s.width*(e.pageX-s.left);this.seekTime=this.player.media.duration*(n/100),this.seekTime<0&&(this.seekTime=0),this.seekTime>this.player.media.duration-1&&(this.seekTime=this.player.media.duration-1),this.mousePosX=e.pageX,this.elements.thumb.time.innerText=formatTime(this.seekTime);const r=null===(t=this.player.config.markers)||void 0===t||null===(i=t.points)||void 0===i?void 0:i.find((({time:e})=>e===Math.round(this.seekTime)));r&&this.elements.thumb.time.insertAdjacentHTML(\"afterbegin\",`${r.label}<br>`)}this.showImageAtCurrentTime()}})),_defineProperty$1(this,\"endMove\",(()=>{this.toggleThumbContainer(!1,!0)})),_defineProperty$1(this,\"startScrubbing\",(e=>{(is.nullOrUndefined(e.button)||!1===e.button||0===e.button)&&(this.mouseDown=!0,this.player.media.duration&&(this.toggleScrubbingContainer(!0),this.toggleThumbContainer(!1,!0),this.showImageAtCurrentTime()))})),_defineProperty$1(this,\"endScrubbing\",(()=>{this.mouseDown=!1,Math.ceil(this.lastTime)===Math.ceil(this.player.media.currentTime)?this.toggleScrubbingContainer(!1):once.call(this.player,this.player.media,\"timeupdate\",(()=>{this.mouseDown||this.toggleScrubbingContainer(!1)}))})),_defineProperty$1(this,\"listeners\",(()=>{this.player.on(\"play\",(()=>{this.toggleThumbContainer(!1,!0)})),this.player.on(\"seeked\",(()=>{this.toggleThumbContainer(!1)})),this.player.on(\"timeupdate\",(()=>{this.lastTime=this.player.media.currentTime}))})),_defineProperty$1(this,\"render\",(()=>{this.elements.thumb.container=createElement(\"div\",{class:this.player.config.classNames.previewThumbnails.thumbContainer}),this.elements.thumb.imageContainer=createElement(\"div\",{class:this.player.config.classNames.previewThumbnails.imageContainer}),this.elements.thumb.container.appendChild(this.elements.thumb.imageContainer);const e=createElement(\"div\",{class:this.player.config.classNames.previewThumbnails.timeContainer});this.elements.thumb.time=createElement(\"span\",{},\"00:00\"),e.appendChild(this.elements.thumb.time),this.elements.thumb.imageContainer.appendChild(e),is.element(this.player.elements.progress)&&this.player.elements.progress.appendChild(this.elements.thumb.container),this.elements.scrubbing.container=createElement(\"div\",{class:this.player.config.classNames.previewThumbnails.scrubbingContainer}),this.player.elements.wrapper.appendChild(this.elements.scrubbing.container)})),_defineProperty$1(this,\"destroy\",(()=>{this.elements.thumb.container&&this.elements.thumb.container.remove(),this.elements.scrubbing.container&&this.elements.scrubbing.container.remove()})),_defineProperty$1(this,\"showImageAtCurrentTime\",(()=>{this.mouseDown?this.setScrubbingContainerSize():this.setThumbContainerSizeAndPos();const e=this.thumbnails[0].frames.findIndex((e=>this.seekTime>=e.startTime&&this.seekTime<=e.endTime)),t=e>=0;let i=0;this.mouseDown||this.toggleThumbContainer(t),t&&(this.thumbnails.forEach(((t,s)=>{this.loadedImages.includes(t.frames[e].text)&&(i=s)})),e!==this.showingThumb&&(this.showingThumb=e,this.loadImage(i)))})),_defineProperty$1(this,\"loadImage\",((e=0)=>{const t=this.showingThumb,i=this.thumbnails[e],{urlPrefix:s}=i,n=i.frames[t],r=i.frames[t].text,a=s+r;if(this.currentImageElement&&this.currentImageElement.dataset.filename===r)this.showImage(this.currentImageElement,n,e,t,r,!1),this.currentImageElement.dataset.index=t,this.removeOldImages(this.currentImageElement);else{this.loadingImage&&this.usingSprites&&(this.loadingImage.onload=null);const i=new Image;i.src=a,i.dataset.index=t,i.dataset.filename=r,this.showingThumbFilename=r,this.player.debug.log(`Loading image: ${a}`),i.onload=()=>this.showImage(i,n,e,t,r,!0),this.loadingImage=i,this.removeOldImages(i)}})),_defineProperty$1(this,\"showImage\",((e,t,i,s,n,r=!0)=>{this.player.debug.log(`Showing thumb: ${n}. num: ${s}. qual: ${i}. newimg: ${r}`),this.setImageSizeAndOffset(e,t),r&&(this.currentImageContainer.appendChild(e),this.currentImageElement=e,this.loadedImages.includes(n)||this.loadedImages.push(n)),this.preloadNearby(s,!0).then(this.preloadNearby(s,!1)).then(this.getHigherQuality(i,e,t,n))})),_defineProperty$1(this,\"removeOldImages\",(e=>{Array.from(this.currentImageContainer.children).forEach((t=>{if(\"img\"!==t.tagName.toLowerCase())return;const i=this.usingSprites?500:1e3;if(t.dataset.index!==e.dataset.index&&!t.dataset.deleting){t.dataset.deleting=!0;const{currentImageContainer:e}=this;setTimeout((()=>{e.removeChild(t),this.player.debug.log(`Removing thumb: ${t.dataset.filename}`)}),i)}}))})),_defineProperty$1(this,\"preloadNearby\",((e,t=!0)=>new Promise((i=>{setTimeout((()=>{const s=this.thumbnails[0].frames[e].text;if(this.showingThumbFilename===s){let n;n=t?this.thumbnails[0].frames.slice(e):this.thumbnails[0].frames.slice(0,e).reverse();let r=!1;n.forEach((e=>{const t=e.text;if(t!==s&&!this.loadedImages.includes(t)){r=!0,this.player.debug.log(`Preloading thumb filename: ${t}`);const{urlPrefix:e}=this.thumbnails[0],s=e+t,n=new Image;n.src=s,n.onload=()=>{this.player.debug.log(`Preloaded thumb filename: ${t}`),this.loadedImages.includes(t)||this.loadedImages.push(t),i()}}})),r||i()}}),300)})))),_defineProperty$1(this,\"getHigherQuality\",((e,t,i,s)=>{if(e<this.thumbnails.length-1){let n=t.naturalHeight;this.usingSprites&&(n=i.h),n<this.thumbContainerHeight&&setTimeout((()=>{this.showingThumbFilename===s&&(this.player.debug.log(`Showing higher quality thumb for: ${s}`),this.loadImage(e+1))}),300)}})),_defineProperty$1(this,\"toggleThumbContainer\",((e=!1,t=!1)=>{const i=this.player.config.classNames.previewThumbnails.thumbContainerShown;this.elements.thumb.container.classList.toggle(i,e),!e&&t&&(this.showingThumb=null,this.showingThumbFilename=null)})),_defineProperty$1(this,\"toggleScrubbingContainer\",((e=!1)=>{const t=this.player.config.classNames.previewThumbnails.scrubbingContainerShown;this.elements.scrubbing.container.classList.toggle(t,e),e||(this.showingThumb=null,this.showingThumbFilename=null)})),_defineProperty$1(this,\"determineContainerAutoSizing\",(()=>{(this.elements.thumb.imageContainer.clientHeight>20||this.elements.thumb.imageContainer.clientWidth>20)&&(this.sizeSpecifiedInCSS=!0)})),_defineProperty$1(this,\"setThumbContainerSizeAndPos\",(()=>{const{imageContainer:e}=this.elements.thumb;if(this.sizeSpecifiedInCSS){if(e.clientHeight>20&&e.clientWidth<20){const t=Math.floor(e.clientHeight*this.thumbAspectRatio);e.style.width=`${t}px`}else if(e.clientHeight<20&&e.clientWidth>20){const t=Math.floor(e.clientWidth/this.thumbAspectRatio);e.style.height=`${t}px`}}else{const t=Math.floor(this.thumbContainerHeight*this.thumbAspectRatio);e.style.height=`${this.thumbContainerHeight}px`,e.style.width=`${t}px`}this.setThumbContainerPos()})),_defineProperty$1(this,\"setThumbContainerPos\",(()=>{const e=this.player.elements.progress.getBoundingClientRect(),t=this.player.elements.container.getBoundingClientRect(),{container:i}=this.elements.thumb,s=t.left-e.left+10,n=t.right-e.left-i.clientWidth-10,r=this.mousePosX-e.left-i.clientWidth/2,a=clamp(r,s,n);i.style.left=`${a}px`,i.style.setProperty(\"--preview-arrow-offset\",r-a+\"px\")})),_defineProperty$1(this,\"setScrubbingContainerSize\",(()=>{const{width:e,height:t}=fitRatio(this.thumbAspectRatio,{width:this.player.media.clientWidth,height:this.player.media.clientHeight});this.elements.scrubbing.container.style.width=`${e}px`,this.elements.scrubbing.container.style.height=`${t}px`})),_defineProperty$1(this,\"setImageSizeAndOffset\",((e,t)=>{if(!this.usingSprites)return;const i=this.thumbContainerHeight/t.h;e.style.height=e.naturalHeight*i+\"px\",e.style.width=e.naturalWidth*i+\"px\",e.style.left=`-${t.x*i}px`,e.style.top=`-${t.y*i}px`})),this.player=e,this.thumbnails=[],this.loaded=!1,this.lastMouseMoveTime=Date.now(),this.mouseDown=!1,this.loadedImages=[],this.elements={thumb:{},scrubbing:{}},this.load()}get enabled(){return this.player.isHTML5&&this.player.isVideo&&this.player.config.previewThumbnails.enabled}get currentImageContainer(){return this.mouseDown?this.elements.scrubbing.container:this.elements.thumb.imageContainer}get usingSprites(){return Object.keys(this.thumbnails[0].frames[0]).includes(\"w\")}get thumbAspectRatio(){return this.usingSprites?this.thumbnails[0].frames[0].w/this.thumbnails[0].frames[0].h:this.thumbnails[0].width/this.thumbnails[0].height}get thumbContainerHeight(){if(this.mouseDown){const{height:e}=fitRatio(this.thumbAspectRatio,{width:this.player.media.clientWidth,height:this.player.media.clientHeight});return e}return this.sizeSpecifiedInCSS?this.elements.thumb.imageContainer.clientHeight:Math.floor(this.player.media.clientWidth/this.thumbAspectRatio/4)}get currentImageElement(){return this.mouseDown?this.currentScrubbingImageElement:this.currentThumbnailImageElement}set currentImageElement(e){this.mouseDown?this.currentScrubbingImageElement=e:this.currentThumbnailImageElement=e}}const source={insertElements(e,t){is.string(t)?insertElement(e,this.media,{src:t}):is.array(t)&&t.forEach((t=>{insertElement(e,this.media,t)}))},change(e){getDeep(e,\"sources.length\")?(html5.cancelRequests.call(this),this.destroy.call(this,(()=>{this.options.quality=[],removeElement(this.media),this.media=null,is.element(this.elements.container)&&this.elements.container.removeAttribute(\"class\");const{sources:t,type:i}=e,[{provider:s=providers.html5,src:n}]=t,r=\"html5\"===s?i:\"div\",a=\"html5\"===s?{}:{src:n};Object.assign(this,{provider:s,type:i,supported:support.check(i,s,this.config.playsinline),media:createElement(r,a)}),this.elements.container.appendChild(this.media),is.boolean(e.autoplay)&&(this.config.autoplay=e.autoplay),this.isHTML5&&(this.config.crossorigin&&this.media.setAttribute(\"crossorigin\",\"\"),this.config.autoplay&&this.media.setAttribute(\"autoplay\",\"\"),is.empty(e.poster)||(this.poster=e.poster),this.config.loop.active&&this.media.setAttribute(\"loop\",\"\"),this.config.muted&&this.media.setAttribute(\"muted\",\"\"),this.config.playsinline&&this.media.setAttribute(\"playsinline\",\"\")),ui.addStyleHook.call(this),this.isHTML5&&source.insertElements.call(this,\"source\",t),this.config.title=e.title,media.setup.call(this),this.isHTML5&&Object.keys(e).includes(\"tracks\")&&source.insertElements.call(this,\"track\",e.tracks),(this.isHTML5||this.isEmbed&&!this.supported.ui)&&ui.build.call(this),this.isHTML5&&this.media.load(),is.empty(e.previewThumbnails)||(Object.assign(this.config.previewThumbnails,e.previewThumbnails),this.previewThumbnails&&this.previewThumbnails.loaded&&(this.previewThumbnails.destroy(),this.previewThumbnails=null),this.config.previewThumbnails.enabled&&(this.previewThumbnails=new PreviewThumbnails(this))),this.fullscreen.update()}),!0)):this.debug.warn(\"Invalid source format\")}};class Plyr{constructor(e,t){if(_defineProperty$1(this,\"play\",(()=>is.function(this.media.play)?(this.ads&&this.ads.enabled&&this.ads.managerPromise.then((()=>this.ads.play())).catch((()=>silencePromise(this.media.play()))),this.media.play()):null)),_defineProperty$1(this,\"pause\",(()=>this.playing&&is.function(this.media.pause)?this.media.pause():null)),_defineProperty$1(this,\"togglePlay\",(e=>(is.boolean(e)?e:!this.playing)?this.play():this.pause())),_defineProperty$1(this,\"stop\",(()=>{this.isHTML5?(this.pause(),this.restart()):is.function(this.media.stop)&&this.media.stop()})),_defineProperty$1(this,\"restart\",(()=>{this.currentTime=0})),_defineProperty$1(this,\"rewind\",(e=>{this.currentTime-=is.number(e)?e:this.config.seekTime})),_defineProperty$1(this,\"forward\",(e=>{this.currentTime+=is.number(e)?e:this.config.seekTime})),_defineProperty$1(this,\"increaseVolume\",(e=>{const t=this.media.muted?0:this.volume;this.volume=t+(is.number(e)?e:0)})),_defineProperty$1(this,\"decreaseVolume\",(e=>{this.increaseVolume(-e)})),_defineProperty$1(this,\"airplay\",(()=>{support.airplay&&this.media.webkitShowPlaybackTargetPicker()})),_defineProperty$1(this,\"toggleControls\",(e=>{if(this.supported.ui&&!this.isAudio){const t=hasClass(this.elements.container,this.config.classNames.hideControls),i=void 0===e?void 0:!e,s=toggleClass(this.elements.container,this.config.classNames.hideControls,i);if(s&&is.array(this.config.controls)&&this.config.controls.includes(\"settings\")&&!is.empty(this.config.settings)&&controls.toggleMenu.call(this,!1),s!==t){const e=s?\"controlshidden\":\"controlsshown\";triggerEvent.call(this,this.media,e)}return!s}return!1})),_defineProperty$1(this,\"on\",((e,t)=>{on.call(this,this.elements.container,e,t)})),_defineProperty$1(this,\"once\",((e,t)=>{once.call(this,this.elements.container,e,t)})),_defineProperty$1(this,\"off\",((e,t)=>{off(this.elements.container,e,t)})),_defineProperty$1(this,\"destroy\",((e,t=!1)=>{if(!this.ready)return;const i=()=>{document.body.style.overflow=\"\",this.embed=null,t?(Object.keys(this.elements).length&&(removeElement(this.elements.buttons.play),removeElement(this.elements.captions),removeElement(this.elements.controls),removeElement(this.elements.wrapper),this.elements.buttons.play=null,this.elements.captions=null,this.elements.controls=null,this.elements.wrapper=null),is.function(e)&&e()):(unbindListeners.call(this),html5.cancelRequests.call(this),replaceElement(this.elements.original,this.elements.container),triggerEvent.call(this,this.elements.original,\"destroyed\",!0),is.function(e)&&e.call(this.elements.original),this.ready=!1,setTimeout((()=>{this.elements=null,this.media=null}),200))};this.stop(),clearTimeout(this.timers.loading),clearTimeout(this.timers.controls),clearTimeout(this.timers.resized),this.isHTML5?(ui.toggleNativeControls.call(this,!0),i()):this.isYouTube?(clearInterval(this.timers.buffering),clearInterval(this.timers.playing),null!==this.embed&&is.function(this.embed.destroy)&&this.embed.destroy(),i()):this.isVimeo&&(null!==this.embed&&this.embed.unload().then(i),setTimeout(i,200))})),_defineProperty$1(this,\"supports\",(e=>support.mime.call(this,e))),this.timers={},this.ready=!1,this.loading=!1,this.failed=!1,this.touch=support.touch,this.media=e,is.string(this.media)&&(this.media=document.querySelectorAll(this.media)),(window.jQuery&&this.media instanceof jQuery||is.nodeList(this.media)||is.array(this.media))&&(this.media=this.media[0]),this.config=extend({},defaults,Plyr.defaults,t||{},(()=>{try{return JSON.parse(this.media.getAttribute(\"data-plyr-config\"))}catch(e){return{}}})()),this.elements={container:null,fullscreen:null,captions:null,buttons:{},display:{},progress:{},inputs:{},settings:{popup:null,menu:null,panels:{},buttons:{}}},this.captions={active:null,currentTrack:-1,meta:new WeakMap},this.fullscreen={active:!1},this.options={speed:[],quality:[]},this.debug=new Console(this.config.debug),this.debug.log(\"Config\",this.config),this.debug.log(\"Support\",support),is.nullOrUndefined(this.media)||!is.element(this.media))return void this.debug.error(\"Setup failed: no suitable element passed\");if(this.media.plyr)return void this.debug.warn(\"Target already setup\");if(!this.config.enabled)return void this.debug.error(\"Setup failed: disabled by config\");if(!support.check().api)return void this.debug.error(\"Setup failed: no support\");const i=this.media.cloneNode(!0);i.autoplay=!1,this.elements.original=i;const s=this.media.tagName.toLowerCase();let n=null,r=null;switch(s){case\"div\":if(n=this.media.querySelector(\"iframe\"),is.element(n)){if(r=parseUrl(n.getAttribute(\"src\")),this.provider=getProviderByUrl(r.toString()),this.elements.container=this.media,this.media=n,this.elements.container.className=\"\",r.search.length){const e=[\"1\",\"true\"];e.includes(r.searchParams.get(\"autoplay\"))&&(this.config.autoplay=!0),e.includes(r.searchParams.get(\"loop\"))&&(this.config.loop.active=!0),this.isYouTube?(this.config.playsinline=e.includes(r.searchParams.get(\"playsinline\")),this.config.youtube.hl=r.searchParams.get(\"hl\")):this.config.playsinline=!0}}else this.provider=this.media.getAttribute(this.config.attributes.embed.provider),this.media.removeAttribute(this.config.attributes.embed.provider);if(is.empty(this.provider)||!Object.values(providers).includes(this.provider))return void this.debug.error(\"Setup failed: Invalid provider\");this.type=types.video;break;case\"video\":case\"audio\":this.type=s,this.provider=providers.html5,this.media.hasAttribute(\"crossorigin\")&&(this.config.crossorigin=!0),this.media.hasAttribute(\"autoplay\")&&(this.config.autoplay=!0),(this.media.hasAttribute(\"playsinline\")||this.media.hasAttribute(\"webkit-playsinline\"))&&(this.config.playsinline=!0),this.media.hasAttribute(\"muted\")&&(this.config.muted=!0),this.media.hasAttribute(\"loop\")&&(this.config.loop.active=!0);break;default:return void this.debug.error(\"Setup failed: unsupported type\")}this.supported=support.check(this.type,this.provider),this.supported.api?(this.eventListeners=[],this.listeners=new Listeners(this),this.storage=new Storage(this),this.media.plyr=this,is.element(this.elements.container)||(this.elements.container=createElement(\"div\"),wrap(this.media,this.elements.container)),ui.migrateStyles.call(this),ui.addStyleHook.call(this),media.setup.call(this),this.config.debug&&on.call(this,this.elements.container,this.config.events.join(\" \"),(e=>{this.debug.log(`event: ${e.type}`)})),this.fullscreen=new Fullscreen(this),(this.isHTML5||this.isEmbed&&!this.supported.ui)&&ui.build.call(this),this.listeners.container(),this.listeners.global(),this.config.ads.enabled&&(this.ads=new Ads(this)),this.isHTML5&&this.config.autoplay&&this.once(\"canplay\",(()=>silencePromise(this.play()))),this.lastSeekTime=0,this.config.previewThumbnails.enabled&&(this.previewThumbnails=new PreviewThumbnails(this))):this.debug.error(\"Setup failed: no support\")}get isHTML5(){return this.provider===providers.html5}get isEmbed(){return this.isYouTube||this.isVimeo}get isYouTube(){return this.provider===providers.youtube}get isVimeo(){return this.provider===providers.vimeo}get isVideo(){return this.type===types.video}get isAudio(){return this.type===types.audio}get playing(){return Boolean(this.ready&&!this.paused&&!this.ended)}get paused(){return Boolean(this.media.paused)}get stopped(){return Boolean(this.paused&&0===this.currentTime)}get ended(){return Boolean(this.media.ended)}set currentTime(e){if(!this.duration)return;const t=is.number(e)&&e>0;this.media.currentTime=t?Math.min(e,this.duration):0,this.debug.log(`Seeking to ${this.currentTime} seconds`)}get currentTime(){return Number(this.media.currentTime)}get buffered(){const{buffered:e}=this.media;return is.number(e)?e:e&&e.length&&this.duration>0?e.end(0)/this.duration:0}get seeking(){return Boolean(this.media.seeking)}get duration(){const e=parseFloat(this.config.duration),t=(this.media||{}).duration,i=is.number(t)&&t!==1/0?t:0;return e||i}set volume(e){let t=e;is.string(t)&&(t=Number(t)),is.number(t)||(t=this.storage.get(\"volume\")),is.number(t)||({volume:t}=this.config),t>1&&(t=1),t<0&&(t=0),this.config.volume=t,this.media.volume=t,!is.empty(e)&&this.muted&&t>0&&(this.muted=!1)}get volume(){return Number(this.media.volume)}set muted(e){let t=e;is.boolean(t)||(t=this.storage.get(\"muted\")),is.boolean(t)||(t=this.config.muted),this.config.muted=t,this.media.muted=t}get muted(){return Boolean(this.media.muted)}get hasAudio(){return!this.isHTML5||(!!this.isAudio||(Boolean(this.media.mozHasAudio)||Boolean(this.media.webkitAudioDecodedByteCount)||Boolean(this.media.audioTracks&&this.media.audioTracks.length)))}set speed(e){let t=null;is.number(e)&&(t=e),is.number(t)||(t=this.storage.get(\"speed\")),is.number(t)||(t=this.config.speed.selected);const{minimumSpeed:i,maximumSpeed:s}=this;t=clamp(t,i,s),this.config.speed.selected=t,setTimeout((()=>{this.media&&(this.media.playbackRate=t)}),0)}get speed(){return Number(this.media.playbackRate)}get minimumSpeed(){return this.isYouTube?Math.min(...this.options.speed):this.isVimeo?.5:.0625}get maximumSpeed(){return this.isYouTube?Math.max(...this.options.speed):this.isVimeo?2:16}set quality(e){const t=this.config.quality,i=this.options.quality;if(!i.length)return;let s=[!is.empty(e)&&Number(e),this.storage.get(\"quality\"),t.selected,t.default].find(is.number),n=!0;if(!i.includes(s)){const e=closest(i,s);this.debug.warn(`Unsupported quality option: ${s}, using ${e} instead`),s=e,n=!1}t.selected=s,this.media.quality=s,n&&this.storage.set({quality:s})}get quality(){return this.media.quality}set loop(e){const t=is.boolean(e)?e:this.config.loop.active;this.config.loop.active=t,this.media.loop=t}get loop(){return Boolean(this.media.loop)}set source(e){source.change.call(this,e)}get source(){return this.media.currentSrc}get download(){const{download:e}=this.config.urls;return is.url(e)?e:this.source}set download(e){is.url(e)&&(this.config.urls.download=e,controls.setDownloadUrl.call(this))}set poster(e){this.isVideo?ui.setPoster.call(this,e,!1).catch((()=>{})):this.debug.warn(\"Poster can only be set for video\")}get poster(){return this.isVideo?this.media.getAttribute(\"poster\")||this.media.getAttribute(\"data-poster\"):null}get ratio(){if(!this.isVideo)return null;const e=reduceAspectRatio(getAspectRatio.call(this));return is.array(e)?e.join(\":\"):e}set ratio(e){this.isVideo?is.string(e)&&validateAspectRatio(e)?(this.config.ratio=reduceAspectRatio(e),setAspectRatio.call(this)):this.debug.error(`Invalid aspect ratio specified (${e})`):this.debug.warn(\"Aspect ratio can only be set for video\")}set autoplay(e){this.config.autoplay=is.boolean(e)?e:this.config.autoplay}get autoplay(){return Boolean(this.config.autoplay)}toggleCaptions(e){captions.toggle.call(this,e,!1)}set currentTrack(e){captions.set.call(this,e,!1),captions.setup.call(this)}get currentTrack(){const{toggled:e,currentTrack:t}=this.captions;return e?t:-1}set language(e){captions.setLanguage.call(this,e,!1)}get language(){return(captions.getCurrentTrack.call(this)||{}).language}set pip(e){if(!support.pip)return;const t=is.boolean(e)?e:!this.pip;is.function(this.media.webkitSetPresentationMode)&&this.media.webkitSetPresentationMode(t?pip.active:pip.inactive),is.function(this.media.requestPictureInPicture)&&(!this.pip&&t?this.media.requestPictureInPicture():this.pip&&!t&&document.exitPictureInPicture())}get pip(){return support.pip?is.empty(this.media.webkitPresentationMode)?this.media===document.pictureInPictureElement:this.media.webkitPresentationMode===pip.active:null}setPreviewThumbnails(e){this.previewThumbnails&&this.previewThumbnails.loaded&&(this.previewThumbnails.destroy(),this.previewThumbnails=null),Object.assign(this.config.previewThumbnails,e),this.config.previewThumbnails.enabled&&(this.previewThumbnails=new PreviewThumbnails(this))}static supported(e,t){return support.check(e,t)}static loadSprite(e,t){return loadSprite(e,t)}static setup(e,t={}){let i=null;return is.string(e)?i=Array.from(document.querySelectorAll(e)):is.nodeList(e)?i=Array.from(e):is.array(e)&&(i=e.filter(is.element)),is.empty(i)?null:i.map((e=>new Plyr(e,t)))}}Plyr.defaults=cloneDeep(defaults);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/plyr/dist/plyr.min.mjs\n");

/***/ })

};
;