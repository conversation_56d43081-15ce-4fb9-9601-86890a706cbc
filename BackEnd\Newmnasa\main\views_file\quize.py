# Django REST framework
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response

# الصلاحيات
from rest_framework.permissions import IsAuthenticated

# التواريخ
from django.utils import timezone

# UUID لتوليد كود الشهادة
import uuid

from rest_framework.views import APIView
from django.db.models import Sum
import traceback

# الموديلات (من المفترض إنها موجودة في models.py أو مقسمة)
from ..models import (
    Quiz,
    Question,
    Answer,
    UserQuizAttempt,
    Certificate,
)

# السيرالايزرز
from ..serializers_file.quize_serializer import (
    QuizSerializer,
    QuestionSerializer,
    AnswerSerializer,
)

# الثروتلز
from ..throttles import QuizGeneralThrottle, QuizSaveAnswerThrottle

# الإشعارات والدوال المساعدة (لو بتستخدمهم)
from main.utils.notifications import notify
from main.constants.notification_messages import (
    CERTIFICATE_ISSUED,
    QUIZ_CERTIFICATE_CONGRATS,
)
from main.constants.notification_types import CERTIFICATE


class QuizViewSet(viewsets.ModelViewSet):
    queryset = Quiz.objects.all()
    serializer_class = QuizSerializer
    permission_classes = [permissions.IsAuthenticated]
    throttle_classes = [QuizGeneralThrottle, QuizSaveAnswerThrottle]
    lookup_field = 'id'  # استخدام ID للكويزات - zaki alkholy

    def _auto_submit_quiz(self, attempt, quiz):
        """دالة التسليم التلقائي عند انتهاء الوقت - zaki alkholy"""
        if attempt.submitted:
            return  # تم التسليم بالفعل

        answers = getattr(attempt, "answers", {}) or {}
        score = 0
        correct_count = 0
        total_questions = quiz.questions.count()
        max_score = sum(q.points for q in quiz.questions.all())

        # حساب النتيجة بناءً على الإجابات المحفوظة - zaki alkholy
        for q in quiz.questions.all():
            qid = str(q.id)
            ans_id = answers.get(qid)
            if ans_id:
                try:
                    selected_answer = Answer.objects.get(id=ans_id)
                    if selected_answer.is_correct:
                        score += q.points
                        correct_count += 1
                except Answer.DoesNotExist:
                    pass

        # تحديث المحاولة مع معلومات التسليم التلقائي - zaki alkholy
        attempt.score = score
        attempt.passed = quiz.is_passed(score)  # النظام الجديد البسيط - zaki alkholy
        attempt.submitted = True
        attempt.submitted_at = timezone.now()
        attempt.save()

        # إصدار شهادة إذا نجح - zaki alkholy
        if attempt.passed:
            self.issue_certificate(attempt.user, quiz.lesson.course)

        print(
            f"zaki alkholy - تم التسليم التلقائي للطالب {attempt.user.username} - النتيجة: {score}/{max_score}"
        )

    @action(detail=True, methods=["post"])
    def submit(self, request, id=None):
        quiz = self.get_object()
        answers = request.data.get("answers", [])
        # تحقق إذا الطالب سلّم الامتحان قبل كده
        if UserQuizAttempt.objects.filter(user=request.user, quiz=quiz).exists():
            return Response(
                {"error": "لقد قمت بتسليم الامتحان بالفعل ولا يمكنك إعادة التسليم."},
                status=status.HTTP_403_FORBIDDEN,
            )
        # zaki alkholy: log للـ answers القادمة
        print("zaki alkholy - answers received:", answers)
        # حساب النتيجة
        score = 0
        correct_count = 0  # عدد الإجابات الصحيحة
        total_questions = quiz.questions.count()
        # حساب مجموع الدرجات الكلي
        max_score = sum(q.points for q in quiz.questions.all())
        answers_feedback = []
        for answer in answers:
            question_id = answer.get("question_id")
            answer_id = answer.get("answer_id")
            if not question_id or not answer_id:
                continue  # تجاهل الإجابات غير المكتملة
            try:
                question = Question.objects.get(id=question_id)
                selected_answer = Answer.objects.get(id=answer_id)
            except Exception as e:
                print(f"zaki alkholy - invalid question/answer: {e}")
                continue
            is_correct = selected_answer.is_correct
            answers_feedback.append(
                {"question_id": question_id, "is_correct": is_correct}
            )
            if is_correct:
                score += question.points
                correct_count += 1
        # تحديث محاولة الاختبار
        attempt = UserQuizAttempt.objects.create(
            user=request.user,
            quiz=quiz,
            score=score,
            passed=quiz.is_passed(score),  # النظام الجديد البسيط - zaki alkholy
        )
        # إصدار شهادة إذا نجح في الاختبار (يظل في الباك)
        if attempt.passed:
            self.issue_certificate(request.user, quiz.lesson.course)
            notify(
                request.user,
                QUIZ_CERTIFICATE_CONGRATS(quiz.lesson.course.title),
                type=CERTIFICATE,
            )
        # الاستجابة للفرونت: الدرجة ومجموع الدرجات وقائمة feedback
        return Response(
            {
                "score": score,
                "max_score": max_score,
                "answers_feedback": answers_feedback,
                "correctCount": correct_count,
                "total": total_questions,
            }
        )

    def issue_certificate(self, user, course):
        certificate = Certificate.objects.create(
            user=user, course=course, verification_code=str(uuid.uuid4())
        )
        # إشعار عند إصدار الشهادة
        notify(user, CERTIFICATE_ISSUED(course.title), type=CERTIFICATE)
        return certificate

    # تصليح منطق الوقت المتبقي للامتحان والواجب - zaki alkholy
    @action(detail=True, methods=["get"], url_path="get_time_left")
    def get_time_left(self, request, id=None):
        quiz = self.get_object()
        user = request.user

        # تحقق إذا كان الطالب سلّم الامتحان/الواجب قبل كده
        submitted_attempt = UserQuizAttempt.objects.filter(
            user=user, quiz=quiz, submitted=True
        ).first()
        if submitted_attempt:
            return Response(
                {
                    "time_left": 0,
                    "status": "submitted",
                    "message": f"لقد قمت بتسليم {quiz.get_quiz_type_display()} بالفعل.",
                },
                status=200,
            )

        # جلب أو إنشاء محاولة غير مسلمة - zaki alkholy
        attempt = (
            UserQuizAttempt.objects.filter(user=user, quiz=quiz, submitted=False)
            .order_by("-created_at")
            .first()
        )

        if not attempt:
            # إنشاء محاولة جديدة مع تسجيل وقت البداية - zaki alkholy
            attempt = UserQuizAttempt.objects.create(
                user=user, quiz=quiz, score=0, passed=False, submitted=False
            )
            started_at = attempt.created_at
            print(
                f"zaki alkholy - إنشاء محاولة جديدة للطالب {user.username} في {quiz.get_quiz_type_display()} {started_at}"
            )
        else:
            # استخدام وقت البداية الموجود - zaki alkholy
            started_at = attempt.created_at
            print(
                f"zaki alkholy - استكمال محاولة موجودة للطالب {user.username} في {quiz.get_quiz_type_display()} بدأت في {started_at}"
            )

        # from .serializers import QuestionSerializer

        questions_data = QuestionSerializer(quiz.questions.all(), many=True).data
        saved_answers = getattr(attempt, "answers", {}) or {}

        # للواجبات (assignment): وقت غير محدود - zaki alkholy
        if quiz.quiz_type == "assignment" or quiz.time_limit == 0:
            print(f"zaki alkholy - واجب بوقت غير محدود للطالب {user.username}")
            return Response(
                {
                    "time_left": -1,  # -1 يعني وقت غير محدود
                    "started_at": started_at,
                    "now": timezone.now(),
                    "time_limit": 0,
                    "questions": questions_data,
                    "saved_answers": saved_answers,
                    "status": "in_progress",
                    "quiz_type": quiz.quiz_type,
                }
            )

        # للامتحانات: حساب الوقت المتبقي - zaki alkholy
        time_limit = quiz.time_limit * 60  # الدقائق إلى ثواني
        now = timezone.now()
        elapsed = (now - started_at).total_seconds()
        time_left = max(0, int(time_limit - elapsed))

        print(
            f"zaki alkholy - امتحان: الوقت المحدد: {time_limit} ثانية، المنقضي: {elapsed} ثانية، المتبقي: {time_left} ثانية"
        )

        # تحقق من انتهاء الوقت للامتحانات فقط - zaki alkholy
        if time_left <= 0:
            print(
                f"zaki alkholy - انتهى الوقت للطالب {user.username}، سيتم التسليم التلقائي"
            )
            # التسليم التلقائي عند انتهاء الوقت - zaki alkholy
            self._auto_submit_quiz(attempt, quiz)
            return Response(
                {
                    "time_left": 0,
                    "status": "auto_submitted",
                    "auto_submitted": True,
                    "message": "تم تسليم الامتحان تلقائياً لانتهاء الوقت المحدد.",
                },
                status=200,
            )

        # إرجاع الوقت المتبقي مع الأسئلة للامتحانات - zaki alkholy
        return Response(
            {
                "time_left": time_left,
                "started_at": started_at,
                "now": now,
                "time_limit": time_limit,
                "questions": questions_data,
                "saved_answers": saved_answers,
                "status": "in_progress",
                "quiz_type": quiz.quiz_type,
            }
        )

    @action(detail=True, methods=["get"])
    def exam_status(self, request, id=None):
        quiz = self.get_object()
        user = request.user
        # جلب آخر محاولة للطالب
        attempt = (
            UserQuizAttempt.objects.filter(user=user, quiz=quiz).order_by("-id").first()
        )
        # from .serializers import QuestionSerializer

        questions_data = QuestionSerializer(quiz.questions.all(), many=True).data
        if not attempt:
            return Response(
                {
                    "status": "not_started",
                    "questions": questions_data,
                    "quiz_name": quiz.title,
                    "time_limit": quiz.time_limit * 60,
                }
            )
        # تحقق من التسليم بناءً على حقل submitted فقط - zaki alkholy
        if getattr(attempt, "submitted", False):
            # تم التسليم - إضافة معلومات الإجابات الصحيحة والخاطئة - zaki alkholy
            answers = getattr(attempt, "answers", {})
            answers_feedback = []
            # تحويل إجابات الطالب من answer_id إلى index للفرونت إند
            student_answers_by_index = {}

            for q in quiz.questions.all():
                qid = str(q.id)
                selected_answer_id = answers.get(qid)
                is_correct = False
                correct_answer_id = None
                selected_answer_text = None
                correct_answer_text = None

                # جلب الإجابة الصحيحة - zaki alkholy
                correct_answer = q.answers.filter(is_correct=True).first()
                if correct_answer:
                    correct_answer_id = correct_answer.id
                    correct_answer_text = correct_answer.text

                # تحقق من الإجابة المختارة وتحويلها إلى index - zaki alkholy
                if selected_answer_id:
                    try:
                        selected_answer = Answer.objects.get(id=selected_answer_id)
                        selected_answer_text = selected_answer.text
                        is_correct = selected_answer.is_correct

                        # العثور على index الإجابة المختارة - zaki alkholy
                        question_answers = list(q.answers.all())
                        for idx, ans in enumerate(question_answers):
                            if ans.id == int(selected_answer_id):
                                student_answers_by_index[qid] = idx
                                break
                    except (Answer.DoesNotExist, ValueError):
                        pass

                answers_feedback.append(
                    {
                        "question_id": qid,
                        "question_text": q.text,
                        "selected_answer_id": selected_answer_id,
                        "selected_answer_text": selected_answer_text,
                        "correct_answer_id": correct_answer_id,
                        "correct_answer_text": correct_answer_text,
                        "is_correct": is_correct,
                        "points": q.points if is_correct else 0,
                    }
                )

            # حساب عدد الإجابات الصحيحة - zaki alkholy
            correct_count = len([fb for fb in answers_feedback if fb["is_correct"]])

            return Response(
                {
                    "status": "submitted",
                    "score": attempt.score,
                    "max_score": sum(q.points for q in quiz.questions.all()),
                    "passed": attempt.passed,
                    "quiz_name": quiz.title,
                    "answers": student_answers_by_index,  # إرسال الإجابات كـ indices
                    "answers_feedback": answers_feedback,
                    "questions": questions_data,
                    "submitted_at": getattr(attempt, "submitted_at", None),
                    "correctCount": correct_count,  # إضافة عدد الإجابات الصحيحة - zaki alkholy
                    "totalQuestions": quiz.questions.count(),  # إضافة العدد الكلي للأسئلة - zaki alkholy
                }
            )
        # محاولة جارية
        return Response(
            {
                "status": "in_progress",
                "answers": getattr(attempt, "answers", {}),
                "questions": questions_data,
                "quiz_name": quiz.title,
                "started_at": getattr(attempt, "created_at", None),
                "time_limit": quiz.time_limit * 60,
            }
        )

    @action(detail=True, methods=["post"])
    def start(self, request, id=None):
        """بدء محاولة امتحان جديدة أو استكمال محاولة موجودة - zaki alkholy"""
        quiz = self.get_object()
        user = request.user

        # تحقق من وجود محاولة مسلمة مسبقاً - zaki alkholy
        submitted_attempt = UserQuizAttempt.objects.filter(
            user=user, quiz=quiz, submitted=True
        ).first()
        if submitted_attempt:
            return Response(
                {"error": "تم تسليم الامتحان بالفعل", "status": "already_submitted"},
                status=403,
            )

        # جلب أو إنشاء محاولة غير مسلمة - zaki alkholy
        attempt = (
            UserQuizAttempt.objects.filter(user=user, quiz=quiz, submitted=False)
            .order_by("-created_at")
            .first()
        )

        if not attempt:
            # إنشاء محاولة جديدة - zaki alkholy
            attempt = UserQuizAttempt.objects.create(
                user=user, quiz=quiz, score=0, passed=False, answers={}, submitted=False
            )
            print(
                f"zaki alkholy - تم إنشاء محاولة جديدة للطالب {user.username} في الامتحان {quiz.title}"
            )
        else:
            print(
                f"zaki alkholy - استكمال محاولة موجودة للطالب {user.username} في الامتحان {quiz.title}"
            )

        return Response(
            {
                "attempt_id": attempt.id,
                "started_at": attempt.created_at,
                "status": "started",
            }
        )

    @action(detail=True, methods=["patch"])
    def save_answer(self, request, id=None):
        """حفظ إجابة سؤال في الامتحان - zaki alkholy"""
        quiz = self.get_object()
        user = request.user
        question_id = request.data.get("question_id")
        answer_id = request.data.get("answer_id")

        # جلب المحاولة الجارية - zaki alkholy
        attempt = (
            UserQuizAttempt.objects.filter(user=user, quiz=quiz, submitted=False)
            .order_by("-created_at")
            .first()
        )

        if not attempt:
            return Response(
                {
                    "error": "لا توجد محاولة جارية. يرجى بدء الامتحان أولاً.",
                    "status": "no_attempt",
                },
                status=400,
            )

        # تحقق من انتهاء الوقت للامتحانات فقط - zaki alkholy
        if quiz.quiz_type == "exam" and quiz.time_limit > 0:
            time_limit = quiz.time_limit * 60
            now = timezone.now()
            elapsed = (now - attempt.created_at).total_seconds()
            if elapsed >= time_limit:
                # التسليم التلقائي - zaki alkholy
                self._auto_submit_quiz(attempt, quiz)
                return Response(
                    {
                        "error": "انتهى وقت الامتحان وتم التسليم تلقائياً",
                        "status": "time_expired",
                        "auto_submitted": True,
                    },
                    status=400,
                )

        # حفظ الإجابة - zaki alkholy
        answers = getattr(attempt, "answers", {}) or {}
        old_answer = answers.get(str(question_id))

        # تحقق من تغيير الإجابة لتجنب الحفظ المتكرر - zaki alkholy
        if old_answer != answer_id:
            answers[str(question_id)] = answer_id
            attempt.answers = answers
            attempt.save()
            print(
                f"zaki alkholy - تم حفظ إجابة السؤال {question_id} للطالب {user.username}: {old_answer} -> {answer_id}"
            )
        else:
            print(
                f"zaki alkholy - الإجابة لم تتغير للسؤال {question_id} للطالب {user.username}"
            )

        return Response(
            {"message": "تم حفظ الإجابة بنجاح", "answers": answers, "status": "saved"}
        )

    @action(detail=True, methods=["post"])
    def check_time_and_auto_submit(self, request, id=None):
        """فحص انتهاء الوقت والتسليم التلقائي إذا لزم الأمر - zaki alkholy"""
        quiz = self.get_object()
        user = request.user

        # جلب المحاولة الجارية - zaki alkholy
        attempt = (
            UserQuizAttempt.objects.filter(user=user, quiz=quiz, submitted=False)
            .order_by("-created_at")
            .first()
        )

        if not attempt:
            return Response(
                {"error": "لا توجد محاولة جارية", "status": "no_attempt"}, status=400
            )

        # للواجبات: لا يوجد تسليم تلقائي - zaki alkholy
        if quiz.quiz_type == "assignment" or quiz.time_limit == 0:
            return Response(
                {"message": "الواجب ليس له وقت محدد", "status": "unlimited_time"}
            )

        # تحقق من انتهاء الوقت للامتحانات فقط - zaki alkholy
        if quiz.quiz_type == "exam" and quiz.time_limit > 0:
            time_limit = quiz.time_limit * 60
            now = timezone.now()
            elapsed = (now - attempt.created_at).total_seconds()
            if elapsed >= time_limit:
                # التسليم التلقائي - zaki alkholy
                self._auto_submit_quiz(attempt, quiz)
                return Response(
                    {
                        "message": "تم التسليم التلقائي لانتهاء الوقت",
                        "status": "auto_submitted",
                        "auto_submitted": True,
                    },
                    status=200,
                )

        return Response({"message": "الوقت لم ينته بعد", "status": "time_remaining"})

    @action(detail=True, methods=["post"])
    def submit(self, request, id=None):
        quiz = self.get_object()
        user = request.user
        # جلب المحاولة الجارية غير المسلمة - zaki alkholy
        attempt = (
            UserQuizAttempt.objects.filter(user=user, quiz=quiz, submitted=False)
            .order_by("-created_at")
            .first()
        )

        if not attempt:
            return Response(
                {
                    "error": "لا توجد محاولة جارية أو تم التسليم بالفعل",
                    "status": "no_attempt",
                },
                status=403,
            )
        answers = getattr(attempt, "answers", {}) or {}
        # حساب النتيجة
        score = 0
        correct_count = 0
        total_questions = quiz.questions.count()
        max_score = sum(q.points for q in quiz.questions.all())
        answers_feedback = []
        for q in quiz.questions.all():
            qid = str(q.id)
            ans_id = answers.get(qid)
            is_correct = False
            if ans_id:
                try:
                    selected_answer = Answer.objects.get(id=ans_id)
                    is_correct = selected_answer.is_correct
                except:
                    pass
            answers_feedback.append({"question_id": qid, "is_correct": is_correct})
            if is_correct:
                score += q.points
                correct_count += 1
        attempt.score = score
        attempt.passed = quiz.is_passed(score)  # النظام الجديد البسيط - zaki alkholy
        attempt.submitted = True
        attempt.submitted_at = timezone.now()
        attempt.save()
        # إصدار شهادة إذا نجح
        if attempt.passed:
            self.issue_certificate(user, quiz.lesson.course)
            notify(
                user,
                QUIZ_CERTIFICATE_CONGRATS(quiz.lesson.course.title),
                type=CERTIFICATE,
            )
        return Response(
            {
                "score": score,
                "max_score": max_score,
                "answers_feedback": answers_feedback,
                "correctCount": correct_count,
                "total": total_questions,
                "passed": attempt.passed,
            }
        )

    @action(detail=True, methods=["post"], url_path="publish")
    def publish(self, request, id=None):
        quiz = self.get_object()
        # تحقق من صلاحية المستخدم (المعلم فقط)
        if not request.user.is_instructor:
            return Response(
                {"error": "غير مصرح لك بالنشر"}, status=status.HTTP_403_FORBIDDEN
            )

        # التحقق من تطابق الدرجات قبل النشر - zakialkholy
        calculated_score = quiz.calculated_max_score
        current_max_score = quiz.max_score

        if calculated_score != current_max_score:
            return Response({
                "error": "تحذير: مجموع درجات الأسئلة لا يساوي الدرجة النهائية للامتحان",
                "details": {
                    "current_max_score": current_max_score,
                    "calculated_score": calculated_score,
                    "difference": calculated_score - current_max_score
                },
                "message": f"الدرجة النهائية المحددة: {current_max_score} - مجموع درجات الأسئلة: {calculated_score}",
                "suggestion": "يرجى تعديل درجات الأسئلة أو الدرجة النهائية لتصبح متساوية قبل النشر"
            }, status=status.HTTP_400_BAD_REQUEST)

        quiz.is_published = True
        quiz.save()
        return Response({
            "status": "تم نشر الامتحان/الواجب بنجاح",
            "max_score": quiz.max_score,
            "passing_score": quiz.passing_score  # النظام الجديد البسيط - zaki alkholy
        })

    @action(detail=True, methods=["post"], url_path="unpublish")
    def unpublish(self, request, id=None):
        quiz = self.get_object()
        if not request.user.is_instructor:
            return Response(
                {"error": "غير مصرح لك بإلغاء النشر"}, status=status.HTTP_403_FORBIDDEN
            )
        quiz.is_published = False
        quiz.save()
        return Response({"status": "تم إلغاء نشر الامتحان/الواجب بنجاح"})


# تحديث QuestionViewSet لدعم رفع الصور - زكي الخولي
class QuestionViewSet(viewsets.ModelViewSet):
    queryset = Question.objects.all()
    serializer_class = QuestionSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'  # استخدام ID للأسئلة - zaki alkholy

    def get_serializer_context(self):
        # إضافة request للـ context لاستخدامه في image_url - زكي الخولي
        context = super().get_serializer_context()
        context["request"] = self.request
        return context

    def perform_create(self, serializer):
        # حفظ السؤال مع معلومات إضافية إذا لزم الأمر - زكي الخولي
        serializer.save()

    def perform_update(self, serializer):
        # تحديث السؤال مع الحفاظ على الصورة القديمة إذا لم يتم رفع صورة جديدة - زكي الخولي
        serializer.save()


class AnswerViewSet(viewsets.ModelViewSet):
    queryset = Answer.objects.all()
    serializer_class = AnswerSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'  # استخدام ID للإجابات - zaki alkholy
