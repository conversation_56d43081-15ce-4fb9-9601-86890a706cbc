"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(pages)/instructor/dashboard/[id]/page",{

/***/ "(app-pages-browser)/./src/components/instructor/LessonList.jsx":
/*!**************************************************!*\
  !*** ./src/components/instructor/LessonList.jsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_Components_course_QuizEditModal__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/_Components/course/QuizEditModal */ \"(app-pages-browser)/./src/app/_Components/course/QuizEditModal.jsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _contexts_CourseContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/CourseContext */ \"(app-pages-browser)/./src/contexts/CourseContext.jsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n/**\r\n * قائمة الدروس في صفحة تفاصيل الكورس للمدرب\r\n * @param {Object} props\r\n * @param {Array} props.lessons - قائمة الدروس\r\n * @param {number|null} props.expandedLesson - معرف الدرس المفتوح\r\n * @param {Function} props.setExpandedLesson - دالة لتغيير الدرس المفتوح\r\n * @param {Function} props.handleSeeLesson - دالة للانتقال لصفحة الدرس\r\n * @param {Function} props.handleDeleteLesson - دالة حذف الدرس\r\n * @param {Object} props.deletingResource - حالة حذف ملف الموارد لكل درس\r\n * @param {Function} props.handleDeleteResource - دالة حذف ملف الموارد\r\n * @param {Function} props.setShowFileForm - دالة إظهار فورم رفع ملف\r\n * @param {Object} props.showFileForm - حالة إظهار فورم رفع ملف لكل درس\r\n * @param {Function} props.setShowQuizModal - دالة إظهار فورم إضافة كويز\r\n * @param {Object} props.showQuizModal - حالة إظهار فورم إضافة كويز لكل درس\r\n * @param {Function} props.setQuizForm - دالة تحديث فورم الكويز\r\n * @param {Object} props.quizForm - بيانات فورم الكويز لكل درس\r\n * @param {Function} props.handleAddQuiz - دالة إضافة كويز\r\n * @param {Function} props.handleDeleteQuiz - دالة حذف كويز\r\n * @param {Function} props.setQuizEditForm - دالة تعديل بيانات الكويز\r\n * @param {Object} props.quizEditForm - بيانات تعديل الكويز\r\n * @param {Function} props.handleEditQuiz - دالة تعديل الكويز\r\n * @param {Function} props.handleAddFile - دالة رفع ملف\r\n * @param {Object} props.isUploading - حالة رفع الملف لكل درس\r\n * @param {Object} props.uploadProgress - تقدم رفع الملف لكل درس\r\n * @param {Object} props.uploadedFile - الملف المرفوع لكل درس\r\n * @param {Object} props.uploadError - خطأ رفع الملف لكل درس\r\n * @param {Object} props.uploadSuccess - نجاح رفع الملف لكل درس\r\n * @param {Object} props.fileInputRefs - مراجع حقول الملفات\r\n * @param {Component} props.QuizFormModal - مكون فورم الكويز\r\n * @param {Component} props.FileUploadModal - مكون رفع الملف\r\n * @param {Function} props.generateQuizPDF - دالة توليد PDF للامتحان\r\n * @param {Object} props.user - بيانات المستخدم الحالي\r\n * @param {Function} props.setLessons - دالة تحديث قائمة الدروس\r\n * @param {string} props.courseId - معرف الكورس\r\n *\r\n * يمكن تمرير المزيد من props حسب الحاجة.\r\n */ const LessonList = ()=>{\n    _s();\n    // استخدام Context بدلاً من Props\n    const { // Course Data\n    courseData, courseId, user, // Lessons\n    lessons = [], setLessons, expandedLesson, setExpandedLesson, expandedQuizzes = {}, setExpandedQuizzes, // Navigation & Actions\n    handleSeeLesson, handleDeleteLesson, // File Upload\n    handleAddFile, handleDeleteResource, deletingResource = {}, showFileForm = {}, setShowFileForm, fileInputRefs, isUploading = {}, uploadProgress = {}, uploadSuccess = {}, uploadError = {}, uploadedFile = {}, // Quiz Management\n    showQuizModal = {}, setShowQuizModal, quizForm = {}, setQuizForm, handleAddQuiz, handleDeleteQuiz, handleEditQuiz, quizEditForm, setQuizEditForm, // Question Management\n    showQuestionForm = {}, setShowQuestionForm, questionForm = {}, setQuestionForm, answersForm = {}, setAnswersForm, questionLoading = {}, handleAddQuestion, handleDeleteQuestion, handleEditQuestion, questionEditForm, setQuestionEditForm, // Image Management\n    croppedImages = {}, setCroppedImages, questionImages = {}, setQuestionImages, imagePreview = {}, setImagePreview, showImageCropper = {}, setShowImageCropper, removeQuestionImage, getCroppedImg, // PDF Generation\n    generateQuizPDF, // Components\n    QuestionFormModal, QuizFormModal, FileUploadModal } = (0,_contexts_CourseContext__WEBPACK_IMPORTED_MODULE_4__.useCourseContext)();\n    if (!lessons.length) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-2xl flex items-center justify-center mx-auto mb-6 border border-blue-200 dark:border-blue-700/50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-16 h-16 text-blue-500 dark:text-blue-400\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 1.5,\n                            d: \"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-2xl font-bold text-gray-900 dark:text-white mb-3\",\n                    children: \"لا توجد دروس بعد\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto\",\n                    children: \"ابدأ بإضافة أول درس للكورس وشارك معرفتك مع الطلاب\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-flex items-center px-4 py-2 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded-lg border border-blue-200 dark:border-blue-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 ml-2\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 4v16m8-8H4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium\",\n                            children: \"أضف درسك الأول\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: lessons.map((lesson, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"group bg-white dark:bg-gray-800 rounded-3xl shadow-lg hover:shadow-2xl border border-gray-200 dark:border-gray-700 transition-all duration-500 transform hover:-translate-y-1 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-6 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-16 h-16 bg-gradient-to-br from-blue-500 via-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg group-hover:shadow-xl transition-all duration-300\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xl\",\n                                                                children: lesson.order\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-3 h-3 text-white\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                lineNumber: 185,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-2xl font-bold text-gray-900 dark:text-white truncate\",\n                                                                    children: lesson.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"px-4 py-2 rounded-xl text-sm font-semibold shadow-sm \".concat(lesson.lesson_type === \"video\" ? \"bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-700\" : \"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700\"),\n                                                                    children: lesson.lesson_type === \"video\" ? \"📹 فيديو\" : \"📝 نصي\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        lesson.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 dark:text-gray-400 text-base mb-4 line-clamp-2 leading-relaxed\",\n                                                            children: lesson.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-6 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 px-3 py-2 bg-gray-50 dark:bg-gray-700/50 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-blue-500 dark:text-blue-400\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                lineNumber: 232,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                            lineNumber: 226,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-gray-700 dark:text-gray-300\",\n                                                                            children: lesson.duration || \"غير محدد\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                            lineNumber: 239,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                    lineNumber: 225,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                lesson.quizzes && lesson.quizzes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 px-3 py-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-purple-500 dark:text-purple-400\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                lineNumber: 251,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                            lineNumber: 245,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-purple-700 dark:text-purple-300\",\n                                                                            children: [\n                                                                                lesson.quizzes.length,\n                                                                                \" امتحان/واجب\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                            lineNumber: 258,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                    lineNumber: 244,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                lesson.resources && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 px-3 py-2 bg-green-50 dark:bg-green-900/20 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-green-500 dark:text-green-400\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                lineNumber: 271,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                            lineNumber: 265,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-green-700 dark:text-green-300\",\n                                                                            children: \"ملف مرفق\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                            lineNumber: 278,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3 flex-shrink-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleSeeLesson(lesson.id),\n                                                    className: \"group flex items-center gap-2 px-4 py-3 text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300 bg-indigo-50 dark:bg-indigo-900/20 hover:bg-indigo-100 dark:hover:bg-indigo-900/30 rounded-xl transition-all duration-300 shadow-sm hover:shadow-md\",\n                                                    title: \"عرض الدرس\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 group-hover:scale-110 transition-transform duration-200\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium hidden sm:block\",\n                                                            children: \"عرض\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setExpandedLesson(expandedLesson === lesson.id ? null : lesson.id),\n                                                    className: \"group flex items-center gap-2 px-4 py-3 text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-xl transition-all duration-300 shadow-sm hover:shadow-md\",\n                                                    title: \"إدارة المحتوى\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 transform transition-all duration-300 group-hover:scale-110 \".concat(expandedLesson === lesson.id ? \"rotate-180\" : \"\"),\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M19 9l-7 7-7-7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium hidden sm:block\",\n                                                            children: \"إدارة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteLesson(lesson.id),\n                                                    className: \"group flex items-center gap-2 px-4 py-3 text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-xl transition-all duration-300 shadow-sm hover:shadow-md\",\n                                                    title: \"حذف الدرس\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 group-hover:scale-110 transition-transform duration-200\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium hidden sm:block\",\n                                                            children: \"حذف\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                lineNumber: 174,\n                                columnNumber: 13\n                            }, undefined),\n                            expandedLesson === lesson.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-gray-200 dark:border-gray-700 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800/50 dark:to-gray-900/50 p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"group flex items-center justify-center gap-4 bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 hover:from-blue-600 hover:via-blue-700 hover:to-indigo-700 text-white px-8 py-4 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-2xl transform hover:-translate-y-1\",\n                                                onClick: ()=>{\n                                                    console.log(\"اضغط إضافة امتحان\", lesson.id);\n                                                    setShowQuizModal((prev)=>({\n                                                            ...prev,\n                                                            [lesson.id]: \"exam\"\n                                                        }));\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors duration-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg\",\n                                                        children: \"إضافة امتحان\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"group flex items-center justify-center gap-4 bg-gradient-to-r from-green-500 via-green-600 to-emerald-600 hover:from-green-600 hover:via-green-700 hover:to-emerald-700 text-white px-8 py-4 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-2xl transform hover:-translate-y-1\",\n                                                onClick: ()=>{\n                                                    console.log(\"اضغط إضافة واجب\", lesson.id);\n                                                    setShowQuizModal((prev)=>({\n                                                            ...prev,\n                                                            [lesson.id]: \"assignment\"\n                                                        }));\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors duration-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg\",\n                                                        children: \"إضافة واجب\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"group flex items-center justify-center gap-4 bg-gradient-to-r from-purple-500 via-purple-600 to-violet-600 hover:from-purple-600 hover:via-purple-700 hover:to-violet-700 text-white px-8 py-4 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-2xl transform hover:-translate-y-1\",\n                                                onClick: ()=>{\n                                                    console.log(\"اضغط رفع ملف\", lesson.id);\n                                                    setShowFileForm((prev)=>({\n                                                            ...prev,\n                                                            [lesson.id]: true\n                                                        }));\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors duration-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg\",\n                                                        children: \"رفع ملف\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    lesson.resources && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-700\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: lesson.resources,\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"flex items-center gap-3 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-semibold transition-colors duration-200 group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-blue-100 dark:bg-blue-800/50 rounded-lg group-hover:bg-blue-200 dark:group-hover:bg-blue-700/50 transition-colors duration-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"تحميل الملف المرفق\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                deletingResource[lesson.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 px-3 py-2 bg-red-50 dark:bg-red-900/20 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"animate-spin h-5 w-5 text-red-500\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    className: \"opacity-25\",\n                                                                    cx: \"12\",\n                                                                    cy: \"12\",\n                                                                    r: \"10\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"4\",\n                                                                    fill: \"none\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    className: \"opacity-75\",\n                                                                    fill: \"currentColor\",\n                                                                    d: \"M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                    lineNumber: 492,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-red-600 dark:text-red-400\",\n                                                            children: \"جاري الحذف...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 25\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteResource(lesson.id),\n                                                    className: \"group flex items-center gap-2 px-3 py-2 text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all duration-200\",\n                                                    disabled: deletingResource[lesson.id],\n                                                    title: \"حذف الملف المرفق\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 group-hover:scale-110 transition-transform duration-200\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"حذف\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    lesson.quizzes && lesson.quizzes.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 text-white\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                lineNumber: 541,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-xl font-bold text-gray-900 dark:text-white\",\n                                                        children: \"الامتحانات والواجبات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 h-px bg-gradient-to-r from-gray-300 to-transparent dark:from-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-h-[600px] overflow-auto custom-scrollbar px-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: lesson.quizzes.map((quiz)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative bg-white dark:bg-gray-800 p-6 rounded-2xl transition-all duration-300 border-2 \".concat(quiz.quiz_type === \"exam\" ? expandedQuizzes[quiz.id] === true ? \"border-blue-400 dark:border-blue-500 shadow-lg shadow-blue-200/50 dark:shadow-blue-900/30 bg-blue-50/30 dark:bg-blue-900/10 ring-1 ring-blue-200 dark:ring-blue-700\" : \"border-blue-300 dark:border-blue-600 hover:border-blue-400 dark:hover:border-blue-500 shadow-sm hover:shadow-md shadow-blue-100 dark:shadow-blue-900/20\" : expandedQuizzes[quiz.id] === true ? \"border-green-400 dark:border-green-500 shadow-lg shadow-green-200/50 dark:shadow-green-900/30 bg-green-50/30 dark:bg-green-900/10 ring-1 ring-green-200 dark:ring-green-700\" : \"border-green-300 dark:border-green-600 hover:border-green-400 dark:hover:border-green-500 shadow-sm hover:shadow-md shadow-green-100 dark:shadow-green-900/20\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute right-0 top-0 bottom-0 w-2 rounded-r-2xl \".concat(quiz.quiz_type === \"exam\" ? \"bg-gradient-to-b from-blue-400 to-blue-600\" : \"bg-gradient-to-b from-green-400 to-green-600\"),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-3 right-0 transform translate-x-1/2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-bold shadow-lg \".concat(quiz.quiz_type === \"exam\" ? quiz.is_published ? \"bg-blue-500 shadow-blue-300\" : \"bg-blue-400 shadow-blue-200 opacity-75\" : quiz.is_published ? \"bg-green-500 shadow-green-300\" : \"bg-green-400 shadow-green-200 opacity-75\"),\n                                                                                children: quiz.quiz_type === \"exam\" ? \"📝\" : \"📋\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                lineNumber: 578,\n                                                                                columnNumber: 33\n                                                                            }, undefined),\n                                                                            quiz.is_published && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute -bottom-1 -right-1 w-3 h-3 bg-emerald-500 rounded-full border-2 border-white dark:border-gray-800 shadow-sm\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-full h-full bg-emerald-400 rounded-full animate-pulse\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 594,\n                                                                                    columnNumber: 37\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                lineNumber: 593,\n                                                                                columnNumber: 35\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                        lineNumber: 577,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                    lineNumber: 570,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-3\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"px-3 py-1 rounded-lg text-sm font-semibold border \".concat(quiz.quiz_type === \"exam\" ? \"bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-700\" : \"bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-700\"),\n                                                                                            children: quiz.quiz_type === \"exam\" ? \"📝 امتحان\" : \"📋 واجب\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 603,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                            className: \"text-lg font-bold text-gray-900 dark:text-white\",\n                                                                                            children: quiz.title\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 614,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 602,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>{\n                                                                                        setExpandedQuizzes((prev)=>({\n                                                                                                ...prev,\n                                                                                                [quiz.id]: !prev[quiz.id]\n                                                                                            }));\n                                                                                    },\n                                                                                    className: \"group flex items-center gap-2 px-4 py-2 rounded-xl transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105 \".concat(expandedQuizzes[quiz.id] === true ? \"bg-gradient-to-r from-blue-500 to-indigo-600 text-white hover:from-blue-600 hover:to-indigo-700\" : \"bg-gray-50 dark:bg-gray-700/50 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-700 dark:hover:text-gray-300\"),\n                                                                                    title: expandedQuizzes[quiz.id] === true ? \"إخفاء التفاصيل\" : \"إظهار التفاصيل\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4 transform transition-all duration-300 group-hover:scale-110 \".concat(expandedQuizzes[quiz.id] === true ? \"rotate-180\" : \"\"),\n                                                                                            fill: \"none\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            viewBox: \"0 0 24 24\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M19 9l-7 7-7-7\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 647,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 637,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-sm font-medium hidden sm:block\",\n                                                                                            children: expandedQuizzes[quiz.id] === true ? \"إخفاء\" : \"إظهار\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 654,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 619,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                            lineNumber: 601,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-600 dark:text-gray-400 mb-3 leading-relaxed\",\n                                                                            children: quiz.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                            lineNumber: 661,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        expandedQuizzes[quiz.id] === true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4 animate-fade-in-down\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-4 text-sm\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2 px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-lg\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                    className: \"w-4 h-4 text-yellow-500\",\n                                                                                                    fill: \"currentColor\",\n                                                                                                    viewBox: \"0 0 20 20\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                        d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                        lineNumber: 675,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                    lineNumber: 670,\n                                                                                                    columnNumber: 39\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"font-medium text-gray-700 dark:text-gray-300\",\n                                                                                                    children: [\n                                                                                                        quiz.max_score,\n                                                                                                        \" نقطة\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                    lineNumber: 677,\n                                                                                                    columnNumber: 39\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 669,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2 px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-lg\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                    className: \"w-4 h-4 text-blue-500\",\n                                                                                                    fill: \"none\",\n                                                                                                    stroke: \"currentColor\",\n                                                                                                    viewBox: \"0 0 24 24\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                        strokeLinecap: \"round\",\n                                                                                                        strokeLinejoin: \"round\",\n                                                                                                        strokeWidth: 2,\n                                                                                                        d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                        lineNumber: 688,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                    lineNumber: 682,\n                                                                                                    columnNumber: 39\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"font-medium text-gray-700 dark:text-gray-300\",\n                                                                                                    children: [\n                                                                                                        quiz.time_limit,\n                                                                                                        \" دقيقة\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                    lineNumber: 695,\n                                                                                                    columnNumber: 39\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 681,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 668,\n                                                                                    columnNumber: 35\n                                                                                }, undefined),\n                                                                                quiz.questions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-4 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-700\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center gap-2 text-sm\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-gray-600 dark:text-gray-400\",\n                                                                                                children: \"مجموع الدرجات:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 704,\n                                                                                                columnNumber: 41\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"font-bold text-blue-600 dark:text-blue-400\",\n                                                                                                children: quiz.questions.reduce((sum, q)=>sum + Number(q.points || 0), 0)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 707,\n                                                                                                columnNumber: 41\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-gray-500 dark:text-gray-400\",\n                                                                                                children: \"من\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 714,\n                                                                                                columnNumber: 41\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"font-bold text-gray-700 dark:text-gray-300\",\n                                                                                                children: quiz.max_score\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 717,\n                                                                                                columnNumber: 41\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"px-2 py-1 rounded-md text-xs font-medium \".concat((()=>{\n                                                                                                    const total = quiz.questions.reduce((sum, q)=>sum + Number(q.points || 0), 0);\n                                                                                                    const diff = total - quiz.max_score;\n                                                                                                    if (diff === 0) return \"bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300\";\n                                                                                                    if (diff > 0) return \"bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300\";\n                                                                                                    return \"bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-300\";\n                                                                                                })()),\n                                                                                                children: (()=>{\n                                                                                                    const total = quiz.questions.reduce((sum, q)=>sum + Number(q.points || 0), 0);\n                                                                                                    const diff = total - quiz.max_score;\n                                                                                                    if (diff === 0) return \"✅ متساوي\";\n                                                                                                    if (diff > 0) return \"\\uD83D\\uDD3A زيادة \".concat(diff);\n                                                                                                    return \"\\uD83D\\uDD3B ناقص \".concat(Math.abs(diff));\n                                                                                                })()\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 720,\n                                                                                                columnNumber: 41\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                        lineNumber: 703,\n                                                                                        columnNumber: 39\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 702,\n                                                                                    columnNumber: 37\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-4\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        className: \"flex items-center gap-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md\",\n                                                                                        onClick: ()=>{\n                                                                                            setShowQuestionForm((prev)=>{\n                                                                                                // Toggle if open, else open and set default type\n                                                                                                const isOpen = !!prev[quiz.id];\n                                                                                                if (!isOpen) {\n                                                                                                    setQuestionForm((qprev)=>({\n                                                                                                            ...qprev,\n                                                                                                            [quiz.id]: {\n                                                                                                                ...qprev[quiz.id],\n                                                                                                                question_type: \"mcq\"\n                                                                                                            }\n                                                                                                        }));\n                                                                                                    setAnswersForm((aprev)=>({\n                                                                                                            ...aprev,\n                                                                                                            [quiz.id]: [\n                                                                                                                {\n                                                                                                                    text: \"\",\n                                                                                                                    is_correct: false\n                                                                                                                }\n                                                                                                            ]\n                                                                                                        }));\n                                                                                                }\n                                                                                                return {\n                                                                                                    ...prev,\n                                                                                                    [quiz.id]: !isOpen\n                                                                                                };\n                                                                                            });\n                                                                                        },\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                className: \"w-4 h-4\",\n                                                                                                fill: \"none\",\n                                                                                                stroke: \"currentColor\",\n                                                                                                viewBox: \"0 0 24 24\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                    strokeLinecap: \"round\",\n                                                                                                    strokeLinejoin: \"round\",\n                                                                                                    strokeWidth: 2,\n                                                                                                    d: \"M12 4v16m8-8H4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                    lineNumber: 787,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 781,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            showQuestionForm[quiz.id] ? \"إغلاق النموذج\" : \"إضافة سؤال\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                        lineNumber: 753,\n                                                                                        columnNumber: 37\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 752,\n                                                                                    columnNumber: 35\n                                                                                }, undefined),\n                                                                                quiz.questions && quiz.questions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-6 border-t border-gray-200 dark:border-gray-700 pt-6\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2 mb-4\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                    className: \"w-5 h-5 text-purple-500\",\n                                                                                                    fill: \"none\",\n                                                                                                    stroke: \"currentColor\",\n                                                                                                    viewBox: \"0 0 24 24\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                        strokeLinecap: \"round\",\n                                                                                                        strokeLinejoin: \"round\",\n                                                                                                        strokeWidth: 2,\n                                                                                                        d: \"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                        lineNumber: 810,\n                                                                                                        columnNumber: 43\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                    lineNumber: 804,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                                                                                    className: \"font-bold text-gray-900 dark:text-white text-lg\",\n                                                                                                    children: [\n                                                                                                        \"الأسئلة (\",\n                                                                                                        quiz.questions.length,\n                                                                                                        \")\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                    lineNumber: 817,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 803,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"space-y-4\",\n                                                                                            children: quiz.questions.map((question, idx)=>{\n                                                                                                const isEditOpen = questionEditForm && questionEditForm.quizId === quiz.id && questionEditForm.question && questionEditForm.question.id === question.id;\n                                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_2___default().Fragment), {\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700/50 dark:to-gray-800/50 rounded-xl p-4 border border-gray-200 dark:border-gray-600 hover:shadow-md transition-all duration-300\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"flex-1\",\n                                                                                                                children: [\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                        className: \"flex items-start gap-3 mb-3\",\n                                                                                                                        children: [\n                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0\",\n                                                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                                    className: \"text-white font-bold text-sm\",\n                                                                                                                                    children: question.order\n                                                                                                                                }, void 0, false, {\n                                                                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                    lineNumber: 836,\n                                                                                                                                    columnNumber: 55\n                                                                                                                                }, undefined)\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                lineNumber: 835,\n                                                                                                                                columnNumber: 53\n                                                                                                                            }, undefined),\n                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                className: \"flex-1\",\n                                                                                                                                children: [\n                                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                                                        className: \"text-gray-900 dark:text-white font-medium leading-relaxed\",\n                                                                                                                                        children: question.text\n                                                                                                                                    }, void 0, false, {\n                                                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                        lineNumber: 841,\n                                                                                                                                        columnNumber: 55\n                                                                                                                                    }, undefined),\n                                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                        className: \"flex items-center gap-3 mt-2\",\n                                                                                                                                        children: [\n                                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                                className: \"px-2 py-1 rounded-md text-xs font-medium \".concat(question.question_type === \"mcq\" || question.question_type === \"multiple_choice\" ? \"bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300\" : question.question_type === \"true_false\" ? \"bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300\" : \"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300\"),\n                                                                                                                                                children: question.question_type === \"mcq\" || question.question_type === \"multiple_choice\" ? \"اختيار من متعدد\" : question.question_type === \"true_false\" ? \"صح أو خطأ\" : \"غير محدد\"\n                                                                                                                                            }, void 0, false, {\n                                                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                                lineNumber: 845,\n                                                                                                                                                columnNumber: 57\n                                                                                                                                            }, undefined),\n                                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                                className: \"px-2 py-1 bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 rounded-md text-xs font-medium\",\n                                                                                                                                                children: [\n                                                                                                                                                    question.points,\n                                                                                                                                                    \" نقطة\"\n                                                                                                                                                ]\n                                                                                                                                            }, void 0, true, {\n                                                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                                lineNumber: 868,\n                                                                                                                                                columnNumber: 57\n                                                                                                                                            }, undefined),\n                                                                                                                                            question.image_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                                className: \"px-2 py-1 bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 rounded-md text-xs font-medium\",\n                                                                                                                                                children: \"\\uD83D\\uDCF7 صورة مرفقة\"\n                                                                                                                                            }, void 0, false, {\n                                                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                                lineNumber: 872,\n                                                                                                                                                columnNumber: 59\n                                                                                                                                            }, undefined)\n                                                                                                                                        ]\n                                                                                                                                    }, void 0, true, {\n                                                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                        lineNumber: 844,\n                                                                                                                                        columnNumber: 55\n                                                                                                                                    }, undefined)\n                                                                                                                                ]\n                                                                                                                            }, void 0, true, {\n                                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                lineNumber: 840,\n                                                                                                                                columnNumber: 53\n                                                                                                                            }, undefined)\n                                                                                                                        ]\n                                                                                                                    }, void 0, true, {\n                                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                        lineNumber: 834,\n                                                                                                                        columnNumber: 51\n                                                                                                                    }, undefined),\n                                                                                                                    question.image_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                        className: \"mt-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg\",\n                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                                                            src: question.image_url,\n                                                                                                                            alt: \"صورة السؤال\",\n                                                                                                                            className: \"max-w-full h-32 object-contain border border-gray-200 dark:border-gray-600 rounded-lg mx-auto\"\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                            lineNumber: 882,\n                                                                                                                            columnNumber: 55\n                                                                                                                        }, undefined)\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                        lineNumber: 881,\n                                                                                                                        columnNumber: 53\n                                                                                                                    }, undefined),\n                                                                                                                    (question.question_type === \"mcq\" || question.question_type === \"multiple_choice\" || question.question_type === \"true_false\") && question.answers && question.answers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                        className: \"mt-4 p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600\",\n                                                                                                                        children: [\n                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                                                                                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2\",\n                                                                                                                                children: \"الإجابات:\"\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                lineNumber: 900,\n                                                                                                                                columnNumber: 57\n                                                                                                                            }, undefined),\n                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                className: \"space-y-2\",\n                                                                                                                                children: question.answers.map((ans, ansIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                        className: \"flex items-center gap-2 p-2 rounded-lg \".concat(ans.is_correct ? \"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700\" : \"bg-gray-50 dark:bg-gray-700/50\"),\n                                                                                                                                        children: [\n                                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                                className: \"w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold \".concat(ans.is_correct ? \"bg-green-500 text-white\" : \"bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300\"),\n                                                                                                                                                children: String.fromCharCode(65 + ansIndex)\n                                                                                                                                            }, void 0, false, {\n                                                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                                lineNumber: 914,\n                                                                                                                                                columnNumber: 65\n                                                                                                                                            }, undefined),\n                                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                                                className: \"flex-1 \".concat(ans.is_correct ? \"text-green-700 dark:text-green-300 font-semibold\" : \"text-gray-700 dark:text-gray-300\"),\n                                                                                                                                                children: ans.text\n                                                                                                                                            }, void 0, false, {\n                                                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                                lineNumber: 926,\n                                                                                                                                                columnNumber: 65\n                                                                                                                                            }, undefined),\n                                                                                                                                            ans.is_correct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                                className: \"flex items-center gap-1 px-2 py-1 bg-green-100 dark:bg-green-800 text-green-700 dark:text-green-300 rounded-md text-xs font-medium\",\n                                                                                                                                                children: [\n                                                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                                                                        className: \"w-3 h-3\",\n                                                                                                                                                        fill: \"currentColor\",\n                                                                                                                                                        viewBox: \"0 0 20 20\",\n                                                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                                                                            fillRule: \"evenodd\",\n                                                                                                                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                                                                                                            clipRule: \"evenodd\"\n                                                                                                                                                        }, void 0, false, {\n                                                                                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                                            lineNumber: 942,\n                                                                                                                                                            columnNumber: 71\n                                                                                                                                                        }, undefined)\n                                                                                                                                                    }, void 0, false, {\n                                                                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                                        lineNumber: 937,\n                                                                                                                                                        columnNumber: 69\n                                                                                                                                                    }, undefined),\n                                                                                                                                                    \"صحيح\"\n                                                                                                                                                ]\n                                                                                                                                            }, void 0, true, {\n                                                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                                lineNumber: 936,\n                                                                                                                                                columnNumber: 67\n                                                                                                                                            }, undefined)\n                                                                                                                                        ]\n                                                                                                                                    }, ans.id, true, {\n                                                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                        lineNumber: 906,\n                                                                                                                                        columnNumber: 63\n                                                                                                                                    }, undefined))\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                lineNumber: 903,\n                                                                                                                                columnNumber: 57\n                                                                                                                            }, undefined)\n                                                                                                                        ]\n                                                                                                                    }, void 0, true, {\n                                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                        lineNumber: 899,\n                                                                                                                        columnNumber: 55\n                                                                                                                    }, undefined)\n                                                                                                                ]\n                                                                                                            }, void 0, true, {\n                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                lineNumber: 833,\n                                                                                                                columnNumber: 49\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"flex gap-3 mt-4\",\n                                                                                                                children: [\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                        className: \"flex items-center gap-2 bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md\",\n                                                                                                                        onClick: ()=>{\n                                                                                                                            setQuestionEditForm({\n                                                                                                                                quizId: quiz.id,\n                                                                                                                                question: {\n                                                                                                                                    ...question\n                                                                                                                                },\n                                                                                                                                answers: question.answers ? question.answers.map((a)=>({\n                                                                                                                                        ...a\n                                                                                                                                    })) : []\n                                                                                                                            });\n                                                                                                                            // عند فتح التعديل، إذا كان هناك صورة، ضعها في imagePreview\n                                                                                                                            if (question.image_url) {\n                                                                                                                                setImagePreview((prev)=>({\n                                                                                                                                        ...prev,\n                                                                                                                                        [quiz.id]: question.image_url\n                                                                                                                                    }));\n                                                                                                                            } else {\n                                                                                                                                setImagePreview((prev)=>({\n                                                                                                                                        ...prev,\n                                                                                                                                        [quiz.id]: null\n                                                                                                                                    }));\n                                                                                                                            }\n                                                                                                                            // امسح الصورة المؤقتة (cropped) عند كل فتح\n                                                                                                                            setCroppedImages((prev)=>({\n                                                                                                                                    ...prev,\n                                                                                                                                    [quiz.id]: null\n                                                                                                                                }));\n                                                                                                                            setQuestionImages((prev)=>({\n                                                                                                                                    ...prev,\n                                                                                                                                    [quiz.id]: null\n                                                                                                                                }));\n                                                                                                                        },\n                                                                                                                        children: [\n                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                                                className: \"w-4 h-4\",\n                                                                                                                                fill: \"none\",\n                                                                                                                                stroke: \"currentColor\",\n                                                                                                                                viewBox: \"0 0 24 24\",\n                                                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                                                    strokeLinecap: \"round\",\n                                                                                                                                    strokeLinejoin: \"round\",\n                                                                                                                                    strokeWidth: 2,\n                                                                                                                                    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                                                                                                }, void 0, false, {\n                                                                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                    lineNumber: 1015,\n                                                                                                                                    columnNumber: 55\n                                                                                                                                }, undefined)\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                lineNumber: 1009,\n                                                                                                                                columnNumber: 53\n                                                                                                                            }, undefined),\n                                                                                                                            \"تعديل\"\n                                                                                                                        ]\n                                                                                                                    }, void 0, true, {\n                                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                        lineNumber: 959,\n                                                                                                                        columnNumber: 51\n                                                                                                                    }, undefined),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                        className: \"flex items-center gap-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md\",\n                                                                                                                        onClick: ()=>{\n                                                                                                                            if (typeof handleDeleteQuestion === \"function\") {\n                                                                                                                                handleDeleteQuestion(quiz.id, question.id);\n                                                                                                                            }\n                                                                                                                        },\n                                                                                                                        children: [\n                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                                                className: \"w-4 h-4\",\n                                                                                                                                fill: \"none\",\n                                                                                                                                stroke: \"currentColor\",\n                                                                                                                                viewBox: \"0 0 24 24\",\n                                                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                                                    strokeLinecap: \"round\",\n                                                                                                                                    strokeLinejoin: \"round\",\n                                                                                                                                    strokeWidth: 2,\n                                                                                                                                    d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                                                                                }, void 0, false, {\n                                                                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                    lineNumber: 1044,\n                                                                                                                                    columnNumber: 55\n                                                                                                                                }, undefined)\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                lineNumber: 1038,\n                                                                                                                                columnNumber: 53\n                                                                                                                            }, undefined),\n                                                                                                                            \"حذف\"\n                                                                                                                        ]\n                                                                                                                    }, void 0, true, {\n                                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                        lineNumber: 1024,\n                                                                                                                        columnNumber: 51\n                                                                                                                    }, undefined)\n                                                                                                                ]\n                                                                                                            }, void 0, true, {\n                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                lineNumber: 958,\n                                                                                                                columnNumber: 49\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                        lineNumber: 832,\n                                                                                                        columnNumber: 47\n                                                                                                    }, undefined)\n                                                                                                }, question.id, false, {\n                                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                    lineNumber: 831,\n                                                                                                    columnNumber: 45\n                                                                                                }, undefined);\n                                                                                            })\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 821,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 802,\n                                                                                    columnNumber: 37\n                                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-6 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 text-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-8 h-8 text-gray-400 mx-auto mb-2\",\n                                                                                            fill: \"none\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            viewBox: \"0 0 24 24\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 1068,\n                                                                                                columnNumber: 41\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 1062,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                                                                            children: \"لا يوجد أسئلة بعد. ابدأ بإضافة أول سؤال للامتحان\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 1075,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 1061,\n                                                                                    columnNumber: 37\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                            lineNumber: 667,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                    lineNumber: 600,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex gap-3 mt-6 flex-wrap justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-3\",\n                                                                            children: [\n                                                                                quiz.is_published ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"flex items-center gap-2 bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md\",\n                                                                                    onClick: async ()=>{\n                                                                                        const token = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"authToken\");\n                                                                                        await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].post(\"\".concat(\"http://127.0.0.1:8000\", \"/api/quizzes/\").concat(quiz.id, \"/unpublish/\"), {}, {\n                                                                                            headers: {\n                                                                                                Authorization: \"Bearer \".concat(token)\n                                                                                            }\n                                                                                        });\n                                                                                        // تحديث الدروس بعد إلغاء النشر\n                                                                                        const { fetchInstructorLessons } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/services/instructor */ \"(app-pages-browser)/./src/services/instructor.js\"));\n                                                                                        const lessonsList = await fetchInstructorLessons(courseId, token);\n                                                                                        setLessons(lessonsList);\n                                                                                    },\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4\",\n                                                                                            fill: \"none\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            viewBox: \"0 0 24 24\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 1118,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 1112,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined),\n                                                                                        \"إلغاء النشر\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 1088,\n                                                                                    columnNumber: 35\n                                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"flex items-center gap-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md\",\n                                                                                    onClick: async ()=>{\n                                                                                        var _quiz_questions;\n                                                                                        // النظام الجديد: تحديث max_score تلقائياً - zaki alkholy\n                                                                                        const totalPoints = (_quiz_questions = quiz.questions) === null || _quiz_questions === void 0 ? void 0 : _quiz_questions.reduce((sum, q)=>sum + Number(q.points || 0), 0);\n                                                                                        // تحديث الدرجة النهائية إذا لزم الأمر\n                                                                                        if (totalPoints !== quiz.max_score) {\n                                                                                            const token = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"authToken\");\n                                                                                            await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].patch(\"\".concat(\"http://127.0.0.1:8000\", \"/api/quizzes/\").concat(quiz.id, \"/\"), {\n                                                                                                max_score: totalPoints\n                                                                                            }, {\n                                                                                                headers: {\n                                                                                                    Authorization: \"Bearer \".concat(token)\n                                                                                                }\n                                                                                            });\n                                                                                            console.log(\"✅ تم تحديث الدرجة النهائية من \".concat(quiz.max_score, \" إلى \").concat(totalPoints));\n                                                                                        }\n                                                                                        // للواجبات: التحقق من وجود أسئلة فقط\n                                                                                        if (quiz.quiz_type === \"assignment\") {\n                                                                                            if (!quiz.questions || quiz.questions.length === 0) {\n                                                                                                alert(\"⚠️ يجب إضافة أسئلة للواجب قبل النشر.\");\n                                                                                                return;\n                                                                                            }\n                                                                                        }\n                                                                                        const token = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"authToken\");\n                                                                                        try {\n                                                                                            // محاولة نشر الامتحان - zakialkholy\n                                                                                            await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].post(\"\".concat(\"http://127.0.0.1:8000\", \"/api/quizzes/\").concat(quiz.id, \"/publish/\"), {}, {\n                                                                                                headers: {\n                                                                                                    Authorization: \"Bearer \".concat(token)\n                                                                                                }\n                                                                                            });\n                                                                                            // إذا نجح النشر، تحديث قائمة الدروس - zakialkholy\n                                                                                            const { fetchInstructorLessons } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/services/instructor */ \"(app-pages-browser)/./src/services/instructor.js\"));\n                                                                                            const lessonsList = await fetchInstructorLessons(courseId, token);\n                                                                                            setLessons(lessonsList);\n                                                                                            // إظهار رسالة نجاح - zakialkholy\n                                                                                            alert(\"تم نشر الامتحان/الواجب بنجاح!\");\n                                                                                        } catch (error) {\n                                                                                            var _error_response;\n                                                                                            // التعامل مع خطأ عدم تطابق الدرجات - zakialkholy\n                                                                                            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 400) {\n                                                                                                const errorData = error.response.data;\n                                                                                                // إظهار رسالة تحذير مفصلة - zakialkholy\n                                                                                                alert(\"\".concat(errorData.error, \"\\n\\n\").concat(errorData.message, \"\\n\\n\").concat(errorData.suggestion));\n                                                                                                console.error(\"تفاصيل خطأ الدرجات:\", errorData.details);\n                                                                                            } else {\n                                                                                                var _error_response_data, _error_response1;\n                                                                                                // خطأ عام آخر - zakialkholy\n                                                                                                alert(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"حدث خطأ أثناء نشر الامتحان\");\n                                                                                            }\n                                                                                        }\n                                                                                    },\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4\",\n                                                                                            fill: \"none\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            viewBox: \"0 0 24 24\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 1222,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 1216,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined),\n                                                                                        \"نشر\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 1128,\n                                                                                    columnNumber: 35\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>generateQuizPDF(quiz, user),\n                                                                                    className: \"flex items-center gap-2 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4\",\n                                                                                            fill: \"none\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            viewBox: \"0 0 24 24\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 1244,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 1238,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        \"تحميل PDF\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 1234,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                            lineNumber: 1085,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"flex items-center gap-2 bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md\",\n                                                                                    onClick: ()=>setQuizEditForm({\n                                                                                            lessonId: lesson.id,\n                                                                                            quiz: {\n                                                                                                id: quiz.id,\n                                                                                                title: quiz.title || \"\",\n                                                                                                description: quiz.description || \"\",\n                                                                                                max_score: quiz.max_score || 100,\n                                                                                                time_limit: quiz.time_limit || 0,\n                                                                                                quiz_type: quiz.quiz_type || \"\"\n                                                                                            }\n                                                                                        }),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4\",\n                                                                                            fill: \"none\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            viewBox: \"0 0 24 24\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 1279,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 1273,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        \"تعديل\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 1257,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"flex items-center gap-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md\",\n                                                                                    onClick: ()=>handleDeleteQuiz(lesson.id, quiz.id),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4\",\n                                                                                            fill: \"none\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            viewBox: \"0 0 24 24\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 1300,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 1294,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        \"حذف\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 1288,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                            lineNumber: 1255,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                    lineNumber: 1084,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, quiz.id, true, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 27\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 p-6 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700/50 dark:to-gray-800/50 rounded-xl border-2 border-dashed border-gray-300 dark:border-gray-600 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-gray-400 dark:text-gray-500\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                        lineNumber: 1325,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                    lineNumber: 1319,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                lineNumber: 1318,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                className: \"text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2\",\n                                                children: \"لا يوجد امتحانات أو واجبات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                lineNumber: 1333,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                                children: \"ابدأ بإضافة امتحان أو واجب لهذا الدرس لتقييم فهم الطلاب\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                lineNumber: 1336,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                        lineNumber: 1317,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                lineNumber: 365,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, lesson.id, true, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, undefined),\n            lessons.map((lesson)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_2___default().Fragment), {\n                    children: [\n                        QuizFormModal && !!showQuizModal[lesson.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuizFormModal, {\n                            isOpen: !!showQuizModal[lesson.id],\n                            onClose: ()=>setShowQuizModal({}),\n                            onSubmit: ()=>handleAddQuiz(lesson.id, showQuizModal[lesson.id]),\n                            formState: quizForm[lesson.id] || {},\n                            setFormState: (updater)=>setQuizForm((prev)=>({\n                                        ...prev,\n                                        [lesson.id]: typeof updater === \"function\" ? updater(prev[lesson.id] || {}) : updater\n                                    })),\n                            loading: false,\n                            isExam: showQuizModal[lesson.id] === \"exam\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                            lineNumber: 1353,\n                            columnNumber: 13\n                        }, undefined),\n                        FileUploadModal && !!showFileForm[lesson.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileUploadModal, {\n                            isOpen: !!showFileForm[lesson.id],\n                            onClose: ()=>setShowFileForm((prev)=>({\n                                        ...prev,\n                                        [lesson.id]: false\n                                    })),\n                            onSubmit: (e)=>handleAddFile(lesson.id),\n                            fileInputRef: (el)=>fileInputRefs.current[lesson.id] = el,\n                            uploading: isUploading[lesson.id],\n                            uploadProgress: uploadProgress[lesson.id]\n                        }, void 0, false, {\n                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                            lineNumber: 1376,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, \"modals-\".concat(lesson.id), true, {\n                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                    lineNumber: 1350,\n                    columnNumber: 9\n                }, undefined)),\n            lessons.map((lesson)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_2___default().Fragment), {\n                    children: (quizEditForm === null || quizEditForm === void 0 ? void 0 : quizEditForm.lessonId) === lesson.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Components_course_QuizEditModal__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        isOpen: !!(quizEditForm && quizEditForm.lessonId === lesson.id),\n                        onClose: ()=>setQuizEditForm(null),\n                        onSubmit: ()=>handleEditQuiz(lesson.id, quizEditForm.quiz.id),\n                        formState: quizEditForm.quiz,\n                        setFormState: (updater)=>setQuizEditForm((prev)=>({\n                                    ...prev,\n                                    quiz: typeof updater === \"function\" ? updater(prev.quiz || {}) : updater\n                                })),\n                        loading: false\n                    }, void 0, false, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                        lineNumber: 1397,\n                        columnNumber: 13\n                    }, undefined)\n                }, \"quiz-edit-modal-\".concat(lesson.id), false, {\n                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                    lineNumber: 1395,\n                    columnNumber: 9\n                }, undefined)),\n            lessons.map((lesson)=>{\n                var _lesson_quizzes;\n                return (_lesson_quizzes = lesson.quizzes) === null || _lesson_quizzes === void 0 ? void 0 : _lesson_quizzes.map((quiz)=>{\n                    var _quiz_questions, _quiz_questions1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_2___default().Fragment), {\n                        children: [\n                            QuestionFormModal && showQuestionForm[quiz.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuestionFormModal, {\n                                isOpen: !!showQuestionForm[quiz.id],\n                                onClose: ()=>setShowQuestionForm((prev)=>({\n                                            ...prev,\n                                            [quiz.id]: false\n                                        })),\n                                onSubmit: (quizId, data)=>handleAddQuestion(quizId, data),\n                                quizId: quiz.id,\n                                formState: questionForm[quiz.id] || {},\n                                setFormState: (updater)=>setQuestionForm((prev)=>({\n                                            ...prev,\n                                            [quiz.id]: typeof updater === \"function\" ? updater(prev[quiz.id] || {}) : updater\n                                        })),\n                                answers: answersForm[quiz.id] || [],\n                                setAnswers: (updater)=>setAnswersForm((prev)=>({\n                                            ...prev,\n                                            [quiz.id]: typeof updater === \"function\" ? updater(prev[quiz.id] || []) : updater\n                                        })),\n                                loading: !!questionLoading[quiz.id],\n                                croppedImage: croppedImages[quiz.id],\n                                setCroppedImage: (img)=>setCroppedImages((prev)=>({\n                                            ...prev,\n                                            [quiz.id]: img\n                                        })),\n                                questionImage: questionImages[quiz.id],\n                                setQuestionImage: (img)=>setQuestionImages((prev)=>({\n                                            ...prev,\n                                            [quiz.id]: img\n                                        })),\n                                imagePreview: imagePreview[quiz.id],\n                                setImagePreview: (img)=>setImagePreview((prev)=>({\n                                            ...prev,\n                                            [quiz.id]: img\n                                        })),\n                                showImageCropper: !!showImageCropper[quiz.id],\n                                setShowImageCropper: (val)=>setShowImageCropper((prev)=>({\n                                            ...prev,\n                                            [quiz.id]: val\n                                        })),\n                                removeQuestionImage: ()=>removeQuestionImage(quiz.id),\n                                getCroppedImg: getCroppedImg,\n                                user: user,\n                                quiz: quiz,\n                                existingOrders: (_quiz_questions = quiz.questions) === null || _quiz_questions === void 0 ? void 0 : _quiz_questions.map((q)=>q.order)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                lineNumber: 1423,\n                                columnNumber: 15\n                            }, undefined),\n                            QuestionFormModal && questionEditForm && questionEditForm.quizId === quiz.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuestionFormModal, {\n                                isOpen: !!(questionEditForm && questionEditForm.quizId === quiz.id),\n                                onClose: ()=>{\n                                    setQuestionEditForm(null);\n                                    setAnswersForm({}); // امسح الإجابات المؤقتة عند غلق المودال\n                                },\n                                onSubmit: (quizId, data)=>handleEditQuestion(quizId, questionEditForm.question.id, data),\n                                quizId: quiz.id,\n                                formState: (questionEditForm === null || questionEditForm === void 0 ? void 0 : questionEditForm.question) || {},\n                                setFormState: (updater)=>setQuestionEditForm((prev)=>({\n                                            ...prev,\n                                            question: typeof updater === \"function\" ? updater((prev === null || prev === void 0 ? void 0 : prev.question) || {}) : updater\n                                        })),\n                                answers: (questionEditForm === null || questionEditForm === void 0 ? void 0 : questionEditForm.answers) || [],\n                                setAnswers: (data)=>setQuestionEditForm((prev)=>({\n                                            ...prev,\n                                            answers: data\n                                        })),\n                                loading: questionLoading[quiz.id],\n                                quiz: quiz,\n                                existingOrders: (_quiz_questions1 = quiz.questions) === null || _quiz_questions1 === void 0 ? void 0 : _quiz_questions1.map((q)=>q.order)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                lineNumber: 1494,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, \"question-modal-\".concat(quiz.id), true, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                        lineNumber: 1420,\n                        columnNumber: 11\n                    }, undefined);\n                });\n            })\n        ]\n    }, void 0, true);\n};\n_s(LessonList, \"Ibf13+xSpTb5/pWs4GbBvvtlrh8=\", false, function() {\n    return [\n        _contexts_CourseContext__WEBPACK_IMPORTED_MODULE_4__.useCourseContext\n    ];\n});\n_c = LessonList;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LessonList);\nvar _c;\n$RefreshReg$(_c, \"LessonList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/instructor/LessonList.jsx\n"));

/***/ })

});