{"pagination": {"ListByteMatchSets": {"input_token": "NextMarker", "output_token": "NextMarker", "limit_key": "Limit", "result_key": "ByteMatchSets"}, "ListIPSets": {"input_token": "NextMarker", "output_token": "NextMarker", "limit_key": "Limit", "result_key": "IPSets"}, "ListRules": {"input_token": "NextMarker", "output_token": "NextMarker", "limit_key": "Limit", "result_key": "Rules"}, "ListSizeConstraintSets": {"input_token": "NextMarker", "output_token": "NextMarker", "limit_key": "Limit", "result_key": "SizeConstraintSets"}, "ListSqlInjectionMatchSets": {"input_token": "NextMarker", "output_token": "NextMarker", "limit_key": "Limit", "result_key": "SqlInjectionMatchSets"}, "ListWebACLs": {"input_token": "NextMarker", "output_token": "NextMarker", "limit_key": "Limit", "result_key": "WebACLs"}, "ListXssMatchSets": {"input_token": "NextMarker", "output_token": "NextMarker", "limit_key": "Limit", "result_key": "XssMatchSets"}, "GetRateBasedRuleManagedKeys": {"input_token": "NextMarker", "output_token": "NextMarker", "result_key": "ManagedKeys"}, "ListActivatedRulesInRuleGroup": {"input_token": "NextMarker", "limit_key": "Limit", "output_token": "NextMarker", "result_key": "ActivatedRules"}, "ListGeoMatchSets": {"input_token": "NextMarker", "limit_key": "Limit", "output_token": "NextMarker", "result_key": "GeoMatchSets"}, "ListLoggingConfigurations": {"input_token": "NextMarker", "limit_key": "Limit", "output_token": "NextMarker", "result_key": "LoggingConfigurations"}, "ListRateBasedRules": {"input_token": "NextMarker", "limit_key": "Limit", "output_token": "NextMarker", "result_key": "Rules"}, "ListRegexMatchSets": {"input_token": "NextMarker", "limit_key": "Limit", "output_token": "NextMarker", "result_key": "RegexMatchSets"}, "ListRegexPatternSets": {"input_token": "NextMarker", "limit_key": "Limit", "output_token": "NextMarker", "result_key": "RegexPatternSets"}, "ListRuleGroups": {"input_token": "NextMarker", "limit_key": "Limit", "output_token": "NextMarker", "result_key": "RuleGroups"}, "ListSubscribedRuleGroups": {"input_token": "NextMarker", "limit_key": "Limit", "output_token": "NextMarker", "result_key": "RuleGroups"}}}