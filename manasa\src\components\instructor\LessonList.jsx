import QuizEditModal from "@/app/_Components/course/QuizEditModal";
import React from "react";
import Cookies from "js-cookie";
import axios from "axios";
import { useCourseContext } from "@/contexts/CourseContext";

/**
 * قائمة الدروس في صفحة تفاصيل الكورس للمدرب
 * @param {Object} props
 * @param {Array} props.lessons - قائمة الدروس
 * @param {number|null} props.expandedLesson - معرف الدرس المفتوح
 * @param {Function} props.setExpandedLesson - دالة لتغيير الدرس المفتوح
 * @param {Function} props.handleSeeLesson - دالة للانتقال لصفحة الدرس
 * @param {Function} props.handleDeleteLesson - دالة حذف الدرس
 * @param {Object} props.deletingResource - حالة حذف ملف الموارد لكل درس
 * @param {Function} props.handleDeleteResource - دالة حذف ملف الموارد
 * @param {Function} props.setShowFileForm - دالة إظهار فورم رفع ملف
 * @param {Object} props.showFileForm - حالة إظهار فورم رفع ملف لكل درس
 * @param {Function} props.setShowQuizModal - دالة إظهار فورم إضافة كويز
 * @param {Object} props.showQuizModal - حالة إظهار فورم إضافة كويز لكل درس
 * @param {Function} props.setQuizForm - دالة تحديث فورم الكويز
 * @param {Object} props.quizForm - بيانات فورم الكويز لكل درس
 * @param {Function} props.handleAddQuiz - دالة إضافة كويز
 * @param {Function} props.handleDeleteQuiz - دالة حذف كويز
 * @param {Function} props.setQuizEditForm - دالة تعديل بيانات الكويز
 * @param {Object} props.quizEditForm - بيانات تعديل الكويز
 * @param {Function} props.handleEditQuiz - دالة تعديل الكويز
 * @param {Function} props.handleAddFile - دالة رفع ملف
 * @param {Object} props.isUploading - حالة رفع الملف لكل درس
 * @param {Object} props.uploadProgress - تقدم رفع الملف لكل درس
 * @param {Object} props.uploadedFile - الملف المرفوع لكل درس
 * @param {Object} props.uploadError - خطأ رفع الملف لكل درس
 * @param {Object} props.uploadSuccess - نجاح رفع الملف لكل درس
 * @param {Object} props.fileInputRefs - مراجع حقول الملفات
 * @param {Component} props.QuizFormModal - مكون فورم الكويز
 * @param {Component} props.FileUploadModal - مكون رفع الملف
 * @param {Function} props.generateQuizPDF - دالة توليد PDF للامتحان
 * @param {Object} props.user - بيانات المستخدم الحالي
 * @param {Function} props.setLessons - دالة تحديث قائمة الدروس
 * @param {string} props.courseId - معرف الكورس
 *
 * يمكن تمرير المزيد من props حسب الحاجة.
 */
const LessonList = () => {
  // استخدام Context بدلاً من Props
  const {
    // Course Data
    courseData,
    courseId,
    user,

    // Lessons
    lessons = [],
    setLessons,
    expandedLesson,
    setExpandedLesson,
    expandedQuizzes = {},
    setExpandedQuizzes,

    // Navigation & Actions
    handleSeeLesson,
    handleDeleteLesson,

    // File Upload
    handleAddFile,
    handleDeleteResource,
    deletingResource = {},
    showFileForm = {},
    setShowFileForm,
    fileInputRefs,
    isUploading = {},
    uploadProgress = {},
    uploadSuccess = {},
    uploadError = {},
    uploadedFile = {},

    // Quiz Management
    showQuizModal = {},
    setShowQuizModal,
    quizForm = {},
    setQuizForm,
    handleAddQuiz,
    handleDeleteQuiz,
    handleEditQuiz,
    quizEditForm,
    setQuizEditForm,

    // Question Management
    showQuestionForm = {},
    setShowQuestionForm,
    questionForm = {},
    setQuestionForm,
    answersForm = {},
    setAnswersForm,
    questionLoading = {},
    handleAddQuestion,
    handleDeleteQuestion,
    handleEditQuestion,
    questionEditForm,
    setQuestionEditForm,

    // Image Management
    croppedImages = {},
    setCroppedImages,
    questionImages = {},
    setQuestionImages,
    imagePreview = {},
    setImagePreview,
    showImageCropper = {},
    setShowImageCropper,
    removeQuestionImage,
    getCroppedImg,

    // PDF Generation
    generateQuizPDF,

    // Components
    QuestionFormModal,
    QuizFormModal,
    FileUploadModal,
  } = useCourseContext();

  if (!lessons.length) {
    return (
      <div className="text-center py-16">
        <div className="w-32 h-32 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-2xl flex items-center justify-center mx-auto mb-6 border border-blue-200 dark:border-blue-700/50">
          <svg
            className="w-16 h-16 text-blue-500 dark:text-blue-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
            />
          </svg>
        </div>
        <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
          لا توجد دروس بعد
        </h3>
        <p className="text-lg text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
          ابدأ بإضافة أول درس للكورس وشارك معرفتك مع الطلاب
        </p>
        <div className="inline-flex items-center px-4 py-2 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded-lg border border-blue-200 dark:border-blue-700">
          <svg
            className="w-4 h-4 ml-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 4v16m8-8H4"
            />
          </svg>
          <span className="text-sm font-medium">أضف درسك الأول</span>
        </div>
      </div>
    );
  }
  return (
    <>
      <div className="space-y-8">
        {lessons.map((lesson, index) => (
          <div
            key={lesson.id}
            className="group bg-white dark:bg-gray-800 rounded-3xl shadow-lg hover:shadow-2xl border border-gray-200 dark:border-gray-700 transition-all duration-500 transform hover:-translate-y-1 overflow-hidden"
          >
            <div className="p-8">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-6 flex-1">
                  {/* Lesson Number */}
                  <div className="relative">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 via-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg group-hover:shadow-xl transition-all duration-300">
                      <span className="text-white font-bold text-xl">
                        {lesson.order}
                      </span>
                    </div>
                    <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                      <svg
                        className="w-3 h-3 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                  </div>

                  {/* Lesson Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-4 mb-3">
                      <h3 className="text-2xl font-bold text-gray-900 dark:text-white truncate">
                        {lesson.title}
                      </h3>
                      <div
                        className={`px-4 py-2 rounded-xl text-sm font-semibold shadow-sm ${
                          lesson.lesson_type === "video"
                            ? "bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-700"
                            : "bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700"
                        }`}
                      >
                        {lesson.lesson_type === "video" ? "📹 فيديو" : "📝 نصي"}
                      </div>
                    </div>

                    {/* Lesson Description */}
                    {lesson.description && (
                      <p className="text-gray-600 dark:text-gray-400 text-base mb-4 line-clamp-2 leading-relaxed">
                        {lesson.description}
                      </p>
                    )}

                    {/* Lesson Stats */}
                    <div className="flex items-center gap-6 text-sm">
                      <div className="flex items-center gap-2 px-3 py-2 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                        <svg
                          className="w-5 h-5 text-blue-500 dark:text-blue-400"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        <span className="font-medium text-gray-700 dark:text-gray-300">
                          {lesson.duration || "غير محدد"}
                        </span>
                      </div>
                      {lesson.quizzes && lesson.quizzes.length > 0 && (
                        <div className="flex items-center gap-2 px-3 py-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                          <svg
                            className="w-5 h-5 text-purple-500 dark:text-purple-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                          </svg>
                          <span className="font-medium text-purple-700 dark:text-purple-300">
                            {lesson.quizzes.length} امتحان/واجب
                          </span>
                        </div>
                      )}
                      {lesson.resources && (
                        <div className="flex items-center gap-2 px-3 py-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
                          <svg
                            className="w-5 h-5 text-green-500 dark:text-green-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
                            />
                          </svg>
                          <span className="font-medium text-green-700 dark:text-green-300">
                            ملف مرفق
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center gap-3 flex-shrink-0">
                  <button
                    onClick={() => handleSeeLesson(lesson.id)}
                    className="group flex items-center gap-2 px-4 py-3 text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300 bg-indigo-50 dark:bg-indigo-900/20 hover:bg-indigo-100 dark:hover:bg-indigo-900/30 rounded-xl transition-all duration-300 shadow-sm hover:shadow-md"
                    title="عرض الدرس"
                  >
                    <svg
                      className="w-5 h-5 group-hover:scale-110 transition-transform duration-200"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                      />
                    </svg>
                    <span className="text-sm font-medium hidden sm:block">
                      عرض
                    </span>
                  </button>
                  <button
                    onClick={() =>
                      setExpandedLesson(
                        expandedLesson === lesson.id ? null : lesson.id
                      )
                    }
                    className="group flex items-center gap-2 px-4 py-3 text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-xl transition-all duration-300 shadow-sm hover:shadow-md"
                    title="إدارة المحتوى"
                  >
                    <svg
                      className={`w-5 h-5 transform transition-all duration-300 group-hover:scale-110 ${
                        expandedLesson === lesson.id ? "rotate-180" : ""
                      }`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                    <span className="text-sm font-medium hidden sm:block">
                      إدارة
                    </span>
                  </button>
                  <button
                    onClick={() => handleDeleteLesson(lesson.id)}
                    className="group flex items-center gap-2 px-4 py-3 text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-xl transition-all duration-300 shadow-sm hover:shadow-md"
                    title="حذف الدرس"
                  >
                    <svg
                      className="w-5 h-5 group-hover:scale-110 transition-transform duration-200"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                      />
                    </svg>
                    <span className="text-sm font-medium hidden sm:block">
                      حذف
                    </span>
                  </button>
                </div>
              </div>
            </div>
            {expandedLesson === lesson.id && (
              <div className="border-t border-gray-200 dark:border-gray-700 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800/50 dark:to-gray-900/50 p-8">
                {/* Action Buttons */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  <button
                    className="group flex items-center justify-center gap-4 bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 hover:from-blue-600 hover:via-blue-700 hover:to-indigo-700 text-white px-8 py-4 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-2xl transform hover:-translate-y-1"
                    onClick={() => {
                      console.log("اضغط إضافة امتحان", lesson.id);
                      setShowQuizModal((prev) => ({
                        ...prev,
                        [lesson.id]: "exam",
                      }));
                    }}
                  >
                    <div className="p-2 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors duration-300">
                      <svg
                        className="w-6 h-6"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                    </div>
                    <span className="text-lg">إضافة امتحان</span>
                  </button>
                  <button
                    className="group flex items-center justify-center gap-4 bg-gradient-to-r from-green-500 via-green-600 to-emerald-600 hover:from-green-600 hover:via-green-700 hover:to-emerald-700 text-white px-8 py-4 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-2xl transform hover:-translate-y-1"
                    onClick={() => {
                      console.log("اضغط إضافة واجب", lesson.id);
                      setShowQuizModal((prev) => ({
                        ...prev,
                        [lesson.id]: "assignment",
                      }));
                    }}
                  >
                    <div className="p-2 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors duration-300">
                      <svg
                        className="w-6 h-6"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"
                        />
                      </svg>
                    </div>
                    <span className="text-lg">إضافة واجب</span>
                  </button>
                  <button
                    className="group flex items-center justify-center gap-4 bg-gradient-to-r from-purple-500 via-purple-600 to-violet-600 hover:from-purple-600 hover:via-purple-700 hover:to-violet-700 text-white px-8 py-4 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-2xl transform hover:-translate-y-1"
                    onClick={() => {
                      console.log("اضغط رفع ملف", lesson.id);
                      setShowFileForm((prev) => ({
                        ...prev,
                        [lesson.id]: true,
                      }));
                    }}
                  >
                    <div className="p-2 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors duration-300">
                      <svg
                        className="w-6 h-6"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                        />
                      </svg>
                    </div>
                    <span className="text-lg">رفع ملف</span>
                  </button>
                </div>
                {/* Resources */}
                {lesson.resources && (
                  <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-700">
                    <div className="flex items-center justify-between">
                      <a
                        href={lesson.resources}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-3 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-semibold transition-colors duration-200 group"
                      >
                        <div className="p-2 bg-blue-100 dark:bg-blue-800/50 rounded-lg group-hover:bg-blue-200 dark:group-hover:bg-blue-700/50 transition-colors duration-200">
                          <svg
                            className="w-5 h-5"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                          </svg>
                        </div>
                        <span>تحميل الملف المرفق</span>
                      </a>
                      {deletingResource[lesson.id] ? (
                        <div className="flex items-center gap-3 px-3 py-2 bg-red-50 dark:bg-red-900/20 rounded-lg">
                          <svg
                            className="animate-spin h-5 w-5 text-red-500"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                              fill="none"
                            />
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                            />
                          </svg>
                          <span className="text-sm font-medium text-red-600 dark:text-red-400">
                            جاري الحذف...
                          </span>
                        </div>
                      ) : (
                        <button
                          onClick={() => handleDeleteResource(lesson.id)}
                          className="group flex items-center gap-2 px-3 py-2 text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all duration-200"
                          disabled={deletingResource[lesson.id]}
                          title="حذف الملف المرفق"
                        >
                          <svg
                            className="w-5 h-5 group-hover:scale-110 transition-transform duration-200"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                            />
                          </svg>
                          <span className="text-sm font-medium">حذف</span>
                        </button>
                      )}
                    </div>
                  </div>
                )}
                {/* مودال إضافة امتحان/واجب */}
                {/* عرض المودال دائمًا مع isOpen للتحكم */}
                {/* قائمة الامتحانات والواجبات مع إدارة الأسئلة */}
                {lesson.quizzes && lesson.quizzes.length > 0 ? (
                  <div className="mt-6">
                    <div className="flex items-center gap-3 mb-6">
                      <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                        <svg
                          className="w-5 h-5 text-white"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                          />
                        </svg>
                      </div>
                      <h4 className="text-xl font-bold text-gray-900 dark:text-white">
                        الامتحانات والواجبات
                      </h4>
                      <div className="flex-1 h-px bg-gradient-to-r from-gray-300 to-transparent dark:from-gray-600"></div>
                    </div>
                    <div className="max-h-[600px] overflow-auto custom-scrollbar px-3">
                      <div className="space-y-6">
                        {lesson.quizzes.map((quiz) => (
                          <div
                            key={quiz.id}
                            className={`relative bg-white dark:bg-gray-800 p-6 rounded-2xl transition-all duration-300 border-2 ${
                              quiz.quiz_type === "exam"
                                ? expandedQuizzes[quiz.id] === true
                                  ? "border-blue-400 dark:border-blue-500 shadow-lg shadow-blue-200/50 dark:shadow-blue-900/30 bg-blue-50/30 dark:bg-blue-900/10 ring-1 ring-blue-200 dark:ring-blue-700"
                                  : "border-blue-300 dark:border-blue-600 hover:border-blue-400 dark:hover:border-blue-500 shadow-sm hover:shadow-md shadow-blue-100 dark:shadow-blue-900/20"
                                : expandedQuizzes[quiz.id] === true
                                ? "border-green-400 dark:border-green-500 shadow-lg shadow-green-200/50 dark:shadow-green-900/30 bg-green-50/30 dark:bg-green-900/10 ring-1 ring-green-200 dark:ring-green-700"
                                : "border-green-300 dark:border-green-600 hover:border-green-400 dark:hover:border-green-500 shadow-sm hover:shadow-md shadow-green-100 dark:shadow-green-900/20"
                            }`}
                          >
                            {/* شريط جانبي ملون مع أيقونة */}
                            <div
                              className={`absolute right-0 top-0 bottom-0 w-2 rounded-r-2xl ${
                                quiz.quiz_type === "exam"
                                  ? "bg-gradient-to-b from-blue-400 to-blue-600"
                                  : "bg-gradient-to-b from-green-400 to-green-600"
                              }`}
                            >
                              <div className="absolute top-3 right-0 transform translate-x-1/2">
                                <div
                                  className={`w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-bold shadow-lg ${
                                    quiz.quiz_type === "exam"
                                      ? quiz.is_published
                                        ? "bg-blue-500 shadow-blue-300"
                                        : "bg-blue-400 shadow-blue-200 opacity-75"
                                      : quiz.is_published
                                      ? "bg-green-500 shadow-green-300"
                                      : "bg-green-400 shadow-green-200 opacity-75"
                                  }`}
                                >
                                  {quiz.quiz_type === "exam" ? "📝" : "📋"}
                                </div>
                                {/* مؤشر حالة النشر */}
                                {quiz.is_published && (
                                  <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-emerald-500 rounded-full border-2 border-white dark:border-gray-800 shadow-sm">
                                    <div className="w-full h-full bg-emerald-400 rounded-full animate-pulse"></div>
                                  </div>
                                )}
                              </div>
                            </div>

                            <div className="w-full relative">
                              <div className="flex items-center justify-between mb-3">
                                <div className="flex items-center gap-3">
                                  <div
                                    className={`px-3 py-1 rounded-lg text-sm font-semibold border ${
                                      quiz.quiz_type === "exam"
                                        ? "bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-700"
                                        : "bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-700"
                                    }`}
                                  >
                                    {quiz.quiz_type === "exam"
                                      ? "📝 امتحان"
                                      : "📋 واجب"}
                                  </div>
                                  <h5 className="text-lg font-bold text-gray-900 dark:text-white">
                                    {quiz.title}
                                  </h5>
                                </div>
                                {/* زر collapse/uncollapse */}
                                <button
                                  onClick={() => {
                                    setExpandedQuizzes((prev) => ({
                                      ...prev,
                                      [quiz.id]: !prev[quiz.id],
                                    }));
                                  }}
                                  className={`group flex items-center gap-2 px-4 py-2 rounded-xl transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105 ${
                                    expandedQuizzes[quiz.id] === true
                                      ? "bg-gradient-to-r from-blue-500 to-indigo-600 text-white hover:from-blue-600 hover:to-indigo-700"
                                      : "bg-gray-50 dark:bg-gray-700/50 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-700 dark:hover:text-gray-300"
                                  }`}
                                  title={
                                    expandedQuizzes[quiz.id] === true
                                      ? "إخفاء التفاصيل"
                                      : "إظهار التفاصيل"
                                  }
                                >
                                  <svg
                                    className={`w-4 h-4 transform transition-all duration-300 group-hover:scale-110 ${
                                      expandedQuizzes[quiz.id] === true
                                        ? "rotate-180"
                                        : ""
                                    }`}
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M19 9l-7 7-7-7"
                                    />
                                  </svg>
                                  <span className="text-sm font-medium hidden sm:block">
                                    {expandedQuizzes[quiz.id] === true
                                      ? "إخفاء"
                                      : "إظهار"}
                                  </span>
                                </button>
                              </div>
                              <p className="text-gray-600 dark:text-gray-400 mb-3 leading-relaxed">
                                {quiz.description}
                              </p>

                              {/* المحتوى القابل للطي - مغلق بشكل افتراضي */}
                              {expandedQuizzes[quiz.id] === true && (
                                <div className="space-y-4 animate-fade-in-down">
                                  <div className="flex items-center gap-4 text-sm">
                                    <div className="flex items-center gap-2 px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-lg">
                                      <svg
                                        className="w-4 h-4 text-yellow-500"
                                        fill="currentColor"
                                        viewBox="0 0 20 20"
                                      >
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                      </svg>
                                      <span className="font-medium text-gray-700 dark:text-gray-300">
                                        {quiz.max_score} نقطة
                                      </span>
                                    </div>
                                    <div className="flex items-center gap-2 px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-lg">
                                      <svg
                                        className="w-4 h-4 text-blue-500"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                      >
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth={2}
                                          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                                        />
                                      </svg>
                                      <span className="font-medium text-gray-700 dark:text-gray-300">
                                        {quiz.time_limit} دقيقة
                                      </span>
                                    </div>
                                  </div>
                                  {/* تحليل الدرجات - النظام الجديد - zaki alkholy */}
                                  {quiz.questions && (
                                    <div className="mt-4 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
                                      <div className="flex items-center gap-2 text-sm">
                                        <span className="text-gray-600 dark:text-gray-400">
                                          مجموع الدرجات:
                                        </span>
                                        <span className="font-bold text-blue-600 dark:text-blue-400">
                                          {quiz.questions.reduce(
                                            (sum, q) =>
                                              sum + Number(q.points || 0),
                                            0
                                          )}
                                        </span>
                                        <span className="text-gray-500 dark:text-gray-400">
                                          من
                                        </span>
                                        <span className="font-bold text-gray-700 dark:text-gray-300">
                                          {quiz.max_score}
                                        </span>
                                        <div
                                          className={`px-2 py-1 rounded-md text-xs font-medium ${(() => {
                                            const total = quiz.questions.reduce(
                                              (sum, q) =>
                                                sum + Number(q.points || 0),
                                              0
                                            );
                                            const diff = total - quiz.max_score;
                                            if (diff === 0)
                                              return "bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300";
                                            if (diff > 0)
                                              return "bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300";
                                            return "bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-300";
                                          })()}`}
                                        >
                                          {(() => {
                                            const total = quiz.questions.reduce(
                                              (sum, q) =>
                                                sum + Number(q.points || 0),
                                              0
                                            );
                                            const diff = total - quiz.max_score;
                                            if (diff === 0) return "✅ متساوي";
                                            if (diff > 0)
                                              return `🔺 زيادة ${diff}`;
                                            return `🔻 ناقص ${Math.abs(diff)}`;
                                          })()}
                                        </div>
                                      </div>
                                    </div>
                                  )}
                                  {/* زر إضافة سؤال */}
                                  <div className="mt-4">
                                    <button
                                      className="flex items-center gap-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md"
                                      onClick={() => {
                                        setShowQuestionForm((prev) => {
                                          // Toggle if open, else open and set default type
                                          const isOpen = !!prev[quiz.id];
                                          if (!isOpen) {
                                            setQuestionForm((qprev) => ({
                                              ...qprev,
                                              [quiz.id]: {
                                                ...qprev[quiz.id],
                                                question_type: "mcq",
                                              },
                                            }));
                                            setAnswersForm((aprev) => ({
                                              ...aprev,
                                              [quiz.id]: [
                                                { text: "", is_correct: false },
                                              ],
                                            }));
                                          }
                                          return {
                                            ...prev,
                                            [quiz.id]: !isOpen,
                                          };
                                        });
                                      }}
                                    >
                                      <svg
                                        className="w-4 h-4"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                      >
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth={2}
                                          d="M12 4v16m8-8H4"
                                        />
                                      </svg>
                                      {showQuestionForm[quiz.id]
                                        ? "إغلاق النموذج"
                                        : "إضافة سؤال"}
                                    </button>
                                  </div>
                                  {/* قائمة الأسئلة */}
                                  {quiz.questions &&
                                  quiz.questions.length > 0 ? (
                                    <div className="mt-6 border-t border-gray-200 dark:border-gray-700 pt-6">
                                      <div className="flex items-center gap-2 mb-4">
                                        <svg
                                          className="w-5 h-5 text-purple-500"
                                          fill="none"
                                          stroke="currentColor"
                                          viewBox="0 0 24 24"
                                        >
                                          <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                          />
                                        </svg>
                                        <h6 className="font-bold text-gray-900 dark:text-white text-lg">
                                          الأسئلة ({quiz.questions.length})
                                        </h6>
                                      </div>
                                      <div className="space-y-4">
                                        {quiz.questions.map((question, idx) => {
                                          const isEditOpen =
                                            questionEditForm &&
                                            questionEditForm.quizId ===
                                              quiz.id &&
                                            questionEditForm.question &&
                                            questionEditForm.question.id ===
                                              question.id;
                                          return (
                                            <React.Fragment key={question.id}>
                                              <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700/50 dark:to-gray-800/50 rounded-xl p-4 border border-gray-200 dark:border-gray-600 hover:shadow-md transition-all duration-300">
                                                <div className="flex-1">
                                                  <div className="flex items-start gap-3 mb-3">
                                                    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                                      <span className="text-white font-bold text-sm">
                                                        {question.order}
                                                      </span>
                                                    </div>
                                                    <div className="flex-1">
                                                      <p className="text-gray-900 dark:text-white font-medium leading-relaxed">
                                                        {question.text}
                                                      </p>
                                                      <div className="flex items-center gap-3 mt-2">
                                                        <div
                                                          className={`px-2 py-1 rounded-md text-xs font-medium ${
                                                            question.question_type ===
                                                              "mcq" ||
                                                            question.question_type ===
                                                              "multiple_choice"
                                                              ? "bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300"
                                                              : question.question_type ===
                                                                "true_false"
                                                              ? "bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300"
                                                              : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
                                                          }`}
                                                        >
                                                          {question.question_type ===
                                                            "mcq" ||
                                                          question.question_type ===
                                                            "multiple_choice"
                                                            ? "اختيار من متعدد"
                                                            : question.question_type ===
                                                              "true_false"
                                                            ? "صح أو خطأ"
                                                            : "غير محدد"}
                                                        </div>
                                                        <div className="px-2 py-1 bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 rounded-md text-xs font-medium">
                                                          {question.points} نقطة
                                                        </div>
                                                        {question.image_url && (
                                                          <div className="px-2 py-1 bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 rounded-md text-xs font-medium">
                                                            📷 صورة مرفقة
                                                          </div>
                                                        )}
                                                      </div>
                                                    </div>
                                                  </div>
                                                  {/* عرض الصورة إذا كانت موجودة */}
                                                  {question.image_url && (
                                                    <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                                                      <img
                                                        src={question.image_url}
                                                        alt="صورة السؤال"
                                                        className="max-w-full h-32 object-contain border border-gray-200 dark:border-gray-600 rounded-lg mx-auto"
                                                      />
                                                    </div>
                                                  )}
                                                  {/* عرض الإجابات لو السؤال MCQ أو صح/خطأ */}
                                                  {(question.question_type ===
                                                    "mcq" ||
                                                    question.question_type ===
                                                      "multiple_choice" ||
                                                    question.question_type ===
                                                      "true_false") &&
                                                    question.answers &&
                                                    question.answers.length >
                                                      0 && (
                                                      <div className="mt-4 p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
                                                        <h6 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                                          الإجابات:
                                                        </h6>
                                                        <div className="space-y-2">
                                                          {question.answers.map(
                                                            (ans, ansIndex) => (
                                                              <div
                                                                key={ans.id}
                                                                className={`flex items-center gap-2 p-2 rounded-lg ${
                                                                  ans.is_correct
                                                                    ? "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700"
                                                                    : "bg-gray-50 dark:bg-gray-700/50"
                                                                }`}
                                                              >
                                                                <div
                                                                  className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                                                                    ans.is_correct
                                                                      ? "bg-green-500 text-white"
                                                                      : "bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300"
                                                                  }`}
                                                                >
                                                                  {String.fromCharCode(
                                                                    65 +
                                                                      ansIndex
                                                                  )}
                                                                </div>
                                                                <span
                                                                  className={`flex-1 ${
                                                                    ans.is_correct
                                                                      ? "text-green-700 dark:text-green-300 font-semibold"
                                                                      : "text-gray-700 dark:text-gray-300"
                                                                  }`}
                                                                >
                                                                  {ans.text}
                                                                </span>
                                                                {ans.is_correct && (
                                                                  <div className="flex items-center gap-1 px-2 py-1 bg-green-100 dark:bg-green-800 text-green-700 dark:text-green-300 rounded-md text-xs font-medium">
                                                                    <svg
                                                                      className="w-3 h-3"
                                                                      fill="currentColor"
                                                                      viewBox="0 0 20 20"
                                                                    >
                                                                      <path
                                                                        fillRule="evenodd"
                                                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                                        clipRule="evenodd"
                                                                      />
                                                                    </svg>
                                                                    صحيح
                                                                  </div>
                                                                )}
                                                              </div>
                                                            )
                                                          )}
                                                        </div>
                                                      </div>
                                                    )}
                                                </div>
                                                <div className="flex gap-3 mt-4">
                                                  <button
                                                    className="flex items-center gap-2 bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md"
                                                    onClick={() => {
                                                      setQuestionEditForm({
                                                        quizId: quiz.id,
                                                        question: {
                                                          ...question,
                                                        },
                                                        answers:
                                                          question.answers
                                                            ? question.answers.map(
                                                                (a) => ({
                                                                  ...a,
                                                                })
                                                              )
                                                            : [],
                                                      });

                                                      // عند فتح التعديل، إذا كان هناك صورة، ضعها في imagePreview
                                                      if (question.image_url) {
                                                        setImagePreview(
                                                          (prev) => ({
                                                            ...prev,
                                                            [quiz.id]:
                                                              question.image_url,
                                                          })
                                                        );
                                                      } else {
                                                        setImagePreview(
                                                          (prev) => ({
                                                            ...prev,
                                                            [quiz.id]: null,
                                                          })
                                                        );
                                                      }
                                                      // امسح الصورة المؤقتة (cropped) عند كل فتح
                                                      setCroppedImages(
                                                        (prev) => ({
                                                          ...prev,
                                                          [quiz.id]: null,
                                                        })
                                                      );
                                                      setQuestionImages(
                                                        (prev) => ({
                                                          ...prev,
                                                          [quiz.id]: null,
                                                        })
                                                      );
                                                    }}
                                                  >
                                                    <svg
                                                      className="w-4 h-4"
                                                      fill="none"
                                                      stroke="currentColor"
                                                      viewBox="0 0 24 24"
                                                    >
                                                      <path
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                        strokeWidth={2}
                                                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                                      />
                                                    </svg>
                                                    تعديل
                                                  </button>
                                                  <button
                                                    className="flex items-center gap-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md"
                                                    onClick={() => {
                                                      if (
                                                        typeof handleDeleteQuestion ===
                                                        "function"
                                                      ) {
                                                        handleDeleteQuestion(
                                                          quiz.id,
                                                          question.id
                                                        );
                                                      }
                                                    }}
                                                  >
                                                    <svg
                                                      className="w-4 h-4"
                                                      fill="none"
                                                      stroke="currentColor"
                                                      viewBox="0 0 24 24"
                                                    >
                                                      <path
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                        strokeWidth={2}
                                                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                                      />
                                                    </svg>
                                                    حذف
                                                  </button>
                                                </div>
                                              </div>
                                            </React.Fragment>
                                          );
                                        })}
                                      </div>
                                    </div>
                                  ) : (
                                    <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 text-center">
                                      <svg
                                        className="w-8 h-8 text-gray-400 mx-auto mb-2"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                      >
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth={2}
                                          d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                        />
                                      </svg>
                                      <p className="text-gray-500 dark:text-gray-400 text-sm">
                                        لا يوجد أسئلة بعد. ابدأ بإضافة أول سؤال
                                        للامتحان
                                      </p>
                                    </div>
                                  )}
                                </div>
                              )}
                            </div>
                            <div className="flex gap-3 mt-6 flex-wrap justify-between items-center">
                              <div className="flex items-center gap-3">
                                {/* أزرار نشر/إلغاء نشر الكويز */}
                                {quiz.is_published ? (
                                  <button
                                    className="flex items-center gap-2 bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md"
                                    onClick={async () => {
                                      const token = Cookies.get("authToken");
                                      await axios.post(
                                        `${process.env.NEXT_PUBLIC_API_URL}/api/quizzes/${quiz.id}/unpublish/`,
                                        {},
                                        {
                                          headers: {
                                            Authorization: `Bearer ${token}`,
                                          },
                                        }
                                      );
                                      // تحديث الدروس بعد إلغاء النشر
                                      const { fetchInstructorLessons } =
                                        await import("@/services/instructor");
                                      const lessonsList =
                                        await fetchInstructorLessons(
                                          courseId,
                                          token
                                        );
                                      setLessons(lessonsList);
                                    }}
                                  >
                                    <svg
                                      className="w-4 h-4"
                                      fill="none"
                                      stroke="currentColor"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"
                                      />
                                    </svg>
                                    إلغاء النشر
                                  </button>
                                ) : (
                                  <button
                                    className="flex items-center gap-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md"
                                    onClick={async () => {
                                      // النظام الجديد: تحديث max_score تلقائياً - zaki alkholy
                                      const totalPoints =
                                        quiz.questions?.reduce(
                                          (sum, q) =>
                                            sum + Number(q.points || 0),
                                          0
                                        );

                                      // تحديث الدرجة النهائية إذا لزم الأمر
                                      if (totalPoints !== quiz.max_score) {
                                        const token = Cookies.get("authToken");
                                        await axios.patch(
                                          `${process.env.NEXT_PUBLIC_API_URL}/api/quizzes/${quiz.id}/`,
                                          { max_score: totalPoints },
                                          {
                                            headers: {
                                              Authorization: `Bearer ${token}`,
                                            },
                                          }
                                        );
                                        console.log(
                                          `✅ تم تحديث الدرجة النهائية من ${quiz.max_score} إلى ${totalPoints}`
                                        );
                                      }

                                      // للواجبات: التحقق من وجود أسئلة فقط
                                      if (quiz.quiz_type === "assignment") {
                                        if (
                                          !quiz.questions ||
                                          quiz.questions.length === 0
                                        ) {
                                          alert(
                                            "⚠️ يجب إضافة أسئلة للواجب قبل النشر."
                                          );
                                          return;
                                        }
                                      }

                                      const token = Cookies.get("authToken");
                                      try {
                                        // محاولة نشر الامتحان - zakialkholy
                                        await axios.post(
                                          `${process.env.NEXT_PUBLIC_API_URL}/api/quizzes/${quiz.id}/publish/`,
                                          {},
                                          {
                                            headers: {
                                              Authorization: `Bearer ${token}`,
                                            },
                                          }
                                        );

                                        // إذا نجح النشر، تحديث قائمة الدروس - zakialkholy
                                        const { fetchInstructorLessons } =
                                          await import("@/services/instructor");
                                        const lessonsList =
                                          await fetchInstructorLessons(
                                            courseId,
                                            token
                                          );
                                        setLessons(lessonsList);

                                        // إظهار رسالة نجاح - zakialkholy
                                        const { toast } = await import("react-hot-toast");
                                        toast.success("تم نشر الامتحان/الواجب بنجاح!");

                                      } catch (error) {
                                        // التعامل مع خطأ عدم تطابق الدرجات - zakialkholy
                                        if (error.response?.status === 400 && error.response?.data?.error?.includes("تحذير")) {
                                          const errorData = error.response.data;
                                          const { toast } = await import("react-hot-toast");

                                          // إظهار رسالة تحذير مفصلة - zakialkholy
                                          toast.error(
                                            `${errorData.error}\n${errorData.message}\n${errorData.suggestion}`,
                                            {
                                              duration: 8000,
                                              style: {
                                                maxWidth: '500px',
                                                fontSize: '14px',
                                                lineHeight: '1.5'
                                              }
                                            }
                                          );

                                          // يمكن إضافة modal للتفاصيل أكثر لو احتجت - zakialkholy
                                          console.error("تفاصيل خطأ الدرجات:", errorData.details);

                                        } else {
                                          // خطأ عام آخر - zakialkholy
                                          const { toast } = await import("react-hot-toast");
                                          toast.error(
                                            error.response?.data?.error || "حدث خطأ أثناء نشر الامتحان"
                                          );
                                        }
                                      }
                                    }}
                                  >
                                    <svg
                                      className="w-4 h-4"
                                      fill="none"
                                      stroke="currentColor"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                      />
                                    </svg>
                                    نشر
                                  </button>
                                )}

                                {/* زر تحميل PDF */}
                                <button
                                  onClick={() => generateQuizPDF(quiz, user)}
                                  className="flex items-center gap-2 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md"
                                >
                                  <svg
                                    className="w-4 h-4"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                    />
                                  </svg>
                                  تحميل PDF
                                </button>
                              </div>

                              <div className="flex items-center gap-3">
                                {/* أزرار التعديل والحذف */}
                                <button
                                  className="flex items-center gap-2 bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md"
                                  onClick={() =>
                                    setQuizEditForm({
                                      lessonId: lesson.id,
                                      quiz: {
                                        id: quiz.id,
                                        title: quiz.title || "",
                                        description: quiz.description || "",
                                        passing_score: quiz.passing_score || 0,
                                        time_limit: quiz.time_limit || 0,
                                        quiz_type: quiz.quiz_type || "",
                                      },
                                    })
                                  }
                                >
                                  <svg
                                    className="w-4 h-4"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                    />
                                  </svg>
                                  تعديل
                                </button>
                                <button
                                  className="flex items-center gap-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md"
                                  onClick={() =>
                                    handleDeleteQuiz(lesson.id, quiz.id)
                                  }
                                >
                                  <svg
                                    className="w-4 h-4"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                    />
                                  </svg>
                                  حذف
                                </button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="mt-6 p-6 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700/50 dark:to-gray-800/50 rounded-xl border-2 border-dashed border-gray-300 dark:border-gray-600 text-center">
                    <div className="w-16 h-16 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg
                        className="w-8 h-8 text-gray-400 dark:text-gray-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                    </div>
                    <h5 className="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">
                      لا يوجد امتحانات أو واجبات
                    </h5>
                    <p className="text-gray-500 dark:text-gray-400 text-sm">
                      ابدأ بإضافة امتحان أو واجب لهذا الدرس لتقييم فهم الطلاب
                    </p>
                  </div>
                )}
                {/* يمكن إضافة المزيد من التفاصيل هنا */}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* المودالز خارج حيز الدروس لتظهر في الـ viewport */}
      {lessons.map((lesson) => (
        <React.Fragment key={`modals-${lesson.id}`}>
          {/* مودال إضافة امتحان/واجب */}
          {QuizFormModal && !!showQuizModal[lesson.id] && (
            <QuizFormModal
              isOpen={!!showQuizModal[lesson.id]}
              onClose={() => setShowQuizModal({})}
              onSubmit={() =>
                handleAddQuiz(lesson.id, showQuizModal[lesson.id])
              }
              formState={quizForm[lesson.id] || {}}
              setFormState={(updater) =>
                setQuizForm((prev) => ({
                  ...prev,
                  [lesson.id]:
                    typeof updater === "function"
                      ? updater(prev[lesson.id] || {})
                      : updater,
                }))
              }
              loading={false}
              isExam={showQuizModal[lesson.id] === "exam"}
            />
          )}

          {/* مودال رفع ملف */}
          {FileUploadModal && !!showFileForm[lesson.id] && (
            <FileUploadModal
              isOpen={!!showFileForm[lesson.id]}
              onClose={() =>
                setShowFileForm((prev) => ({
                  ...prev,
                  [lesson.id]: false,
                }))
              }
              onSubmit={(e) => handleAddFile(lesson.id)}
              fileInputRef={(el) => (fileInputRefs.current[lesson.id] = el)}
              uploading={isUploading[lesson.id]}
              uploadProgress={uploadProgress[lesson.id]}
            />
          )}
        </React.Fragment>
      ))}

      {/* مودال تعديل الامتحان/الواجب */}
      {lessons.map((lesson) => (
        <React.Fragment key={`quiz-edit-modal-${lesson.id}`}>
          {quizEditForm?.lessonId === lesson.id && (
            <QuizEditModal
              isOpen={!!(quizEditForm && quizEditForm.lessonId === lesson.id)}
              onClose={() => setQuizEditForm(null)}
              onSubmit={() => handleEditQuiz(lesson.id, quizEditForm.quiz.id)}
              formState={quizEditForm.quiz}
              setFormState={(updater) =>
                setQuizEditForm((prev) => ({
                  ...prev,
                  quiz:
                    typeof updater === "function"
                      ? updater(prev.quiz || {})
                      : updater,
                }))
              }
              loading={false}
            />
          )}
        </React.Fragment>
      ))}

      {/* مودالز الأسئلة */}
      {lessons.map((lesson) =>
        lesson.quizzes?.map((quiz) => (
          <React.Fragment key={`question-modal-${quiz.id}`}>
            {/* مودال إضافة سؤال */}
            {QuestionFormModal && showQuestionForm[quiz.id] && (
              <QuestionFormModal
                isOpen={!!showQuestionForm[quiz.id]}
                onClose={() =>
                  setShowQuestionForm((prev) => ({
                    ...prev,
                    [quiz.id]: false,
                  }))
                }
                onSubmit={(quizId, data) => handleAddQuestion(quizId, data)}
                quizId={quiz.id}
                formState={questionForm[quiz.id] || {}}
                setFormState={(updater) =>
                  setQuestionForm((prev) => ({
                    ...prev,
                    [quiz.id]:
                      typeof updater === "function"
                        ? updater(prev[quiz.id] || {})
                        : updater,
                  }))
                }
                answers={answersForm[quiz.id] || []}
                setAnswers={(updater) =>
                  setAnswersForm((prev) => ({
                    ...prev,
                    [quiz.id]:
                      typeof updater === "function"
                        ? updater(prev[quiz.id] || [])
                        : updater,
                  }))
                }
                loading={!!questionLoading[quiz.id]}
                croppedImage={croppedImages[quiz.id]}
                setCroppedImage={(img) =>
                  setCroppedImages((prev) => ({
                    ...prev,
                    [quiz.id]: img,
                  }))
                }
                questionImage={questionImages[quiz.id]}
                setQuestionImage={(img) =>
                  setQuestionImages((prev) => ({
                    ...prev,
                    [quiz.id]: img,
                  }))
                }
                imagePreview={imagePreview[quiz.id]}
                setImagePreview={(img) =>
                  setImagePreview((prev) => ({
                    ...prev,
                    [quiz.id]: img,
                  }))
                }
                showImageCropper={!!showImageCropper[quiz.id]}
                setShowImageCropper={(val) =>
                  setShowImageCropper((prev) => ({
                    ...prev,
                    [quiz.id]: val,
                  }))
                }
                removeQuestionImage={() => removeQuestionImage(quiz.id)}
                getCroppedImg={getCroppedImg}
                user={user}
                quiz={quiz}
                existingOrders={quiz.questions?.map((q) => q.order)}
              />
            )}

            {/* مودال تعديل سؤال */}
            {QuestionFormModal &&
              questionEditForm &&
              questionEditForm.quizId === quiz.id && (
                <QuestionFormModal
                  isOpen={
                    !!(questionEditForm && questionEditForm.quizId === quiz.id)
                  }
                  onClose={() => {
                    setQuestionEditForm(null);
                    setAnswersForm({}); // امسح الإجابات المؤقتة عند غلق المودال
                  }}
                  onSubmit={(quizId, data) =>
                    handleEditQuestion(
                      quizId,
                      questionEditForm.question.id,
                      data
                    )
                  }
                  quizId={quiz.id}
                  formState={questionEditForm?.question || {}}
                  setFormState={(updater) =>
                    setQuestionEditForm((prev) => ({
                      ...prev,
                      question:
                        typeof updater === "function"
                          ? updater(prev?.question || {})
                          : updater,
                    }))
                  }
                  answers={questionEditForm?.answers || []}
                  setAnswers={(data) =>
                    setQuestionEditForm((prev) => ({
                      ...prev,
                      answers: data,
                    }))
                  }
                  loading={questionLoading[quiz.id]}
                  quiz={quiz}
                  existingOrders={quiz.questions?.map((q) => q.order)}
                />
              )}
          </React.Fragment>
        ))
      )}
    </>
  );
};

export default LessonList;
