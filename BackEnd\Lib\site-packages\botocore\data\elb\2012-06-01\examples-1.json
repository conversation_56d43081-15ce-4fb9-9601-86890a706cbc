{"version": "1.0", "examples": {"AddTags": [{"input": {"LoadBalancerNames": ["my-load-balancer"], "Tags": [{"Key": "project", "Value": "lima"}, {"Key": "department", "Value": "digital-media"}]}, "comments": {"input": {}, "output": {}}, "description": "This example adds two tags to the specified load balancer.", "id": "elb-add-tags-1", "title": "To add tags to a load balancer"}], "ApplySecurityGroupsToLoadBalancer": [{"input": {"LoadBalancerName": "my-load-balancer", "SecurityGroups": ["sg-fc448899"]}, "output": {"SecurityGroups": ["sg-fc448899"]}, "comments": {"input": {}, "output": {}}, "description": "This example associates a security group with the specified load balancer in a VPC.", "id": "elb-apply-security-groups-to-load-balancer-1", "title": "To associate a security group with a load balancer in a VPC"}], "AttachLoadBalancerToSubnets": [{"input": {"LoadBalancerName": "my-load-balancer", "Subnets": ["subnet-0ecac448"]}, "output": {"Subnets": ["subnet-15aaab61", "subnet-0ecac448"]}, "comments": {"input": {}, "output": {}}, "description": "This example adds the specified subnet to the set of configured subnets for the specified load balancer.", "id": "elb-attach-load-balancer-to-subnets-1", "title": "To attach subnets to a load balancer"}], "ConfigureHealthCheck": [{"input": {"HealthCheck": {"HealthyThreshold": 2, "Interval": 30, "Target": "HTTP:80/png", "Timeout": 3, "UnhealthyThreshold": 2}, "LoadBalancerName": "my-load-balancer"}, "output": {"HealthCheck": {"HealthyThreshold": 2, "Interval": 30, "Target": "HTTP:80/png", "Timeout": 3, "UnhealthyThreshold": 2}}, "comments": {"input": {}, "output": {}}, "description": "This example specifies the health check settings used to evaluate the health of your backend EC2 instances.", "id": "elb-configure-health-check-1", "title": "To specify the health check settings for your backend EC2 instances"}], "CreateAppCookieStickinessPolicy": [{"input": {"CookieName": "my-app-cookie", "LoadBalancerName": "my-load-balancer", "PolicyName": "my-app-cookie-policy"}, "comments": {"input": {}, "output": {}}, "description": "This example generates a stickiness policy that follows the sticky session lifetimes of the application-generated cookie.", "id": "elb-create-app-cookie-stickiness-policy-1", "title": "To generate a stickiness policy for your load balancer"}], "CreateLBCookieStickinessPolicy": [{"input": {"CookieExpirationPeriod": 60, "LoadBalancerName": "my-load-balancer", "PolicyName": "my-duration-cookie-policy"}, "comments": {"input": {}, "output": {}}, "description": "This example generates a stickiness policy with sticky session lifetimes controlled by the specified expiration period.", "id": "elb-create-lb-cookie-stickiness-policy-1", "title": "To generate a duration-based stickiness policy for your load balancer"}], "CreateLoadBalancer": [{"input": {"Listeners": [{"InstancePort": 80, "InstanceProtocol": "HTTP", "LoadBalancerPort": 80, "Protocol": "HTTP"}], "LoadBalancerName": "my-load-balancer", "SecurityGroups": ["sg-a61988c3"], "Subnets": ["subnet-15aaab61"]}, "output": {"DNSName": "my-load-balancer-**********.us-west-2.elb.amazonaws.com"}, "comments": {"input": {}, "output": {}}, "description": "This example creates a load balancer with an HTTP listener in a VPC.", "id": "elb-create-load-balancer-1", "title": "To create an HTTP load balancer in a VPC"}, {"input": {"AvailabilityZones": ["us-west-2a"], "Listeners": [{"InstancePort": 80, "InstanceProtocol": "HTTP", "LoadBalancerPort": 80, "Protocol": "HTTP"}], "LoadBalancerName": "my-load-balancer"}, "output": {"DNSName": "my-load-balancer-123456789.us-west-2.elb.amazonaws.com"}, "comments": {"input": {}, "output": {}}, "description": "This example creates a load balancer with an HTTP listener in EC2-Classic.", "id": "elb-create-load-balancer-2", "title": "To create an HTTP load balancer in EC2-Classic"}, {"input": {"Listeners": [{"InstancePort": 80, "InstanceProtocol": "HTTP", "LoadBalancerPort": 80, "Protocol": "HTTP"}, {"InstancePort": 80, "InstanceProtocol": "HTTP", "LoadBalancerPort": 443, "Protocol": "HTTPS", "SSLCertificateId": "arn:aws:iam::**********12:server-certificate/my-server-cert"}], "LoadBalancerName": "my-load-balancer", "SecurityGroups": ["sg-a61988c3"], "Subnets": ["subnet-15aaab61"]}, "output": {"DNSName": "my-load-balancer-**********.us-west-2.elb.amazonaws.com"}, "comments": {"input": {}, "output": {}}, "description": "This example creates a load balancer with an HTTPS listener in a VPC.", "id": "elb-create-load-balancer-3", "title": "To create an HTTPS load balancer in a VPC"}, {"input": {"AvailabilityZones": ["us-west-2a"], "Listeners": [{"InstancePort": 80, "InstanceProtocol": "HTTP", "LoadBalancerPort": 80, "Protocol": "HTTP"}, {"InstancePort": 80, "InstanceProtocol": "HTTP", "LoadBalancerPort": 443, "Protocol": "HTTPS", "SSLCertificateId": "arn:aws:iam::**********12:server-certificate/my-server-cert"}], "LoadBalancerName": "my-load-balancer"}, "output": {"DNSName": "my-load-balancer-123456789.us-west-2.elb.amazonaws.com"}, "comments": {"input": {}, "output": {}}, "description": "This example creates a load balancer with an HTTPS listener in EC2-Classic.", "id": "elb-create-load-balancer-4", "title": "To create an HTTPS load balancer in EC2-Classic"}, {"input": {"Listeners": [{"InstancePort": 80, "InstanceProtocol": "HTTP", "LoadBalancerPort": 80, "Protocol": "HTTP"}], "LoadBalancerName": "my-load-balancer", "Scheme": "internal", "SecurityGroups": ["sg-a61988c3"], "Subnets": ["subnet-15aaab61"]}, "output": {"DNSName": "internal-my-load-balancer-123456789.us-west-2.elb.amazonaws.com"}, "comments": {"input": {}, "output": {}}, "description": "This example creates an internal load balancer with an HTTP listener in a VPC.", "id": "elb-create-load-balancer-5", "title": "To create an internal load balancer"}], "CreateLoadBalancerListeners": [{"input": {"Listeners": [{"InstancePort": 80, "InstanceProtocol": "HTTP", "LoadBalancerPort": 80, "Protocol": "HTTP"}], "LoadBalancerName": "my-load-balancer"}, "comments": {"input": {}, "output": {}}, "description": "This example creates a listener for your load balancer at port 80 using the HTTP protocol.", "id": "elb-create-load-balancer-listeners-1", "title": "To create an HTTP listener for a load balancer"}, {"input": {"Listeners": [{"InstancePort": 80, "InstanceProtocol": "HTTP", "LoadBalancerPort": 443, "Protocol": "HTTPS", "SSLCertificateId": "arn:aws:iam::**********12:server-certificate/my-server-cert"}], "LoadBalancerName": "my-load-balancer"}, "comments": {"input": {}, "output": {}}, "description": "This example creates a listener for your load balancer at port 443 using the HTTPS protocol.", "id": "elb-create-load-balancer-listeners-2", "title": "To create an HTTPS listener for a load balancer"}], "CreateLoadBalancerPolicy": [{"input": {"LoadBalancerName": "my-load-balancer", "PolicyAttributes": [{"AttributeName": "ProxyProtocol", "AttributeValue": "true"}], "PolicyName": "my-ProxyProtocol-policy", "PolicyTypeName": "ProxyProtocolPolicyType"}, "comments": {"input": {}, "output": {}}, "description": "This example creates a policy that enables Proxy Protocol on the specified load balancer.", "id": "elb-create-load-balancer-policy-1", "title": "To create a policy that enables Proxy Protocol on a load balancer"}, {"input": {"LoadBalancerName": "my-load-balancer", "PolicyAttributes": [{"AttributeName": "PublicKey", "AttributeValue": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwAYUjnfyEyXr1pxjhFWBpMlggUcqoi3kl+dS74kj//c6x7ROtusUaeQCTgIUkayttRDWchuqo1pHC1u+n5xxXnBBe2ejbb2WRsKIQ5rXEeixsjFpFsojpSQKkzhVGI6mJVZBJDVKSHmswnwLBdofLhzvllpovBPTHe+o4haAWvDBALJU0pkSI1FecPHcs2hwxf14zHoXy1e2k36A64nXW43wtfx5qcVSIxtCEOjnYRg7RPvybaGfQ+v6Iaxb/+7J5kEvZhTFQId+bSiJImF1FSUT1W1xwzBZPUbcUkkXDj45vC2s3Z8E+Lk7a3uZhvsQHLZnrfuWjBWGWvZ/MhZYgEXAMPLE"}], "PolicyName": "my-PublicKey-policy", "PolicyTypeName": "PublicKeyPolicyType"}, "comments": {"input": {}, "output": {}}, "description": "This example creates a public key policy.", "id": "elb-create-load-balancer-policy-2", "title": "To create a public key policy"}, {"input": {"LoadBalancerName": "my-load-balancer", "PolicyAttributes": [{"AttributeName": "PublicKeyPolicyName", "AttributeValue": "my-PublicKey-policy"}], "PolicyName": "my-authentication-policy", "PolicyTypeName": "BackendServerAuthenticationPolicyType"}, "comments": {"input": {}, "output": {}}, "description": "This example creates a backend server authentication policy that enables authentication on your backend instance using a public key policy.", "id": "elb-create-load-balancer-policy-3", "title": "To create a backend server authentication policy"}], "DeleteLoadBalancer": [{"input": {"LoadBalancerName": "my-load-balancer"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified load balancer.", "id": "elb-delete-load-balancer-1", "title": "To delete a load balancer"}], "DeleteLoadBalancerListeners": [{"input": {"LoadBalancerName": "my-load-balancer", "LoadBalancerPorts": [80]}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the listener for the specified port from the specified load balancer.", "id": "elb-delete-load-balancer-listeners-1", "title": "To delete a listener from your load balancer"}], "DeleteLoadBalancerPolicy": [{"input": {"LoadBalancerName": "my-load-balancer", "PolicyName": "my-duration-cookie-policy"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified policy from the specified load balancer. The policy must not be enabled on any listener.", "id": "elb-delete-load-balancer-policy-1", "title": "To delete a policy from your load balancer"}], "DeregisterInstancesFromLoadBalancer": [{"input": {"Instances": [{"InstanceId": "i-d6f6fae3"}], "LoadBalancerName": "my-load-balancer"}, "output": {"Instances": [{"InstanceId": "i-207d9717"}, {"InstanceId": "i-afefb49b"}]}, "comments": {"input": {}, "output": {}}, "description": "This example deregisters the specified instance from the specified load balancer.", "id": "elb-deregister-instances-from-load-balancer-1", "title": "To deregister instances from a load balancer"}], "DescribeInstanceHealth": [{"input": {"LoadBalancerName": "my-load-balancer"}, "output": {"InstanceStates": [{"Description": "N/A", "InstanceId": "i-207d9717", "ReasonCode": "N/A", "State": "InService"}, {"Description": "N/A", "InstanceId": "i-afefb49b", "ReasonCode": "N/A", "State": "InService"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the health of the instances for the specified load balancer.", "id": "elb-describe-instance-health-1", "title": "To describe the health of the instances for a load balancer"}], "DescribeLoadBalancerAttributes": [{"input": {"LoadBalancerName": "my-load-balancer"}, "output": {"LoadBalancerAttributes": {"AccessLog": {"Enabled": false}, "ConnectionDraining": {"Enabled": false, "Timeout": 300}, "ConnectionSettings": {"IdleTimeout": 60}, "CrossZoneLoadBalancing": {"Enabled": false}}}, "comments": {"input": {}, "output": {}}, "description": "This example describes the attributes of the specified load balancer.", "id": "elb-describe-load-balancer-attributes-1", "title": "To describe the attributes of a load balancer"}], "DescribeLoadBalancerPolicies": [{"input": {"LoadBalancerName": "my-load-balancer", "PolicyNames": ["my-authentication-policy"]}, "output": {"PolicyDescriptions": [{"PolicyAttributeDescriptions": [{"AttributeName": "PublicKeyPolicyName", "AttributeValue": "my-PublicKey-policy"}], "PolicyName": "my-authentication-policy", "PolicyTypeName": "BackendServerAuthenticationPolicyType"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the specified policy associated with the specified load balancer.", "id": "elb-describe-load-balancer-policies-1", "title": "To describe a policy associated with a load balancer"}], "DescribeLoadBalancerPolicyTypes": [{"input": {"PolicyTypeNames": ["ProxyProtocolPolicyType"]}, "output": {"PolicyTypeDescriptions": [{"Description": "Policy that controls whether to include the IP address and port of the originating request for TCP messages. This policy operates on TCP listeners only.", "PolicyAttributeTypeDescriptions": [{"AttributeName": "ProxyProtocol", "AttributeType": "Boolean", "Cardinality": "ONE"}], "PolicyTypeName": "ProxyProtocolPolicyType"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the specified load balancer policy type.", "id": "elb-describe-load-balancer-policy-types-1", "title": "To describe a load balancer policy type defined by Elastic Load Balancing"}], "DescribeLoadBalancers": [{"input": {"LoadBalancerNames": ["my-load-balancer"]}, "output": {"LoadBalancerDescriptions": [{"AvailabilityZones": ["us-west-2a"], "BackendServerDescriptions": [{"InstancePort": 80, "PolicyNames": ["my-ProxyProtocol-policy"]}], "CanonicalHostedZoneName": "my-load-balancer-**********.us-west-2.elb.amazonaws.com", "CanonicalHostedZoneNameID": "Z3DZXE0EXAMPLE", "CreatedTime": "2015-03-19T03:24:02.650Z", "DNSName": "my-load-balancer-**********.us-west-2.elb.amazonaws.com", "HealthCheck": {"HealthyThreshold": 2, "Interval": 30, "Target": "HTTP:80/png", "Timeout": 3, "UnhealthyThreshold": 2}, "Instances": [{"InstanceId": "i-207d9717"}, {"InstanceId": "i-afefb49b"}], "ListenerDescriptions": [{"Listener": {"InstancePort": 80, "InstanceProtocol": "HTTP", "LoadBalancerPort": 80, "Protocol": "HTTP"}, "PolicyNames": []}, {"Listener": {"InstancePort": 443, "InstanceProtocol": "HTTPS", "LoadBalancerPort": 443, "Protocol": "HTTPS", "SSLCertificateId": "arn:aws:iam::**********12:server-certificate/my-server-cert"}, "PolicyNames": ["ELBSecurityPolicy-2015-03"]}], "LoadBalancerName": "my-load-balancer", "Policies": {"AppCookieStickinessPolicies": [], "LBCookieStickinessPolicies": [{"CookieExpirationPeriod": 60, "PolicyName": "my-duration-cookie-policy"}], "OtherPolicies": ["my-PublicKey-policy", "my-authentication-policy", "my-SSLNegotiation-policy", "my-ProxyProtocol-policy", "ELBSecurityPolicy-2015-03"]}, "Scheme": "internet-facing", "SecurityGroups": ["sg-a61988c3"], "SourceSecurityGroup": {"GroupName": "my-elb-sg", "OwnerAlias": "**********12"}, "Subnets": ["subnet-15aaab61"], "VPCId": "vpc-a01106c2"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the specified load balancer.", "id": "elb-describe-load-balancers-1", "title": "To describe one of your load balancers"}], "DescribeTags": [{"input": {"LoadBalancerNames": ["my-load-balancer"]}, "output": {"TagDescriptions": [{"LoadBalancerName": "my-load-balancer", "Tags": [{"Key": "project", "Value": "lima"}, {"Key": "department", "Value": "digital-media"}]}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the tags for the specified load balancer.", "id": "elb-describe-tags-1", "title": "To describe the tags for a load balancer"}], "DetachLoadBalancerFromSubnets": [{"input": {"LoadBalancerName": "my-load-balancer", "Subnets": ["subnet-0ecac448"]}, "output": {"Subnets": ["subnet-15aaab61"]}, "comments": {"input": {}, "output": {}}, "description": "This example detaches the specified load balancer from the specified subnet.", "id": "elb-detach-load-balancer-from-subnets-1", "title": "To detach a load balancer from a subnet"}], "DisableAvailabilityZonesForLoadBalancer": [{"input": {"AvailabilityZones": ["us-west-2a"], "LoadBalancerName": "my-load-balancer"}, "output": {"AvailabilityZones": ["us-west-2b"]}, "comments": {"input": {}, "output": {}}, "description": "This example removes the specified Availability Zone from the set of Availability Zones for the specified load balancer.", "id": "elb-disable-availability-zones-for-load-balancer-1", "title": "To disable an Availability Zone for a load balancer"}], "EnableAvailabilityZonesForLoadBalancer": [{"input": {"AvailabilityZones": ["us-west-2b"], "LoadBalancerName": "my-load-balancer"}, "output": {"AvailabilityZones": ["us-west-2a", "us-west-2b"]}, "comments": {"input": {}, "output": {}}, "description": "This example adds the specified Availability Zone to the specified load balancer.", "id": "elb-enable-availability-zones-for-load-balancer-1", "title": "To enable an Availability Zone for a load balancer"}], "ModifyLoadBalancerAttributes": [{"input": {"LoadBalancerAttributes": {"CrossZoneLoadBalancing": {"Enabled": true}}, "LoadBalancerName": "my-load-balancer"}, "output": {"LoadBalancerAttributes": {"CrossZoneLoadBalancing": {"Enabled": true}}, "LoadBalancerName": "my-load-balancer"}, "comments": {"input": {}, "output": {}}, "description": "This example enables cross-zone load balancing for the specified load balancer.", "id": "elb-modify-load-balancer-attributes-1", "title": "To enable cross-zone load balancing"}, {"input": {"LoadBalancerAttributes": {"ConnectionDraining": {"Enabled": true, "Timeout": 300}}, "LoadBalancerName": "my-load-balancer"}, "output": {"LoadBalancerAttributes": {"ConnectionDraining": {"Enabled": true, "Timeout": 300}}, "LoadBalancerName": "my-load-balancer"}, "comments": {"input": {}, "output": {}}, "description": "This example enables connection draining for the specified load balancer.", "id": "elb-modify-load-balancer-attributes-2", "title": "To enable connection draining"}], "RegisterInstancesWithLoadBalancer": [{"input": {"Instances": [{"InstanceId": "i-d6f6fae3"}], "LoadBalancerName": "my-load-balancer"}, "output": {"Instances": [{"InstanceId": "i-d6f6fae3"}, {"InstanceId": "i-207d9717"}, {"InstanceId": "i-afefb49b"}]}, "comments": {"input": {}, "output": {}}, "description": "This example registers the specified instance with the specified load balancer.", "id": "elb-register-instances-with-load-balancer-1", "title": "To register instances with a load balancer"}], "RemoveTags": [{"input": {"LoadBalancerNames": ["my-load-balancer"], "Tags": [{"Key": "project"}]}, "comments": {"input": {}, "output": {}}, "description": "This example removes the specified tag from the specified load balancer.", "id": "elb-remove-tags-1", "title": "To remove tags from a load balancer"}], "SetLoadBalancerListenerSSLCertificate": [{"input": {"LoadBalancerName": "my-load-balancer", "LoadBalancerPort": 443, "SSLCertificateId": "arn:aws:iam::**********12:server-certificate/new-server-cert"}, "comments": {"input": {}, "output": {}}, "description": "This example replaces the existing SSL certificate for the specified HTTPS listener.", "id": "elb-set-load-balancer-listener-ssl-certificate-1", "title": "To update the SSL certificate for an HTTPS listener"}], "SetLoadBalancerPoliciesForBackendServer": [{"input": {"InstancePort": 80, "LoadBalancerName": "my-load-balancer", "PolicyNames": ["my-ProxyProtocol-policy"]}, "comments": {"input": {}, "output": {}}, "description": "This example replaces the policies that are currently associated with the specified port.", "id": "elb-set-load-balancer-policies-for-backend-server-1", "title": "To replace the policies associated with a port for a backend instance"}], "SetLoadBalancerPoliciesOfListener": [{"input": {"LoadBalancerName": "my-load-balancer", "LoadBalancerPort": 80, "PolicyNames": ["my-SSLNegotiation-policy"]}, "comments": {"input": {}, "output": {}}, "description": "This example replaces the policies that are currently associated with the specified listener.", "id": "elb-set-load-balancer-policies-of-listener-1", "title": "To replace the policies associated with a listener"}]}}