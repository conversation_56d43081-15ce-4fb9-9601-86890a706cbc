"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(pages)/instructor/dashboard/[id]/page",{

/***/ "(app-pages-browser)/./src/app/_Components/pdf/QuizPDFGenerator.jsx":
/*!******************************************************!*\
  !*** ./src/app/_Components/pdf/QuizPDFGenerator.jsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateQuizPDF: () => (/* binding */ generateQuizPDF)\n/* harmony export */ });\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jspdf */ \"(app-pages-browser)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n/* harmony import */ var jspdf_autotable__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jspdf-autotable */ \"(app-pages-browser)/./node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.mjs\");\n/* harmony import */ var _fonts_Amiri_Regular_normal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../fonts/Amiri-Regular-normal */ \"(app-pages-browser)/./src/fonts/Amiri-Regular-normal.js\");\n// ✅ ملف: src/app/_Components/pdf/QuizPDFGenerator.jsx\n\n\n\nfunction toArabicNumber(number) {\n    const arabicDigits = [\n        \"٠\",\n        \"١\",\n        \"٢\",\n        \"٣\",\n        \"٤\",\n        \"٥\",\n        \"٦\",\n        \"٧\",\n        \"٨\",\n        \"٩\"\n    ];\n    return number.toString().replace(/\\d/g, (d)=>arabicDigits[d]);\n}\nconst toBase64 = (url)=>fetch(url).then((res)=>res.blob()).then((blob)=>new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.onloadend = ()=>resolve(reader.result);\n            reader.onerror = reject;\n            reader.readAsDataURL(blob);\n        }));\n// دالة لحساب أبعاد الصورة مع الحفاظ على النسبة الأصلية\nconst calculateImageDimensions = (originalWidth, originalHeight, maxWidth, maxHeight)=>{\n    const aspectRatio = originalWidth / originalHeight;\n    let newWidth = maxWidth;\n    let newHeight = maxWidth / aspectRatio;\n    // إذا كان الارتفاع أكبر من المسموح، نقلل العرض\n    if (newHeight > maxHeight) {\n        newHeight = maxHeight;\n        newWidth = maxHeight * aspectRatio;\n    }\n    return {\n        width: newWidth,\n        height: newHeight\n    };\n};\n// دالة لتحميل الصورة والحصول على أبعادها الأصلية\nconst loadImageWithDimensions = (url)=>{\n    return new Promise((resolve, reject)=>{\n        const img = new Image();\n        img.crossOrigin = \"anonymous\";\n        img.onload = ()=>{\n            const canvas = document.createElement('canvas');\n            const ctx = canvas.getContext('2d');\n            canvas.width = img.naturalWidth;\n            canvas.height = img.naturalHeight;\n            ctx.drawImage(img, 0, 0);\n            const base64 = canvas.toDataURL('image/jpeg', 0.9); // جودة عالية\n            resolve({\n                base64,\n                originalWidth: img.naturalWidth,\n                originalHeight: img.naturalHeight\n            });\n        };\n        img.onerror = reject;\n        img.src = url;\n    });\n};\nasync function generateQuizPDF(quiz, user) {\n    // تحسين إعدادات PDF - zakialkholy\n    const doc = new jspdf__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n        orientation: \"portrait\",\n        unit: \"mm\",\n        format: \"a4\",\n        compress: true // ضغط الملف لتقليل الحجم\n    });\n    doc.setFont(\"Amiri-Regular\", \"normal\");\n    doc.setFontSize(14);\n    const pageHeight = 297;\n    const pageWidth = 210;\n    const marginHorizontal = 15;\n    const contentMarginTop = 35; // زيادة المساحة العلوية - zakialkholy\n    const contentMarginBottom = 30; // زيادة المساحة السفلية - zakialkholy\n    const labels = [\n        \"أ\",\n        \"ب\",\n        \"ج\",\n        \"د\",\n        \"هـ\",\n        \"و\"\n    ];\n    let y = contentMarginTop;\n    const estimateQuestionHeight = (q)=>{\n        let height = 10;\n        // السؤال في سطر واحد\n        height += 10;\n        // الصورة تأخذ مساحة متغيرة حسب حجمها الأصلي (حد أقصى 85)\n        if (q.image_url) height += 85;\n        // الاختيارات تحت الصورة - حساب عدد الصفوف المطلوبة\n        const answersPerRow = 2; // عدد الاختيارات في كل صف\n        const answerRows = Math.ceil(q.answers.length / answersPerRow);\n        height += answerRows * 10;\n        return height + 15;\n    };\n    const renderHeader = ()=>{\n        const topY = 15;\n        doc.setFontSize(10);\n        doc.text(\"اسم المعلم: \".concat(user.first_name), marginHorizontal + 35, topY, {\n            align: \"right\"\n        });\n        doc.text(\"\".concat(quiz.title), pageWidth / 2, topY, {\n            align: \"center\"\n        });\n        doc.text(\"رقم الهاتف: \".concat(user.phone_number), pageWidth - marginHorizontal - 35, topY, {\n            align: \"left\"\n        });\n        doc.line(marginHorizontal, topY + 4, pageWidth - marginHorizontal, topY + 4);\n        doc.setFontSize(14);\n    };\n    const renderFooter = ()=>{\n        const footerY = pageHeight - 10;\n        // ✅ خط فوق الفوتر\n        doc.line(marginHorizontal, footerY - 6, pageWidth - marginHorizontal, footerY - 6);\n        doc.setFontSize(10);\n        doc.text(\"اسم المعلم: \".concat(user.first_name), marginHorizontal + 35, footerY, {\n            align: \"right\"\n        });\n        doc.text(\"\".concat(quiz.title), pageWidth / 2, footerY, {\n            align: \"center\"\n        });\n        doc.text(\"رقم الهاتف: \".concat(user.phone_number), pageWidth - marginHorizontal - 35, footerY, {\n            align: \"left\"\n        });\n        doc.setFontSize(14);\n    };\n    renderHeader();\n    for(let i = 0; i < quiz.questions.length; i++){\n        const q = quiz.questions[i];\n        const qNumber = toArabicNumber(i + 1);\n        const estimatedHeight = estimateQuestionHeight(q);\n        if (y + estimatedHeight > pageHeight - contentMarginBottom) {\n            renderFooter();\n            doc.addPage();\n            renderHeader();\n            y = contentMarginTop;\n        }\n        const boxTopY = y;\n        let contentY = y + 5;\n        // السؤال في سطر واحد\n        const qText = \"\".concat(qNumber, \"- \").concat(q.text);\n        doc.text(qText, pageWidth - marginHorizontal - 5, contentY, {\n            align: \"right\"\n        });\n        contentY += 10;\n        let maxContentY = contentY;\n        // الصورة بجودة عالية مع الحفاظ على النسبة الأصلية\n        if (q.image_url) {\n            try {\n                const imageData = await loadImageWithDimensions(q.image_url);\n                // الحد الأقصى للعرض والارتفاع\n                const maxWidth = pageWidth - 2 * marginHorizontal - 10;\n                const maxHeight = 80; // ارتفاع أقصى معقول\n                // حساب الأبعاد مع الحفاظ على النسبة الأصلية\n                const { width: imgWidth, height: imgHeight } = calculateImageDimensions(imageData.originalWidth, imageData.originalHeight, maxWidth, maxHeight);\n                // توسيط الصورة أفقياً\n                const imgX = marginHorizontal + (maxWidth - imgWidth) / 2;\n                doc.addImage(imageData.base64, \"JPEG\", imgX, contentY, imgWidth, imgHeight);\n                contentY += imgHeight + 5; // مسافة بعد الصورة\n                maxContentY = contentY;\n            } catch (err) {\n                console.warn(\"⚠️ فشل تحميل الصورة:\", err);\n                // في حالة فشل التحميل، نستخدم الطريقة القديمة\n                try {\n                    const base64 = await toBase64(q.image_url);\n                    const imgWidth = 100; // حجم معقول\n                    const imgHeight = 60;\n                    const imgX = marginHorizontal + 5;\n                    doc.addImage(base64, \"JPEG\", imgX, contentY, imgWidth, imgHeight);\n                    contentY += imgHeight + 5;\n                    maxContentY = contentY;\n                } catch (fallbackErr) {\n                    console.warn(\"⚠️ فشل تحميل الصورة نهائياً:\", fallbackErr);\n                }\n            }\n        }\n        // الاختيارات تحت الصورة وجنب بعض\n        let answerY = contentY;\n        const answersPerRow = 2; // عدد الاختيارات في كل صف\n        const answerWidth = (pageWidth - 2 * marginHorizontal - 20) / answersPerRow; // عرض كل اختيار\n        for(let j = 0; j < q.answers.length; j += answersPerRow){\n            // الاختيار الأول (على اليمين)\n            if (q.answers[j]) {\n                const a1 = \"\".concat(labels[j] || toArabicNumber(j + 1), \"- \").concat(q.answers[j].text);\n                const a1X = pageWidth - marginHorizontal - 5;\n                doc.text(a1, a1X, answerY, {\n                    align: \"right\"\n                });\n            }\n            // الاختيار الثاني (على اليسار)\n            if (q.answers[j + 1]) {\n                const a2 = \"\".concat(labels[j + 1] || toArabicNumber(j + 2), \"- \").concat(q.answers[j + 1].text);\n                const a2X = pageWidth - marginHorizontal - answerWidth;\n                doc.text(a2, a2X, answerY, {\n                    align: \"right\"\n                });\n            }\n            answerY += 10;\n        }\n        const bottomY = Math.max(answerY, maxContentY);\n        const boxHeight = bottomY - boxTopY + 5;\n        doc.rect(marginHorizontal, boxTopY, pageWidth - 2 * marginHorizontal, boxHeight);\n        y = bottomY + 10;\n    }\n    renderFooter();\n    doc.save(\"\".concat(quiz.title, \".pdf\"));\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/_Components/pdf/QuizPDFGenerator.jsx\n"));

/***/ })

});