import uuid
import hmac
import hashlib
import time
import os
import tempfile
import re
import logging
from rest_framework.views import APIView
from django.http import Http404, JsonResponse
from django.shortcuts import render, get_object_or_404
from django.utils import timezone
from django.conf import settings
from rest_framework import status, permissions, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.parsers import <PERSON>PartParser, FormParser, JSONParser
from rest_framework.permissions import IsAuthenticated, IsAdminUser, AllowAny
from django.db.models import Q

import ffmpeg
import boto3

from main.utils.notifications import notify
from main.constants.notification_messages import (
    COURSE_CREATED,
    COURSE_DELETED_STUDENT,
    COURSE_DELETED_INSTRUCTOR,
    COURSE_UPDATED,
    VIDEO_UPLOADED,
    VIDEO_DELETED,
    ANNOUNCEMENT_NEW,
)
from main.constants.notification_types import COURSE
from main.tasks import send_bulk_notification

from ..models import (
    Course,
    Lesson,
    Enrollment,
    Certificate,
    Announcement,
    FAQ,
    Category,
    MainCategory,
)
from ..serializers_file.course_serializer import (
    CourseReadSerializer,
    CourseWriteSerializer,
    LessonSerializer,
    CertificateSerializer,
    AnnouncementSerializer,
    FAQSerializer,
    CategorySerializer,
    MainCategorySerializer,
)
from ..permissions import IsInstructor
from ..cloudinary_service import CloudinaryVideoService
from ..bunny_stream_service import (
    BunnyStreamService,
)  # خدمة Bunny Stream الجديدة - زكي الخولي


def filter_courses_for_user(user):
    if user.is_staff:
        return Course.objects.all()
    if user.is_instructor:
        # المعلم يقدر يشوف كورساته + الكورسات المنشورة + الكورسات اللي مسجل فيها كطالب - zakialkholy
        return Course.objects.filter(
            Q(instructor=user) | Q(is_published=True) | Q(students=user)
        )
    return Course.objects.filter(Q(is_published=True) | Q(students=user))


class CourseViewSet(viewsets.ModelViewSet):
    queryset = Course.objects.all()
    permission_classes = [IsAuthenticated]
    parser_classes = (MultiPartParser, FormParser, JSONParser)
    lookup_field = "slug"  # استخدام slug بدلاً من pk - zaki alkholy

    def get_permissions(self):
        # السماح فقط للمدرس بالكتابة، لكن القراءة لأي مستخدم مسجل
        if self.action in ["create", "update", "partial_update", "destroy"]:
            return [IsInstructor()]
        return [IsAuthenticated()]

    def get_serializer_class(self):
        if self.action in ["list", "retrieve"]:
            return CourseReadSerializer
        return CourseWriteSerializer

    def get_queryset(self):
        return filter_courses_for_user(self.request.user)

    def perform_create(self, serializer):
        course = serializer.save(instructor=self.request.user)
        notify(self.request.user, COURSE_CREATED(course.title), type=COURSE)

    @action(
        detail=True,
        methods=["delete"],
        url_path="delete_course",
        permission_classes=[IsAuthenticated],
    )
    def delete_course(self, request, slug=None):
        course = self.get_object()
        if course.instructor != request.user:
            return Response({"error": "غير مصرح لك بالحذف"}, status=403)
        title = course.title
        students = course.students.all()
        course.delete()
        # إشعار جماعي للطلاب عبر Celery
        send_bulk_notification.delay(
            [student.id for student in students],
            COURSE_DELETED_STUDENT(title),
            type=COURSE,
        )
        notify(request.user, COURSE_DELETED_INSTRUCTOR(title), type=COURSE)
        return Response({"message": "تم الحذف"}, status=204)

    def perform_update(self, serializer):
        course = serializer.save()
        notify(course.instructor, COURSE_UPDATED(course.title), type=COURSE)
        return course

    @action(detail=True, methods=["get"])
    def lessons(self, request, slug=None):
        course = self.get_object()
        lessons = course.lessons.all()
        serializer = LessonSerializer(lessons, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=["post"], permission_classes=[IsAuthenticated])
    def toggle_like(self, request, slug=None):
        course = self.get_object()
        user = request.user
        if course.likes.filter(id=user.id).exists():
            course.likes.remove(user)
            liked = False
        else:
            course.likes.add(user)
            liked = True
        return Response({"liked": liked, "likes_count": course.likes.count()})

    @action(detail=True, methods=["post"], permission_classes=[IsAuthenticated])
    def enroll(self, request, slug=None):
        """تسجيل الطالب في الكورس - zaki alkholy"""
        course = self.get_object()
        user = request.user

        # التحقق من أن المستخدم ليس المدرب (مؤقتاً معطل للاختبار - zaki alkholy)
        # if course.instructor == user:
        #     return Response(
        #         {"error": "المدرب لا يحتاج للتسجيل في كورسه"},
        #         status=status.HTTP_400_BAD_REQUEST
        #     )

        # التحقق من التسجيل المسبق
        if course.students.filter(id=user.id).exists():
            return Response(
                {"message": "أنت مسجل بالفعل في هذا الكورس"}, status=status.HTTP_200_OK
            )

        # إضافة الطالب للكورس
        course.students.add(user)

        # إنشاء أو تحديث سجل Enrollment
        enrollment, created = Enrollment.objects.get_or_create(
            student=user, course=course, defaults={"progress": 0, "completed": False}
        )

        return Response(
            {
                "message": "تم التسجيل في الكورس بنجاح",
                "enrolled": True,
                "enrollment_id": str(enrollment.id),
            },
            status=status.HTTP_201_CREATED if created else status.HTTP_200_OK,
        )

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=400)

    # Hossam Delete Lesson Code
    @action(
        detail=True,
        methods=["delete"],
        url_path="lessons/(?P<lesson_id>[^/.]+)",
        permission_classes=[IsAuthenticated],
    )
    def delete_lesson(self, request, slug=None, lesson_id=None):
        course = self.get_object()
        try:
            lesson = course.lessons.get(id=lesson_id)
        except Lesson.DoesNotExist:
            return Response({"error": "الدرس غير موجود"}, status=404)

        if course.instructor != request.user:
            return Response({"error": "غير مصرح لك بالحذف"}, status=403)

        lesson.delete()
        return Response({"message": "تم حذف الدرس"}, status=204)

    def get_object(self):
        """
        إضافة حماية ضد الـ slugs المكررة - zaki alkholy
        """
        queryset = self.filter_queryset(self.get_queryset())
        lookup_url_kwarg = self.lookup_url_kwarg or self.lookup_field
        filter_kwargs = {self.lookup_field: self.kwargs[lookup_url_kwarg]}

        try:
            obj = queryset.get(**filter_kwargs)
        except queryset.model.MultipleObjectsReturned:
            # في حالة وجود عدة كورسات بنفس الـ slug، اختر الأول
            obj = queryset.filter(**filter_kwargs).first()
            if not obj:
                raise Http404("الكورس غير موجود")
        except queryset.model.DoesNotExist:
            raise Http404("الكورس غير موجود")

        # التحقق من الصلاحيات
        self.check_object_permissions(self.request, obj)
        return obj


class LessonViewSet(viewsets.ModelViewSet):
    queryset = Lesson.objects.all()
    serializer_class = LessonSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = "id"  # استخدام ID للدروس - zaki alkholy

    @action(detail=True, methods=["delete"])
    def delete_resource(self, request, id=None):
        lesson = self.get_object()
        if lesson.resources:
            # استخراج public_id من الرابط
            url = lesson.resources.url
            public_id = self.extract_public_id_from_url(url)
            if public_id:
                try:
                    import cloudinary.uploader

                    cloudinary.uploader.destroy(public_id, resource_type="raw")
                except Exception as e:
                    # سجل الخطأ لكن لا توقف العملية
                    logger.error(f"Cloudinary deletion failed: {str(e)}")
            lesson.resources = None
            lesson.save()
        return Response({"message": "تم حذف الملف"}, status=status.HTTP_204_NO_CONTENT)

    def extract_public_id_from_url(self, url):
        # استخلاص الـ public_id من رابط Cloudinary
        match = re.search(r"v\d+/(.+?)\.\w+$", url)
        if match:
            return match.group(1)
        return None

    def get_queryset(self):
        """
        تحسين استعلام الدروس ليشمل فقط الدروس المسموح بها للمستخدم
        """
        user = self.request.user

        if user.is_authenticated:
            # Get lesson IDs for instructor's courses
            instructor_lessons = Lesson.objects.filter(
                course__instructor=user
            ).values_list("id", flat=True)

            # Get lesson IDs for enrolled courses
            enrolled_lessons = Lesson.objects.filter(
                course__in=user.enrolled_courses.all()
            ).values_list("id", flat=True)

            # Get lesson IDs for preview lessons
            preview_lessons = Lesson.objects.filter(is_preview=True).values_list(
                "id", flat=True
            )

            # Combine allowed lesson IDs and filter the queryset
            allowed_lesson_ids = (
                list(instructor_lessons)
                + list(enrolled_lessons)
                + list(preview_lessons)
            )

            # Ensure unique IDs
            unique_allowed_lesson_ids = list(set(allowed_lesson_ids))

            return Lesson.objects.filter(id__in=unique_allowed_lesson_ids)

        return Lesson.objects.none()

    @action(detail=True, methods=["post"])
    def upload_video(self, request, id=None):
        """
        رفع فيديو جديد - نظام مختلط بين Cloudinary و Bunny Stream - زكي الخولي
        Cloudinary: للفيديوهات الترويجية المجانية
        Bunny Stream: للفيديوهات المدفوعة المحمية
        """
        lesson = self.get_object()

        # التحقق من أن المستخدم هو المدرب المسؤول عن الدورة
        if lesson.course.instructor != request.user:
            return Response(
                {"error": "You are not authorized to upload videos for this course"},
                status=status.HTTP_403_FORBIDDEN,
            )

        if not request.FILES.get("video"):
            return Response(
                {"error": "No video file provided"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            video_file = request.FILES["video"]
            video_type = request.data.get("video_type", "premium")  # premium أو promo

            # تحديد منصة الرفع حسب نوع الفيديو - زكي الخولي
            if video_type == "promo" or lesson.is_preview:
                # رفع الفيديو الترويجي إلى Cloudinary - زكي الخولي
                result = CloudinaryVideoService.upload_promo_video(
                    video_file, folder="promo_videos"
                )

                # حفظ بيانات الفيديو الترويجي - زكي الخولي
                lesson.video = result["secure_url"]
                lesson.video_public_id = result["public_id"]
                lesson.video_platform = "cloudinary"
                lesson.video_duration = result.get("duration", 0)

                response_data = {
                    "message": "تم رفع الفيديو الترويجي بنجاح - زكي الخولي",
                    "data": {
                        "url": result["secure_url"],
                        "duration": result.get("duration"),
                        "format": result.get("format"),
                        "public_id": result["public_id"],
                        "platform": "cloudinary",
                        "video_type": "promo",
                    },
                }

            else:
                # رفع الفيديو المدفوع إلى Bunny Stream للحماية القصوى - زكي الخولي
                bunny_service = BunnyStreamService()
                result = bunny_service.upload_video(
                    video_file,
                    title=f"{lesson.course.title} - {lesson.title}",
                    lesson_id=lesson.id,
                )

                # حفظ بيانات الفيديو المحمي - زكي الخولي
                lesson.bunny_video_id = result["video_id"]
                lesson.bunny_video_title = result["title"]
                lesson.bunny_thumbnail_url = result.get("thumbnail_url")
                lesson.video_platform = "bunny_stream"
                lesson.video_duration = result.get("duration", 0)
                lesson.is_drm_protected = True
                lesson.watermark_enabled = True

                response_data = {
                    "message": "تم رفع الفيديو المحمي بنجاح - زكي الخولي",
                    "data": {
                        "video_id": result["video_id"],
                        "duration": result.get("duration"),
                        "hls_url": result.get("hls_url"),
                        "iframe_url": result.get("iframe_url"),
                        "platform": "bunny_stream",
                        "video_type": "premium",
                        "security_features": ["DRM", "Watermark", "Token Auth"],
                    },
                }

            lesson.save()
            notify(request.user, VIDEO_UPLOADED(lesson.title), type=COURSE)

            return Response(response_data, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"فشل رفع الفيديو: {str(e)} - زكي الخولي")
            return Response(
                {"error": "Failed to upload video", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=True, methods=["get"])
    def video_url(self, request, id=None):
        """
        الحصول على رابط مؤقت وآمن للفيديو - نظام مختلط - زكي الخولي
        Cloudinary: للفيديوهات الترويجية
        Bunny Stream: للفيديوهات المحمية مع watermark شخصي
        """
        lesson = self.get_object()

        # التحقق من وجود فيديو على أي منصة - زكي الخولي
        if not lesson.has_video():
            return Response(
                {"error": "No video found for this lesson"},
                status=status.HTTP_404_NOT_FOUND,
            )

        # تحديد نوع المستخدم (معلم أم طالب) - زكي الخولي
        is_instructor = lesson.course.instructor == request.user

        # التحقق من صلاحية الطلاب (ليس للمعلم) - زكي الخولي
        if not is_instructor:
            if not lesson.is_preview and lesson.is_premium_video():
                # التحقق من الاشتراك للفيديوهات المدفوعة - زكي الخولي
                if not lesson.course.students.filter(id=request.user.id).exists():
                    return Response(
                        {"error": "You must enroll in this course to access the video"},
                        status=status.HTTP_403_FORBIDDEN,
                    )

        try:
            # تحديد نوع الفيديو ومنصة الاستضافة - زكي الخولي
            if lesson.is_video_on_cloudinary():
                # فيديو ترويجي على Cloudinary (الكود القديم معدل) - زكي الخولي
                video_url = CloudinaryVideoService.get_hls_url(
                    public_id=lesson.video_public_id
                )

                return Response(
                    {
                        "video_url": video_url,
                        "expires_in": 3600,
                        "duration": lesson.video_duration,
                        "platform": "cloudinary",
                        "video_type": "promo",
                        "security_level": "basic",
                    }
                )

            elif lesson.is_video_on_bunny_stream():
                # فيديو محمي على Bunny Stream - زكي الخولي
                bunny_service = BunnyStreamService()

                if is_instructor:
                    # للمعلم: رابط بدون autoplay وبدون watermark - زكي الخولي
                    instructor_name = (
                        f"{request.user.first_name} {request.user.last_name}".strip()
                    )
                    if not instructor_name:
                        instructor_name = request.user.username

                    instructor_video = bunny_service.generate_instructor_playback_url(
                        video_id=lesson.bunny_video_id,
                        instructor_name=instructor_name,
                        expiry_hours=lesson.token_expiry_hours,
                    )

                    return Response(
                        {
                            "video_url": instructor_video["instructor_url"],
                            "expires_in": lesson.token_expiry_hours * 3600,
                            "duration": lesson.video_duration,
                            "platform": "bunny_stream",
                            "video_type": "premium",
                            "security_level": "instructor",
                            "user_type": "instructor",
                            "expires_at": instructor_video["expires_at"],
                            "security_features": [
                                "DRM Protection",
                                "Token Authentication",
                                "No Autoplay",
                            ],
                        }
                    )
                else:
                    # للطالب: رابط مع watermark شخصي - زكي الخولي
                    student_name = (
                        f"{request.user.first_name} {request.user.last_name}".strip()
                    )
                    if not student_name:
                        student_name = request.user.username

                    secure_video = bunny_service.generate_secure_playback_url(
                        video_id=lesson.bunny_video_id,
                        student_name=student_name,
                        expiry_hours=lesson.token_expiry_hours,
                    )

                    return Response(
                        {
                            "video_url": secure_video["secure_url"],
                            "expires_in": lesson.token_expiry_hours * 3600,
                            "duration": lesson.video_duration,
                            "platform": "bunny_stream",
                            "video_type": "premium",
                            "security_level": "maximum",
                            "watermark": student_name,
                            "user_type": "student",
                            "expires_at": secure_video["expires_at"],
                            "security_features": lesson.get_video_security_level()[
                                "features"
                            ],
                        }
                    )

            else:
                return Response(
                    {"error": "Video platform not supported"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        except Exception as e:
            logger.error(f"فشل في إنشاء رابط الفيديو: {str(e)} - زكي الخولي")
            return Response(
                {"error": "Failed to generate video URL", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=True, methods=["delete"])
    def delete_video(self, request, id=None):
        """
        حذف الفيديو من المنصة المناسبة - نظام مختلط - زكي الخولي
        Cloudinary: للفيديوهات الترويجية
        Bunny Stream: للفيديوهات المحمية
        """
        lesson = self.get_object()

        # التحقق من الصلاحيات
        if lesson.course.instructor != request.user:
            return Response(
                {"error": "You are not authorized to delete this video"},
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            deleted_from = None

            # حذف من Cloudinary إذا كان موجود - زكي الخولي
            if lesson.is_video_on_cloudinary():
                # حذف الفيديو الترويجي من Cloudinary (الكود القديم معدل) - زكي الخولي
                import cloudinary.uploader

                cloudinary.uploader.destroy(
                    lesson.video_public_id, resource_type="video"
                )

                # تنظيف البيانات - زكي الخولي
                lesson.video = None
                lesson.video_public_id = None
                deleted_from = "cloudinary"

            # حذف من Bunny Stream إذا كان موجود - زكي الخولي
            if lesson.is_video_on_bunny_stream():
                bunny_service = BunnyStreamService()
                bunny_service.delete_video(lesson.bunny_video_id)

                # تنظيف البيانات - زكي الخولي
                lesson.bunny_video_id = None
                lesson.bunny_video_title = None
                lesson.bunny_thumbnail_url = None
                deleted_from = "bunny_stream"

            if deleted_from:
                # تنظيف البيانات المشتركة - زكي الخولي
                lesson.video_duration = 0
                lesson.video_platform = "bunny_stream"  # الافتراضي للدروس الجديدة
                lesson.save()

                notify(request.user, VIDEO_DELETED(lesson.title), type=COURSE)

                return Response(
                    {
                        "message": f"تم حذف الفيديو من {deleted_from} بنجاح - زكي الخولي",
                        "deleted_from": deleted_from,
                        "lesson_id": lesson.id,
                    },
                    status=status.HTTP_204_NO_CONTENT,
                )
            else:
                return Response(
                    {"error": "No video associated with this lesson"},
                    status=status.HTTP_404_NOT_FOUND,
                )

        except Exception as e:
            logger.error(f"فشل حذف الفيديو: {str(e)} - زكي الخولي")
            return Response(
                {"error": "Failed to delete video", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def perform_create(self, serializer):
        lesson = serializer.save()
        # استخلاص public_id إذا تم تعيين حقل video مباشرة (مثلاً يدوياً أو عبر واجهة الإدارة)
        if lesson.video and not lesson.video_public_id:
            public_id = self.extract_public_id_from_url(str(lesson.video))
            if public_id:
                lesson.video_public_id = public_id
                lesson.save()


class LessonResourceUploadView(APIView):
    parser_classes = [MultiPartParser, FormParser]

    def post(self, request, pk):
        lesson = Lesson.objects.get(pk=pk)
        file = request.FILES.get("resources")
        if not file:
            return Response({"error": "No file provided."}, status=400)

        lesson.resources = file
        lesson.save()
        return Response({"resources": lesson.resources.url}, status=status.HTTP_200_OK)


class VideoPlayerView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, lesson_id):
        try:
            lesson = get_object_or_404(Lesson, id=lesson_id)

            # التحقق من صلاحية الوصول للدرس
            if not lesson.is_preview:
                enrollment = Enrollment.objects.filter(
                    student=request.user, course=lesson.course
                ).first()

                if not enrollment:
                    return Response(
                        {"error": "يجب عليك الاشتراك في الدورة أولاً"},
                        status=status.HTTP_403_FORBIDDEN,
                    )

            # إنشاء توكن للوصول للفيديو
            token = self.get_video_token(request.user.id, lesson_id)
            video_url = f"{lesson.video_url}?token={token}"

            # تحديث تقدم المشاهدة
            if not lesson.is_preview:
                enrollment = Enrollment.objects.get(
                    student=request.user, course=lesson.course
                )
                enrollment.last_accessed = timezone.now()
                enrollment.current_lesson = lesson
                enrollment.save()

            return render(
                request,
                "main/video_player.html",
                {"lesson": lesson, "video_url": video_url, "user": request.user},
            )
        except Exception as e:
            logger.error(f"Video player view failed: {str(e)}")
            return Response(
                {"error": "حدث خطأ أثناء تحميل الفيديو"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def get_video_token(self, user_id, lesson_id):
        timestamp = int(time.time())
        data = f"{user_id}:{lesson_id}:{timestamp}"
        secret = settings.SECRET_KEY
        token = hmac.new(
            secret.encode("utf-8"), data.encode("utf-8"), hashlib.sha256
        ).hexdigest()
        return f"{token}:{timestamp}"


def video_player(request, lesson_id):
    lesson = get_object_or_404(Lesson, id=lesson_id)
    video_url = CloudinaryVideoService.generate_secure_url(
        public_id=lesson.video_public_id, expiration=3600
    )
    return render(
        request, "main/video_player.html", {"lesson": lesson, "video_url": video_url}
    )


class CertificateViewSet(viewsets.ModelViewSet):
    queryset = Certificate.objects.all()
    serializer_class = CertificateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return self.queryset.filter(user=self.request.user)


class AnnouncementViewSet(viewsets.ModelViewSet):
    queryset = Announcement.objects.all()
    serializer_class = AnnouncementSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        course_id = self.request.query_params.get("course_id")
        if course_id:
            return self.queryset.filter(course_id=course_id)
        return self.queryset.none()

    def perform_create(self, serializer):
        announcement = serializer.save()
        course = announcement.course
        students = list(course.students.all())
        send_bulk_notification.delay(
            [student.id for student in students],
            ANNOUNCEMENT_NEW(course.title, announcement.title),
            type=COURSE,
        )


class FAQViewSet(viewsets.ModelViewSet):
    queryset = FAQ.objects.all()
    serializer_class = FAQSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        course_id = self.request.query_params.get("course_id")
        if course_id:
            return self.queryset.filter(course_id=course_id)
        return self.queryset.none()


class CategoryViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Category.objects.all()
    serializer_class = CategorySerializer
    lookup_field = "slug"  # استخدام slug للفئات - حماية مع SEO - zaki alkholy


# ======================================
# ViewSet للفئات العامة بدون authentication - zaki alkholy
# ======================================
class PublicCategoryViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet للفئات - متاح للجميع بدون authentication
    للاستخدام في الصفحة الرئيسية وصفحة عرض جميع الكورسات
    """

    queryset = Category.objects.all()
    serializer_class = CategorySerializer
    permission_classes = []  # بدون authentication
    lookup_field = "slug"


class MainCategoryViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = MainCategory.objects.all()
    serializer_class = MainCategorySerializer
    lookup_field = "slug"  # استخدام slug للفئات الرئيسية - حماية مع SEO - zaki alkholy


# ======================================
# ViewSet للكورسات العامة بدون authentication - zaki alkholy
# ======================================
class PublicCoursesViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet للكورسات المنشورة - متاح للجميع بدون authentication
    للاستخدام في الصفحة الرئيسية وصفحة عرض جميع الكورسات
    """

    serializer_class = CourseReadSerializer
    permission_classes = []  # بدون authentication
    lookup_field = "slug"

    def get_queryset(self):
        # إرجاع الكورسات المنشورة فقط
        return Course.objects.filter(is_published=True).order_by("-created_at")

    def list(self, request, *args, **kwargs):
        """
        جلب قائمة الكورسات المنشورة مع إمكانية الفلترة
        """
        queryset = self.get_queryset()

        # فلترة حسب الفئة
        category = request.query_params.get("category", None)
        if category:
            queryset = queryset.filter(category__name__icontains=category)

        # فلترة حسب المستوى
        level = request.query_params.get("level", None)
        if level:
            queryset = queryset.filter(level=level)

        # بحث في العنوان والوصف
        search = request.query_params.get("search", None)
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search)
                | Q(description__icontains=search)
                | Q(short_description__icontains=search)
            )

        # ترتيب عشوائي إذا طُلب
        randomize = request.query_params.get("randomize", "false").lower() == "true"
        if randomize:
            queryset = queryset.order_by("?")

        # تحديد عدد النتائج
        limit = request.query_params.get("limit", None)
        if limit:
            try:
                limit = int(limit)
                queryset = queryset[:limit]
            except ValueError:
                pass

        # Pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def get_object(self):
        """
        إضافة حماية ضد الـ slugs المكررة - zaki alkholy
        """
        queryset = self.filter_queryset(self.get_queryset())
        lookup_url_kwarg = self.lookup_url_kwarg or self.lookup_field
        filter_kwargs = {self.lookup_field: self.kwargs[lookup_url_kwarg]}

        try:
            obj = queryset.get(**filter_kwargs)
        except queryset.model.MultipleObjectsReturned:
            # في حالة وجود عدة كورسات بنفس الـ slug، اختر الأول
            obj = queryset.filter(**filter_kwargs).first()
            if not obj:
                raise Http404("الكورس غير موجود")
        except queryset.model.DoesNotExist:
            raise Http404("الكورس غير موجود")

        # التحقق من الصلاحيات
        self.check_object_permissions(self.request, obj)
        return obj


# class VideoProtectionService:
#     def __init__(self):
#         self.s3_client = None
#         self.mediaconvert_client = None
#         try:
#             if all(
#                 [
#                     settings.AWS_ACCESS_KEY_ID,
#                     settings.AWS_SECRET_ACCESS_KEY,
#                     settings.AWS_REGION,
#                 ]
#             ):
#                 self.s3_client = boto3.client(
#                     "s3",
#                     aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
#                     aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
#                     region_name=settings.AWS_REGION,
#                 )
#                 if settings.AWS_MEDIACONVERT_ENDPOINT:
#                     self.mediaconvert_client = boto3.client(
#                         "mediaconvert",
#                         endpoint_url=settings.AWS_MEDIACONVERT_ENDPOINT,
#                         region_name=settings.AWS_REGION,
#                     )
#         except Exception as e:
#             logger.error(f"Failed to initialize AWS clients: {str(e)}")
#             # لا نرفع الاستثناء هنا، بل نترك الخدمة تعمل بدون AWS

#     def process_video(self, video_file, user_id, watermark_text):
#         if not self.s3_client:
#             raise Exception(
#                 "AWS S3 client not initialized. Please check AWS credentials."
#             )

#         try:
#             temp_dir = tempfile.mkdtemp()
#             input_path = os.path.join(temp_dir, "input.mp4")
#             output_path = os.path.join(temp_dir, "output.mp4")

#             with open(input_path, "wb") as f:
#                 f.write(video_file.read())

#             self.add_watermark(input_path, output_path, watermark_text)

#             s3_key = f"protected_videos/{user_id}/{uuid.uuid4()}.mp4"
#             self.s3_client.upload_file(
#                 output_path, settings.AWS_STORAGE_BUCKET_NAME, s3_key
#             )

#             if self.mediaconvert_client:
#                 job = self.create_media_convert_job(s3_key)
#                 return job["Job"]["Id"]
#             else:
#                 return s3_key

#         except Exception as e:
#             logger.error(f"Failed to process video: {str(e)}")
#             raise
#         finally:
#             try:
#                 if os.path.exists(input_path):
#                     os.remove(input_path)
#                 if os.path.exists(output_path):
#                     os.remove(output_path)
#                 if os.path.exists(temp_dir):
#                     os.rmdir(temp_dir)
#             except Exception as e:
#                 logger.error(f"Failed to clean up temp files: {str(e)}")

#     def add_watermark(self, input_path, output_path, text):
#         try:
#             stream = ffmpeg.input(input_path)
#             stream = ffmpeg.drawtext(
#                 stream,
#                 text=text,
#                 fontfile=settings.FONT_PATH,
#                 fontsize=24,
#                 fontcolor="white",
#                 x="(w-text_w)-20",
#                 y="(h-text_h)-20",
#                 box=1,
#                 boxcolor="black@0.5",
#                 boxborderw=5,
#             )
#             stream = ffmpeg.output(stream, output_path)
#             ffmpeg.run(stream, overwrite_output=True)
#         except Exception as e:
#             logger.error(f"Failed to add watermark: {str(e)}")
#             raise

#     def create_media_convert_job(self, s3_key):
#         try:
#             job_settings = {
#                 "OutputGroups": [
#                     {
#                         "Name": "HLS Output",
#                         "OutputGroupSettings": {
#                             "HlsGroupSettings": {
#                                 "Destination": f"s3://{settings.AWS_STORAGE_BUCKET_NAME}/protected_videos/",
#                                 "SegmentLength": 10,
#                                 "Encryption": {
#                                     "Mode": "AES_128",
#                                     "KeyProviderSettings": {
#                                         "StaticKeySettings": {
#                                             "KeyFormat": "identity",
#                                             "KeyFormatVersions": "1",
#                                             "StaticKeyValue": settings.VIDEO_ENCRYPTION_KEY,
#                                         }
#                                     },
#                                 },
#                             }
#                         },
#                     }
#                 ],
#                 "Inputs": [
#                     {"FileInput": f"s3://{settings.AWS_STORAGE_BUCKET_NAME}/{s3_key}"}
#                 ],
#             }

#             return self.mediaconvert_client.create_job(
#                 Role=settings.AWS_MEDIACONVERT_ROLE, Settings=job_settings
#             )
#         except Exception as e:
#             logger.error(f"Failed to create media convert job: {str(e)}")
#             raise
