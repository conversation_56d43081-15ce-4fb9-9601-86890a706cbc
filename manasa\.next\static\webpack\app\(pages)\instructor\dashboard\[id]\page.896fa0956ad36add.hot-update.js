"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(pages)/instructor/dashboard/[id]/page",{

/***/ "(app-pages-browser)/./src/components/instructor/CourseHeader.jsx":
/*!****************************************************!*\
  !*** ./src/components/instructor/CourseHeader.jsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _config_environment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../config/environment */ \"(app-pages-browser)/./src/config/environment.js\");\n\n\n\n\nconst CourseHeader = (param)=>{\n    let { courseId, courseData, publishing, togglePublish, setShowEditModal } = param;\n    var _courseData_category;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl lg:text-4xl font-bold text-white\",\n                                children: (courseData === null || courseData === void 0 ? void 0 : courseData.title) || \"تفاصيل الكورس\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                lineNumber: 17,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-3 py-1 rounded-full text-sm font-medium \".concat((courseData === null || courseData === void 0 ? void 0 : courseData.is_published) ? \"bg-green-500/20 text-green-100 border border-green-400/30\" : \"bg-yellow-500/20 text-yellow-100 border border-yellow-400/30\"),\n                                children: (courseData === null || courseData === void 0 ? void 0 : courseData.is_published) ? \"منشور\" : \"مسودة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                lineNumber: 20,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/80 text-lg max-w-2xl\",\n                        children: (courseData === null || courseData === void 0 ? void 0 : courseData.short_description) || (courseData === null || courseData === void 0 ? void 0 : courseData.description) || \"إدارة وتحرير محتوى الكورس\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-6 mt-4 text-white/70\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            (courseData === null || courseData === void 0 ? void 0 : courseData.students_count) || 0,\n                                            \" طالب\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                lineNumber: 36,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: (courseData === null || courseData === void 0 ? void 0 : courseData.rating) || \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: (courseData === null || courseData === void 0 ? void 0 : (_courseData_category = courseData.category) === null || _courseData_category === void 0 ? void 0 : _courseData_category.name) || \"غير محدد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/instructor/dashboard/\".concat(courseId, \"/add-lesson\"),\n                        className: \"bg-white/10 backdrop-blur-sm text-white px-6 py-3 rounded-xl hover:bg-white/20 transition-all duration-300 border border-white/20 flex items-center gap-2 font-medium\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, undefined),\n                            \"إضافة درس جديد\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"bg-white/10 backdrop-blur-sm text-white px-6 py-3 rounded-xl hover:bg-white/20 transition-all duration-300 border border-white/20 flex items-center gap-2 font-medium\",\n                        onClick: ()=>setShowEditModal(true),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, undefined),\n                            \"تعديل الكورس\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: togglePublish,\n                        disabled: publishing,\n                        className: \"px-6 py-3 rounded-xl font-medium transition-all duration-300 flex items-center gap-2 \".concat((courseData === null || courseData === void 0 ? void 0 : courseData.is_published) ? \"bg-red-500/20 text-red-100 border border-red-400/30 hover:bg-red-500/30\" : \"bg-green-500/20 text-green-100 border border-green-400/30 hover:bg-green-500/30\", \" \").concat(publishing ? \"opacity-50 cursor-not-allowed\" : \"\"),\n                        children: publishing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 animate-spin\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"جاري التحديث...\"\n                            ]\n                        }, void 0, true) : (courseData === null || courseData === void 0 ? void 0 : courseData.is_published) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"إلغاء النشر\"\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"نشر الكورس\"\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, undefined);\n};\n_c = CourseHeader;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CourseHeader);\nvar _c;\n$RefreshReg$(_c, \"CourseHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/instructor/CourseHeader.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/config/environment.js":
/*!***********************************!*\
  !*** ./src/config/environment.js ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ENV: () => (/* binding */ ENV),\n/* harmony export */   ENVIRONMENT_CONFIGS: () => (/* binding */ ENVIRONMENT_CONFIGS),\n/* harmony export */   getCacheConfig: () => (/* binding */ getCacheConfig),\n/* harmony export */   getCorsConfig: () => (/* binding */ getCorsConfig),\n/* harmony export */   getCurrentEnvironmentConfig: () => (/* binding */ getCurrentEnvironmentConfig),\n/* harmony export */   getDatabaseConfig: () => (/* binding */ getDatabaseConfig),\n/* harmony export */   getLoggingConfig: () => (/* binding */ getLoggingConfig),\n/* harmony export */   getSecurityConfig: () => (/* binding */ getSecurityConfig),\n/* harmony export */   validateEnvironmentVariables: () => (/* binding */ validateEnvironmentVariables)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// إعدادات البيئة للمنصة التعليمية - zaki alkholy\n// ===============================\n// متغيرات البيئة - zaki alkholy\n// ===============================\nvar _process_env_ALLOWED_HOSTS, _process_env_CORS_ALLOWED_ORIGINS;\nconst ENV = {\n    NODE_ENV: \"development\" || 0,\n    API_URL: \"http://127.0.0.1:8000\" || 0,\n    FRONTEND_URL: process.env.NEXT_PUBLIC_FRONTEND_URL || 'http://localhost:3000',\n    // إعدادات قاعدة البيانات (للمرجع فقط - تُستخدم في الباك إند)\n    DATABASE_URL: process.env.DATABASE_URL,\n    // إعدادات التخزين السحابي\n    CLOUDINARY_CLOUD_NAME: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,\n    CLOUDINARY_API_KEY: process.env.CLOUDINARY_API_KEY,\n    CLOUDINARY_API_SECRET: process.env.CLOUDINARY_API_SECRET,\n    // إعدادات المصادقة\n    JWT_SECRET: process.env.JWT_SECRET,\n    JWT_REFRESH_SECRET: process.env.JWT_REFRESH_SECRET,\n    JWT_ACCESS_TOKEN_LIFETIME: process.env.JWT_ACCESS_TOKEN_LIFETIME || '15m',\n    JWT_REFRESH_TOKEN_LIFETIME: process.env.JWT_REFRESH_TOKEN_LIFETIME || '7d',\n    // إعدادات Google OAuth\n    GOOGLE_CLIENT_ID: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,\n    GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET,\n    // إعدادات البريد الإلكتروني\n    EMAIL_HOST: process.env.EMAIL_HOST,\n    EMAIL_PORT: process.env.EMAIL_PORT || 587,\n    EMAIL_HOST_USER: process.env.EMAIL_HOST_USER,\n    EMAIL_HOST_PASSWORD: process.env.EMAIL_HOST_PASSWORD,\n    EMAIL_USE_TLS: process.env.EMAIL_USE_TLS === 'true',\n    DEFAULT_FROM_EMAIL: process.env.DEFAULT_FROM_EMAIL,\n    // إعدادات المدفوعات\n    PAYMOB_API_KEY: process.env.PAYMOB_API_KEY,\n    PAYMOB_INTEGRATION_ID: process.env.PAYMOB_INTEGRATION_ID,\n    PAYMOB_HMAC_SECRET: process.env.PAYMOB_HMAC_SECRET,\n    // إعدادات الإشعارات الفورية\n    VAPID_PUBLIC_KEY: process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY,\n    VAPID_PRIVATE_KEY: process.env.VAPID_PRIVATE_KEY,\n    VAPID_SUBJECT: process.env.VAPID_SUBJECT,\n    // إعدادات Redis (للتخزين المؤقت)\n    REDIS_URL: process.env.REDIS_URL,\n    // إعدادات Celery (للمهام الخلفية)\n    CELERY_BROKER_URL: process.env.CELERY_BROKER_URL,\n    CELERY_RESULT_BACKEND: process.env.CELERY_RESULT_BACKEND,\n    // إعدادات التحليلات\n    GOOGLE_ANALYTICS_ID: process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID,\n    // إعدادات الأمان\n    SECRET_KEY: process.env.SECRET_KEY,\n    ALLOWED_HOSTS: ((_process_env_ALLOWED_HOSTS = process.env.ALLOWED_HOSTS) === null || _process_env_ALLOWED_HOSTS === void 0 ? void 0 : _process_env_ALLOWED_HOSTS.split(',')) || [\n        'localhost',\n        '127.0.0.1'\n    ],\n    CORS_ALLOWED_ORIGINS: ((_process_env_CORS_ALLOWED_ORIGINS = process.env.CORS_ALLOWED_ORIGINS) === null || _process_env_CORS_ALLOWED_ORIGINS === void 0 ? void 0 : _process_env_CORS_ALLOWED_ORIGINS.split(',')) || [\n        'http://localhost:3000'\n    ],\n    // إعدادات التطوير\n    DEBUG: process.env.DEBUG === 'true',\n    LOGGING_LEVEL: process.env.LOGGING_LEVEL || 'INFO'\n};\n// ===============================\n// إعدادات البيئة المختلفة - zaki alkholy\n// ===============================\nconst ENVIRONMENT_CONFIGS = {\n    development: {\n        API_TIMEOUT: 30000,\n        ENABLE_LOGGING: true,\n        ENABLE_MOCK_DATA: true,\n        CACHE_ENABLED: false,\n        COMPRESSION_ENABLED: false,\n        SSL_REQUIRED: false,\n        DEBUG_MODE: true,\n        HOT_RELOAD: true,\n        SOURCE_MAPS: true\n    },\n    staging: {\n        API_TIMEOUT: 20000,\n        ENABLE_LOGGING: true,\n        ENABLE_MOCK_DATA: false,\n        CACHE_ENABLED: true,\n        COMPRESSION_ENABLED: true,\n        SSL_REQUIRED: true,\n        DEBUG_MODE: false,\n        HOT_RELOAD: false,\n        SOURCE_MAPS: true\n    },\n    production: {\n        API_TIMEOUT: 15000,\n        ENABLE_LOGGING: false,\n        ENABLE_MOCK_DATA: false,\n        CACHE_ENABLED: true,\n        COMPRESSION_ENABLED: true,\n        SSL_REQUIRED: true,\n        DEBUG_MODE: false,\n        HOT_RELOAD: false,\n        SOURCE_MAPS: false\n    }\n};\n// ===============================\n// الحصول على إعدادات البيئة الحالية - zaki alkholy\n// ===============================\nconst getCurrentEnvironmentConfig = ()=>{\n    return ENVIRONMENT_CONFIGS[ENV.NODE_ENV] || ENVIRONMENT_CONFIGS.development;\n};\n// ===============================\n// التحقق من صحة متغيرات البيئة - zaki alkholy\n// ===============================\nconst validateEnvironmentVariables = ()=>{\n    const requiredVars = {\n        development: [\n            'NEXT_PUBLIC_API_URL'\n        ],\n        staging: [\n            'NEXT_PUBLIC_API_URL',\n            'JWT_SECRET',\n            'SECRET_KEY'\n        ],\n        production: [\n            'NEXT_PUBLIC_API_URL',\n            'JWT_SECRET',\n            'SECRET_KEY',\n            'DATABASE_URL',\n            'EMAIL_HOST_USER',\n            'EMAIL_HOST_PASSWORD'\n        ]\n    };\n    const currentEnvVars = requiredVars[ENV.NODE_ENV] || requiredVars.development;\n    const missingVars = [];\n    currentEnvVars.forEach((varName)=>{\n        if (!ENV[varName.replace('NEXT_PUBLIC_', '')]) {\n            missingVars.push(varName);\n        }\n    });\n    if (missingVars.length > 0) {\n        console.warn(\"Missing required environment variables for \".concat(ENV.NODE_ENV, \":\"), missingVars);\n        return false;\n    }\n    return true;\n};\n// ===============================\n// إعدادات الأمان حسب البيئة - zaki alkholy\n// ===============================\nconst getSecurityConfig = ()=>{\n    const baseConfig = {\n        httpOnly: true,\n        secure: ENV.NODE_ENV === 'production',\n        sameSite: 'strict',\n        maxAge: 7 * 24 * 60 * 60 * 1000\n    };\n    if (ENV.NODE_ENV === 'production') {\n        return {\n            ...baseConfig,\n            domain: new URL(ENV.FRONTEND_URL).hostname,\n            secure: true\n        };\n    }\n    return baseConfig;\n};\n// ===============================\n// إعدادات CORS حسب البيئة - zaki alkholy\n// ===============================\nconst getCorsConfig = ()=>{\n    if (ENV.NODE_ENV === 'development') {\n        return {\n            origin: true,\n            credentials: true\n        };\n    }\n    return {\n        origin: ENV.CORS_ALLOWED_ORIGINS,\n        credentials: true,\n        optionsSuccessStatus: 200\n    };\n};\n// ===============================\n// إعدادات قاعدة البيانات حسب البيئة - zaki alkholy\n// ===============================\nconst getDatabaseConfig = ()=>{\n    const baseConfig = {\n        ENGINE: 'django.db.backends.postgresql',\n        OPTIONS: {\n            'init_command': \"SET sql_mode='STRICT_TRANS_TABLES'\"\n        },\n        CONN_MAX_AGE: 600\n    };\n    if (ENV.NODE_ENV === 'production') {\n        return {\n            ...baseConfig,\n            CONN_MAX_AGE: 0,\n            OPTIONS: {\n                ...baseConfig.OPTIONS,\n                'sslmode': 'require'\n            }\n        };\n    }\n    return baseConfig;\n};\n// ===============================\n// إعدادات التخزين المؤقت حسب البيئة - zaki alkholy\n// ===============================\nconst getCacheConfig = ()=>{\n    if (ENV.NODE_ENV === 'development') {\n        return {\n            BACKEND: 'django.core.cache.backends.locmem.LocMemCache',\n            LOCATION: 'unique-snowflake'\n        };\n    }\n    return {\n        BACKEND: 'django_redis.cache.RedisCache',\n        LOCATION: ENV.REDIS_URL,\n        OPTIONS: {\n            CLIENT_CLASS: 'django_redis.client.DefaultClient'\n        }\n    };\n};\n// ===============================\n// إعدادات السجلات حسب البيئة - zaki alkholy\n// ===============================\nconst getLoggingConfig = ()=>{\n    const baseConfig = {\n        version: 1,\n        disable_existing_loggers: false,\n        formatters: {\n            verbose: {\n                format: '{levelname} {asctime} {module} {process:d} {thread:d} {message}',\n                style: '{'\n            },\n            simple: {\n                format: '{levelname} {message}',\n                style: '{'\n            }\n        }\n    };\n    if (ENV.NODE_ENV === 'production') {\n        return {\n            ...baseConfig,\n            handlers: {\n                file: {\n                    level: 'INFO',\n                    class: 'logging.FileHandler',\n                    filename: 'logs/django.log',\n                    formatter: 'verbose'\n                },\n                console: {\n                    level: 'ERROR',\n                    class: 'logging.StreamHandler',\n                    formatter: 'simple'\n                }\n            },\n            root: {\n                handlers: [\n                    'file',\n                    'console'\n                ]\n            }\n        };\n    }\n    return {\n        ...baseConfig,\n        handlers: {\n            console: {\n                level: 'DEBUG',\n                class: 'logging.StreamHandler',\n                formatter: 'verbose'\n            }\n        },\n        root: {\n            handlers: [\n                'console'\n            ]\n        }\n    };\n};\n// تشغيل التحقق من متغيرات البيئة عند تحميل الملف\nvalidateEnvironmentVariables();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/config/environment.js\n"));

/***/ })

});