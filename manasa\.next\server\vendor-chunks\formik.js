"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/formik";
exports.ids = ["vendor-chunks/formik"];
exports.modules = {

/***/ "(ssr)/./node_modules/formik/dist/formik.esm.js":
/*!************************************************!*\
  !*** ./node_modules/formik/dist/formik.esm.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorMessage: () => (/* binding */ ErrorMessage),\n/* harmony export */   FastField: () => (/* binding */ FastField),\n/* harmony export */   Field: () => (/* binding */ Field),\n/* harmony export */   FieldArray: () => (/* binding */ FieldArray),\n/* harmony export */   Form: () => (/* binding */ Form),\n/* harmony export */   Formik: () => (/* binding */ Formik),\n/* harmony export */   FormikConsumer: () => (/* binding */ FormikConsumer),\n/* harmony export */   FormikContext: () => (/* binding */ FormikContext),\n/* harmony export */   FormikProvider: () => (/* binding */ FormikProvider),\n/* harmony export */   connect: () => (/* binding */ connect),\n/* harmony export */   getActiveElement: () => (/* binding */ getActiveElement),\n/* harmony export */   getIn: () => (/* binding */ getIn),\n/* harmony export */   insert: () => (/* binding */ insert),\n/* harmony export */   isEmptyArray: () => (/* binding */ isEmptyArray),\n/* harmony export */   isEmptyChildren: () => (/* binding */ isEmptyChildren),\n/* harmony export */   isFunction: () => (/* binding */ isFunction),\n/* harmony export */   isInputEvent: () => (/* binding */ isInputEvent),\n/* harmony export */   isInteger: () => (/* binding */ isInteger),\n/* harmony export */   isNaN: () => (/* binding */ isNaN$1),\n/* harmony export */   isObject: () => (/* binding */ isObject),\n/* harmony export */   isPromise: () => (/* binding */ isPromise),\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   move: () => (/* binding */ move),\n/* harmony export */   prepareDataForValidation: () => (/* binding */ prepareDataForValidation),\n/* harmony export */   replace: () => (/* binding */ replace),\n/* harmony export */   setIn: () => (/* binding */ setIn),\n/* harmony export */   setNestedObjectValues: () => (/* binding */ setNestedObjectValues),\n/* harmony export */   swap: () => (/* binding */ swap),\n/* harmony export */   useField: () => (/* binding */ useField),\n/* harmony export */   useFormik: () => (/* binding */ useFormik),\n/* harmony export */   useFormikContext: () => (/* binding */ useFormikContext),\n/* harmony export */   validateYupSchema: () => (/* binding */ validateYupSchema),\n/* harmony export */   withFormik: () => (/* binding */ withFormik),\n/* harmony export */   yupToFormErrors: () => (/* binding */ yupToFormErrors)\n/* harmony export */ });\n/* harmony import */ var deepmerge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! deepmerge */ \"(ssr)/./node_modules/deepmerge/dist/es.js\");\n/* harmony import */ var lodash_es_isPlainObject__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lodash-es/isPlainObject */ \"(ssr)/./node_modules/lodash-es/isPlainObject.js\");\n/* harmony import */ var lodash_es_cloneDeep__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lodash-es/cloneDeep */ \"(ssr)/./node_modules/lodash-es/cloneDeep.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_fast_compare__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-fast-compare */ \"(ssr)/./node_modules/react-fast-compare/index.js\");\n/* harmony import */ var react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_fast_compare__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var tiny_warning__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! tiny-warning */ \"(ssr)/./node_modules/tiny-warning/dist/tiny-warning.esm.js\");\n/* harmony import */ var lodash_es_clone__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash-es/clone */ \"(ssr)/./node_modules/lodash-es/clone.js\");\n/* harmony import */ var lodash_es_toPath__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash-es/toPath */ \"(ssr)/./node_modules/lodash-es/toPath.js\");\n/* harmony import */ var hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hoist-non-react-statics */ \"(ssr)/./node_modules/hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js\");\n/* harmony import */ var hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n\n\n\n\n\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  subClass.__proto__ = superClass;\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nvar FormikContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nFormikContext.displayName = 'FormikContext';\nvar FormikProvider = FormikContext.Provider;\nvar FormikConsumer = FormikContext.Consumer;\nfunction useFormikContext() {\n  var formik = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(FormikContext);\n  !!!formik ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"Formik context is undefined, please verify you are calling useFormikContext() as child of a <Formik> component.\") : 0 : void 0;\n  return formik;\n}\n\n/** @private is the value an empty array? */\n\nvar isEmptyArray = function isEmptyArray(value) {\n  return Array.isArray(value) && value.length === 0;\n};\n/** @private is the given object a Function? */\n\nvar isFunction = function isFunction(obj) {\n  return typeof obj === 'function';\n};\n/** @private is the given object an Object? */\n\nvar isObject = function isObject(obj) {\n  return obj !== null && typeof obj === 'object';\n};\n/** @private is the given object an integer? */\n\nvar isInteger = function isInteger(obj) {\n  return String(Math.floor(Number(obj))) === obj;\n};\n/** @private is the given object a string? */\n\nvar isString = function isString(obj) {\n  return Object.prototype.toString.call(obj) === '[object String]';\n};\n/** @private is the given object a NaN? */\n// eslint-disable-next-line no-self-compare\n\nvar isNaN$1 = function isNaN(obj) {\n  return obj !== obj;\n};\n/** @private Does a React component have exactly 0 children? */\n\nvar isEmptyChildren = function isEmptyChildren(children) {\n  return react__WEBPACK_IMPORTED_MODULE_1__.Children.count(children) === 0;\n};\n/** @private is the given object/value a promise? */\n\nvar isPromise = function isPromise(value) {\n  return isObject(value) && isFunction(value.then);\n};\n/** @private is the given object/value a type of synthetic event? */\n\nvar isInputEvent = function isInputEvent(value) {\n  return value && isObject(value) && isObject(value.target);\n};\n/**\r\n * Same as document.activeElement but wraps in a try-catch block. In IE it is\r\n * not safe to call document.activeElement if there is nothing focused.\r\n *\r\n * The activeElement will be null only if the document or document body is not\r\n * yet defined.\r\n *\r\n * @param {?Document} doc Defaults to current document.\r\n * @return {Element | null}\r\n * @see https://github.com/facebook/fbjs/blob/master/packages/fbjs/src/core/dom/getActiveElement.js\r\n */\n\nfunction getActiveElement(doc) {\n  doc = doc || (typeof document !== 'undefined' ? document : undefined);\n\n  if (typeof doc === 'undefined') {\n    return null;\n  }\n\n  try {\n    return doc.activeElement || doc.body;\n  } catch (e) {\n    return doc.body;\n  }\n}\n/**\r\n * Deeply get a value from an object via its path.\r\n */\n\nfunction getIn(obj, key, def, p) {\n  if (p === void 0) {\n    p = 0;\n  }\n\n  var path = (0,lodash_es_toPath__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(key);\n\n  while (obj && p < path.length) {\n    obj = obj[path[p++]];\n  } // check if path is not in the end\n\n\n  if (p !== path.length && !obj) {\n    return def;\n  }\n\n  return obj === undefined ? def : obj;\n}\n/**\r\n * Deeply set a value from in object via it's path. If the value at `path`\r\n * has changed, return a shallow copy of obj with `value` set at `path`.\r\n * If `value` has not changed, return the original `obj`.\r\n *\r\n * Existing objects / arrays along `path` are also shallow copied. Sibling\r\n * objects along path retain the same internal js reference. Since new\r\n * objects / arrays are only created along `path`, we can test if anything\r\n * changed in a nested structure by comparing the object's reference in\r\n * the old and new object, similar to how russian doll cache invalidation\r\n * works.\r\n *\r\n * In earlier versions of this function, which used cloneDeep, there were\r\n * issues whereby settings a nested value would mutate the parent\r\n * instead of creating a new object. `clone` avoids that bug making a\r\n * shallow copy of the objects along the update path\r\n * so no object is mutated in place.\r\n *\r\n * Before changing this function, please read through the following\r\n * discussions.\r\n *\r\n * @see https://github.com/developit/linkstate\r\n * @see https://github.com/jaredpalmer/formik/pull/123\r\n */\n\nfunction setIn(obj, path, value) {\n  var res = (0,lodash_es_clone__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(obj); // this keeps inheritance when obj is a class\n\n  var resVal = res;\n  var i = 0;\n  var pathArray = (0,lodash_es_toPath__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(path);\n\n  for (; i < pathArray.length - 1; i++) {\n    var currentPath = pathArray[i];\n    var currentObj = getIn(obj, pathArray.slice(0, i + 1));\n\n    if (currentObj && (isObject(currentObj) || Array.isArray(currentObj))) {\n      resVal = resVal[currentPath] = (0,lodash_es_clone__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(currentObj);\n    } else {\n      var nextPath = pathArray[i + 1];\n      resVal = resVal[currentPath] = isInteger(nextPath) && Number(nextPath) >= 0 ? [] : {};\n    }\n  } // Return original object if new value is the same as current\n\n\n  if ((i === 0 ? obj : resVal)[pathArray[i]] === value) {\n    return obj;\n  }\n\n  if (value === undefined) {\n    delete resVal[pathArray[i]];\n  } else {\n    resVal[pathArray[i]] = value;\n  } // If the path array has a single element, the loop did not run.\n  // Deleting on `resVal` had no effect in this scenario, so we delete on the result instead.\n\n\n  if (i === 0 && value === undefined) {\n    delete res[pathArray[i]];\n  }\n\n  return res;\n}\n/**\r\n * Recursively a set the same value for all keys and arrays nested object, cloning\r\n * @param object\r\n * @param value\r\n * @param visited\r\n * @param response\r\n */\n\nfunction setNestedObjectValues(object, value, visited, response) {\n  if (visited === void 0) {\n    visited = new WeakMap();\n  }\n\n  if (response === void 0) {\n    response = {};\n  }\n\n  for (var _i = 0, _Object$keys = Object.keys(object); _i < _Object$keys.length; _i++) {\n    var k = _Object$keys[_i];\n    var val = object[k];\n\n    if (isObject(val)) {\n      if (!visited.get(val)) {\n        visited.set(val, true); // In order to keep array values consistent for both dot path  and\n        // bracket syntax, we need to check if this is an array so that\n        // this will output  { friends: [true] } and not { friends: { \"0\": true } }\n\n        response[k] = Array.isArray(val) ? [] : {};\n        setNestedObjectValues(val, value, visited, response[k]);\n      }\n    } else {\n      response[k] = value;\n    }\n  }\n\n  return response;\n}\n\nfunction formikReducer(state, msg) {\n  switch (msg.type) {\n    case 'SET_VALUES':\n      return _extends({}, state, {\n        values: msg.payload\n      });\n\n    case 'SET_TOUCHED':\n      return _extends({}, state, {\n        touched: msg.payload\n      });\n\n    case 'SET_ERRORS':\n      if (react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(state.errors, msg.payload)) {\n        return state;\n      }\n\n      return _extends({}, state, {\n        errors: msg.payload\n      });\n\n    case 'SET_STATUS':\n      return _extends({}, state, {\n        status: msg.payload\n      });\n\n    case 'SET_ISSUBMITTING':\n      return _extends({}, state, {\n        isSubmitting: msg.payload\n      });\n\n    case 'SET_ISVALIDATING':\n      return _extends({}, state, {\n        isValidating: msg.payload\n      });\n\n    case 'SET_FIELD_VALUE':\n      return _extends({}, state, {\n        values: setIn(state.values, msg.payload.field, msg.payload.value)\n      });\n\n    case 'SET_FIELD_TOUCHED':\n      return _extends({}, state, {\n        touched: setIn(state.touched, msg.payload.field, msg.payload.value)\n      });\n\n    case 'SET_FIELD_ERROR':\n      return _extends({}, state, {\n        errors: setIn(state.errors, msg.payload.field, msg.payload.value)\n      });\n\n    case 'RESET_FORM':\n      return _extends({}, state, msg.payload);\n\n    case 'SET_FORMIK_STATE':\n      return msg.payload(state);\n\n    case 'SUBMIT_ATTEMPT':\n      return _extends({}, state, {\n        touched: setNestedObjectValues(state.values, true),\n        isSubmitting: true,\n        submitCount: state.submitCount + 1\n      });\n\n    case 'SUBMIT_FAILURE':\n      return _extends({}, state, {\n        isSubmitting: false\n      });\n\n    case 'SUBMIT_SUCCESS':\n      return _extends({}, state, {\n        isSubmitting: false\n      });\n\n    default:\n      return state;\n  }\n} // Initial empty states // objects\n\n\nvar emptyErrors = {};\nvar emptyTouched = {};\nfunction useFormik(_ref) {\n  var _ref$validateOnChange = _ref.validateOnChange,\n      validateOnChange = _ref$validateOnChange === void 0 ? true : _ref$validateOnChange,\n      _ref$validateOnBlur = _ref.validateOnBlur,\n      validateOnBlur = _ref$validateOnBlur === void 0 ? true : _ref$validateOnBlur,\n      _ref$validateOnMount = _ref.validateOnMount,\n      validateOnMount = _ref$validateOnMount === void 0 ? false : _ref$validateOnMount,\n      isInitialValid = _ref.isInitialValid,\n      _ref$enableReinitiali = _ref.enableReinitialize,\n      enableReinitialize = _ref$enableReinitiali === void 0 ? false : _ref$enableReinitiali,\n      onSubmit = _ref.onSubmit,\n      rest = _objectWithoutPropertiesLoose(_ref, [\"validateOnChange\", \"validateOnBlur\", \"validateOnMount\", \"isInitialValid\", \"enableReinitialize\", \"onSubmit\"]);\n\n  var props = _extends({\n    validateOnChange: validateOnChange,\n    validateOnBlur: validateOnBlur,\n    validateOnMount: validateOnMount,\n    onSubmit: onSubmit\n  }, rest);\n\n  var initialValues = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(props.initialValues);\n  var initialErrors = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(props.initialErrors || emptyErrors);\n  var initialTouched = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(props.initialTouched || emptyTouched);\n  var initialStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(props.initialStatus);\n  var isMounted = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n  var fieldRegistry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n\n  if (true) {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n      !(typeof isInitialValid === 'undefined') ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'isInitialValid has been deprecated and will be removed in future versions of Formik. Please use initialErrors or validateOnMount instead.') : 0 : void 0; // eslint-disable-next-line\n    }, []);\n  }\n\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    isMounted.current = true;\n    return function () {\n      isMounted.current = false;\n    };\n  }, []);\n\n  var _React$useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0),\n      setIteration = _React$useState[1];\n\n  var stateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n    values: (0,lodash_es_cloneDeep__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(props.initialValues),\n    errors: (0,lodash_es_cloneDeep__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(props.initialErrors) || emptyErrors,\n    touched: (0,lodash_es_cloneDeep__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(props.initialTouched) || emptyTouched,\n    status: (0,lodash_es_cloneDeep__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(props.initialStatus),\n    isSubmitting: false,\n    isValidating: false,\n    submitCount: 0\n  });\n  var state = stateRef.current;\n  var dispatch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (action) {\n    var prev = stateRef.current;\n    stateRef.current = formikReducer(prev, action); // force rerender\n\n    if (prev !== stateRef.current) setIteration(function (x) {\n      return x + 1;\n    });\n  }, []);\n  var runValidateHandler = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (values, field) {\n    return new Promise(function (resolve, reject) {\n      var maybePromisedErrors = props.validate(values, field);\n\n      if (maybePromisedErrors == null) {\n        // use loose null check here on purpose\n        resolve(emptyErrors);\n      } else if (isPromise(maybePromisedErrors)) {\n        maybePromisedErrors.then(function (errors) {\n          resolve(errors || emptyErrors);\n        }, function (actualException) {\n          if (true) {\n            console.warn(\"Warning: An unhandled error was caught during validation in <Formik validate />\", actualException);\n          }\n\n          reject(actualException);\n        });\n      } else {\n        resolve(maybePromisedErrors);\n      }\n    });\n  }, [props.validate]);\n  /**\r\n   * Run validation against a Yup schema and optionally run a function if successful\r\n   */\n\n  var runValidationSchema = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (values, field) {\n    var validationSchema = props.validationSchema;\n    var schema = isFunction(validationSchema) ? validationSchema(field) : validationSchema;\n    var promise = field && schema.validateAt ? schema.validateAt(field, values) : validateYupSchema(values, schema);\n    return new Promise(function (resolve, reject) {\n      promise.then(function () {\n        resolve(emptyErrors);\n      }, function (err) {\n        // Yup will throw a validation error if validation fails. We catch those and\n        // resolve them into Formik errors. We can sniff if something is a Yup error\n        // by checking error.name.\n        // @see https://github.com/jquense/yup#validationerrorerrors-string--arraystring-value-any-path-string\n        if (err.name === 'ValidationError') {\n          resolve(yupToFormErrors(err));\n        } else {\n          // We throw any other errors\n          if (true) {\n            console.warn(\"Warning: An unhandled error was caught during validation in <Formik validationSchema />\", err);\n          }\n\n          reject(err);\n        }\n      });\n    });\n  }, [props.validationSchema]);\n  var runSingleFieldLevelValidation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (field, value) {\n    return new Promise(function (resolve) {\n      return resolve(fieldRegistry.current[field].validate(value));\n    });\n  }, []);\n  var runFieldLevelValidations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (values) {\n    var fieldKeysWithValidation = Object.keys(fieldRegistry.current).filter(function (f) {\n      return isFunction(fieldRegistry.current[f].validate);\n    }); // Construct an array with all of the field validation functions\n\n    var fieldValidations = fieldKeysWithValidation.length > 0 ? fieldKeysWithValidation.map(function (f) {\n      return runSingleFieldLevelValidation(f, getIn(values, f));\n    }) : [Promise.resolve('DO_NOT_DELETE_YOU_WILL_BE_FIRED')]; // use special case ;)\n\n    return Promise.all(fieldValidations).then(function (fieldErrorsList) {\n      return fieldErrorsList.reduce(function (prev, curr, index) {\n        if (curr === 'DO_NOT_DELETE_YOU_WILL_BE_FIRED') {\n          return prev;\n        }\n\n        if (curr) {\n          prev = setIn(prev, fieldKeysWithValidation[index], curr);\n        }\n\n        return prev;\n      }, {});\n    });\n  }, [runSingleFieldLevelValidation]); // Run all validations and return the result\n\n  var runAllValidations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (values) {\n    return Promise.all([runFieldLevelValidations(values), props.validationSchema ? runValidationSchema(values) : {}, props.validate ? runValidateHandler(values) : {}]).then(function (_ref2) {\n      var fieldErrors = _ref2[0],\n          schemaErrors = _ref2[1],\n          validateErrors = _ref2[2];\n      var combinedErrors = deepmerge__WEBPACK_IMPORTED_MODULE_0__[\"default\"].all([fieldErrors, schemaErrors, validateErrors], {\n        arrayMerge: arrayMerge\n      });\n      return combinedErrors;\n    });\n  }, [props.validate, props.validationSchema, runFieldLevelValidations, runValidateHandler, runValidationSchema]); // Run all validations methods and update state accordingly\n\n  var validateFormWithHighPriority = useEventCallback(function (values) {\n    if (values === void 0) {\n      values = state.values;\n    }\n\n    dispatch({\n      type: 'SET_ISVALIDATING',\n      payload: true\n    });\n    return runAllValidations(values).then(function (combinedErrors) {\n      if (!!isMounted.current) {\n        dispatch({\n          type: 'SET_ISVALIDATING',\n          payload: false\n        });\n        dispatch({\n          type: 'SET_ERRORS',\n          payload: combinedErrors\n        });\n      }\n\n      return combinedErrors;\n    });\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (validateOnMount && isMounted.current === true && react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(initialValues.current, props.initialValues)) {\n      validateFormWithHighPriority(initialValues.current);\n    }\n  }, [validateOnMount, validateFormWithHighPriority]);\n  var resetForm = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (nextState) {\n    var values = nextState && nextState.values ? nextState.values : initialValues.current;\n    var errors = nextState && nextState.errors ? nextState.errors : initialErrors.current ? initialErrors.current : props.initialErrors || {};\n    var touched = nextState && nextState.touched ? nextState.touched : initialTouched.current ? initialTouched.current : props.initialTouched || {};\n    var status = nextState && nextState.status ? nextState.status : initialStatus.current ? initialStatus.current : props.initialStatus;\n    initialValues.current = values;\n    initialErrors.current = errors;\n    initialTouched.current = touched;\n    initialStatus.current = status;\n\n    var dispatchFn = function dispatchFn() {\n      dispatch({\n        type: 'RESET_FORM',\n        payload: {\n          isSubmitting: !!nextState && !!nextState.isSubmitting,\n          errors: errors,\n          touched: touched,\n          status: status,\n          values: values,\n          isValidating: !!nextState && !!nextState.isValidating,\n          submitCount: !!nextState && !!nextState.submitCount && typeof nextState.submitCount === 'number' ? nextState.submitCount : 0\n        }\n      });\n    };\n\n    if (props.onReset) {\n      var maybePromisedOnReset = props.onReset(state.values, imperativeMethods);\n\n      if (isPromise(maybePromisedOnReset)) {\n        maybePromisedOnReset.then(dispatchFn);\n      } else {\n        dispatchFn();\n      }\n    } else {\n      dispatchFn();\n    }\n  }, [props.initialErrors, props.initialStatus, props.initialTouched, props.onReset]);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (isMounted.current === true && !react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(initialValues.current, props.initialValues)) {\n      if (enableReinitialize) {\n        initialValues.current = props.initialValues;\n        resetForm();\n\n        if (validateOnMount) {\n          validateFormWithHighPriority(initialValues.current);\n        }\n      }\n    }\n  }, [enableReinitialize, props.initialValues, resetForm, validateOnMount, validateFormWithHighPriority]);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (enableReinitialize && isMounted.current === true && !react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(initialErrors.current, props.initialErrors)) {\n      initialErrors.current = props.initialErrors || emptyErrors;\n      dispatch({\n        type: 'SET_ERRORS',\n        payload: props.initialErrors || emptyErrors\n      });\n    }\n  }, [enableReinitialize, props.initialErrors]);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (enableReinitialize && isMounted.current === true && !react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(initialTouched.current, props.initialTouched)) {\n      initialTouched.current = props.initialTouched || emptyTouched;\n      dispatch({\n        type: 'SET_TOUCHED',\n        payload: props.initialTouched || emptyTouched\n      });\n    }\n  }, [enableReinitialize, props.initialTouched]);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (enableReinitialize && isMounted.current === true && !react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(initialStatus.current, props.initialStatus)) {\n      initialStatus.current = props.initialStatus;\n      dispatch({\n        type: 'SET_STATUS',\n        payload: props.initialStatus\n      });\n    }\n  }, [enableReinitialize, props.initialStatus, props.initialTouched]);\n  var validateField = useEventCallback(function (name) {\n    // This will efficiently validate a single field by avoiding state\n    // changes if the validation function is synchronous. It's different from\n    // what is called when using validateForm.\n    if (fieldRegistry.current[name] && isFunction(fieldRegistry.current[name].validate)) {\n      var value = getIn(state.values, name);\n      var maybePromise = fieldRegistry.current[name].validate(value);\n\n      if (isPromise(maybePromise)) {\n        // Only flip isValidating if the function is async.\n        dispatch({\n          type: 'SET_ISVALIDATING',\n          payload: true\n        });\n        return maybePromise.then(function (x) {\n          return x;\n        }).then(function (error) {\n          dispatch({\n            type: 'SET_FIELD_ERROR',\n            payload: {\n              field: name,\n              value: error\n            }\n          });\n          dispatch({\n            type: 'SET_ISVALIDATING',\n            payload: false\n          });\n        });\n      } else {\n        dispatch({\n          type: 'SET_FIELD_ERROR',\n          payload: {\n            field: name,\n            value: maybePromise\n          }\n        });\n        return Promise.resolve(maybePromise);\n      }\n    } else if (props.validationSchema) {\n      dispatch({\n        type: 'SET_ISVALIDATING',\n        payload: true\n      });\n      return runValidationSchema(state.values, name).then(function (x) {\n        return x;\n      }).then(function (error) {\n        dispatch({\n          type: 'SET_FIELD_ERROR',\n          payload: {\n            field: name,\n            value: getIn(error, name)\n          }\n        });\n        dispatch({\n          type: 'SET_ISVALIDATING',\n          payload: false\n        });\n      });\n    }\n\n    return Promise.resolve();\n  });\n  var registerField = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (name, _ref3) {\n    var validate = _ref3.validate;\n    fieldRegistry.current[name] = {\n      validate: validate\n    };\n  }, []);\n  var unregisterField = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (name) {\n    delete fieldRegistry.current[name];\n  }, []);\n  var setTouched = useEventCallback(function (touched, shouldValidate) {\n    dispatch({\n      type: 'SET_TOUCHED',\n      payload: touched\n    });\n    var willValidate = shouldValidate === undefined ? validateOnBlur : shouldValidate;\n    return willValidate ? validateFormWithHighPriority(state.values) : Promise.resolve();\n  });\n  var setErrors = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (errors) {\n    dispatch({\n      type: 'SET_ERRORS',\n      payload: errors\n    });\n  }, []);\n  var setValues = useEventCallback(function (values, shouldValidate) {\n    var resolvedValues = isFunction(values) ? values(state.values) : values;\n    dispatch({\n      type: 'SET_VALUES',\n      payload: resolvedValues\n    });\n    var willValidate = shouldValidate === undefined ? validateOnChange : shouldValidate;\n    return willValidate ? validateFormWithHighPriority(resolvedValues) : Promise.resolve();\n  });\n  var setFieldError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (field, value) {\n    dispatch({\n      type: 'SET_FIELD_ERROR',\n      payload: {\n        field: field,\n        value: value\n      }\n    });\n  }, []);\n  var setFieldValue = useEventCallback(function (field, value, shouldValidate) {\n    dispatch({\n      type: 'SET_FIELD_VALUE',\n      payload: {\n        field: field,\n        value: value\n      }\n    });\n    var willValidate = shouldValidate === undefined ? validateOnChange : shouldValidate;\n    return willValidate ? validateFormWithHighPriority(setIn(state.values, field, value)) : Promise.resolve();\n  });\n  var executeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (eventOrTextValue, maybePath) {\n    // By default, assume that the first argument is a string. This allows us to use\n    // handleChange with React Native and React Native Web's onChangeText prop which\n    // provides just the value of the input.\n    var field = maybePath;\n    var val = eventOrTextValue;\n    var parsed; // If the first argument is not a string though, it has to be a synthetic React Event (or a fake one),\n    // so we handle like we would a normal HTML change event.\n\n    if (!isString(eventOrTextValue)) {\n      // If we can, persist the event\n      // @see https://reactjs.org/docs/events.html#event-pooling\n      if (eventOrTextValue.persist) {\n        eventOrTextValue.persist();\n      }\n\n      var target = eventOrTextValue.target ? eventOrTextValue.target : eventOrTextValue.currentTarget;\n      var type = target.type,\n          name = target.name,\n          id = target.id,\n          value = target.value,\n          checked = target.checked,\n          outerHTML = target.outerHTML,\n          options = target.options,\n          multiple = target.multiple;\n      field = maybePath ? maybePath : name ? name : id;\n\n      if (!field && \"development\" !== \"production\") {\n        warnAboutMissingIdentifier({\n          htmlContent: outerHTML,\n          documentationAnchorLink: 'handlechange-e-reactchangeeventany--void',\n          handlerName: 'handleChange'\n        });\n      }\n\n      val = /number|range/.test(type) ? (parsed = parseFloat(value), isNaN(parsed) ? '' : parsed) : /checkbox/.test(type) // checkboxes\n      ? getValueForCheckbox(getIn(state.values, field), checked, value) : options && multiple // <select multiple>\n      ? getSelectedValues(options) : value;\n    }\n\n    if (field) {\n      // Set form fields by name\n      setFieldValue(field, val);\n    }\n  }, [setFieldValue, state.values]);\n  var handleChange = useEventCallback(function (eventOrPath) {\n    if (isString(eventOrPath)) {\n      return function (event) {\n        return executeChange(event, eventOrPath);\n      };\n    } else {\n      executeChange(eventOrPath);\n    }\n  });\n  var setFieldTouched = useEventCallback(function (field, touched, shouldValidate) {\n    if (touched === void 0) {\n      touched = true;\n    }\n\n    dispatch({\n      type: 'SET_FIELD_TOUCHED',\n      payload: {\n        field: field,\n        value: touched\n      }\n    });\n    var willValidate = shouldValidate === undefined ? validateOnBlur : shouldValidate;\n    return willValidate ? validateFormWithHighPriority(state.values) : Promise.resolve();\n  });\n  var executeBlur = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (e, path) {\n    if (e.persist) {\n      e.persist();\n    }\n\n    var _e$target = e.target,\n        name = _e$target.name,\n        id = _e$target.id,\n        outerHTML = _e$target.outerHTML;\n    var field = path ? path : name ? name : id;\n\n    if (!field && \"development\" !== \"production\") {\n      warnAboutMissingIdentifier({\n        htmlContent: outerHTML,\n        documentationAnchorLink: 'handleblur-e-any--void',\n        handlerName: 'handleBlur'\n      });\n    }\n\n    setFieldTouched(field, true);\n  }, [setFieldTouched]);\n  var handleBlur = useEventCallback(function (eventOrString) {\n    if (isString(eventOrString)) {\n      return function (event) {\n        return executeBlur(event, eventOrString);\n      };\n    } else {\n      executeBlur(eventOrString);\n    }\n  });\n  var setFormikState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (stateOrCb) {\n    if (isFunction(stateOrCb)) {\n      dispatch({\n        type: 'SET_FORMIK_STATE',\n        payload: stateOrCb\n      });\n    } else {\n      dispatch({\n        type: 'SET_FORMIK_STATE',\n        payload: function payload() {\n          return stateOrCb;\n        }\n      });\n    }\n  }, []);\n  var setStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (status) {\n    dispatch({\n      type: 'SET_STATUS',\n      payload: status\n    });\n  }, []);\n  var setSubmitting = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (isSubmitting) {\n    dispatch({\n      type: 'SET_ISSUBMITTING',\n      payload: isSubmitting\n    });\n  }, []);\n  var submitForm = useEventCallback(function () {\n    dispatch({\n      type: 'SUBMIT_ATTEMPT'\n    });\n    return validateFormWithHighPriority().then(function (combinedErrors) {\n      // In case an error was thrown and passed to the resolved Promise,\n      // `combinedErrors` can be an instance of an Error. We need to check\n      // that and abort the submit.\n      // If we don't do that, calling `Object.keys(new Error())` yields an\n      // empty array, which causes the validation to pass and the form\n      // to be submitted.\n      var isInstanceOfError = combinedErrors instanceof Error;\n      var isActuallyValid = !isInstanceOfError && Object.keys(combinedErrors).length === 0;\n\n      if (isActuallyValid) {\n        // Proceed with submit...\n        //\n        // To respect sync submit fns, we can't simply wrap executeSubmit in a promise and\n        // _always_ dispatch SUBMIT_SUCCESS because isSubmitting would then always be false.\n        // This would be fine in simple cases, but make it impossible to disable submit\n        // buttons where people use callbacks or promises as side effects (which is basically\n        // all of v1 Formik code). Instead, recall that we are inside of a promise chain already,\n        //  so we can try/catch executeSubmit(), if it returns undefined, then just bail.\n        // If there are errors, throw em. Otherwise, wrap executeSubmit in a promise and handle\n        // cleanup of isSubmitting on behalf of the consumer.\n        var promiseOrUndefined;\n\n        try {\n          promiseOrUndefined = executeSubmit(); // Bail if it's sync, consumer is responsible for cleaning up\n          // via setSubmitting(false)\n\n          if (promiseOrUndefined === undefined) {\n            return;\n          }\n        } catch (error) {\n          throw error;\n        }\n\n        return Promise.resolve(promiseOrUndefined).then(function (result) {\n          if (!!isMounted.current) {\n            dispatch({\n              type: 'SUBMIT_SUCCESS'\n            });\n          }\n\n          return result;\n        })[\"catch\"](function (_errors) {\n          if (!!isMounted.current) {\n            dispatch({\n              type: 'SUBMIT_FAILURE'\n            }); // This is a legit error rejected by the onSubmit fn\n            // so we don't want to break the promise chain\n\n            throw _errors;\n          }\n        });\n      } else if (!!isMounted.current) {\n        // ^^^ Make sure Formik is still mounted before updating state\n        dispatch({\n          type: 'SUBMIT_FAILURE'\n        }); // throw combinedErrors;\n\n        if (isInstanceOfError) {\n          throw combinedErrors;\n        }\n      }\n\n      return;\n    });\n  });\n  var handleSubmit = useEventCallback(function (e) {\n    if (e && e.preventDefault && isFunction(e.preventDefault)) {\n      e.preventDefault();\n    }\n\n    if (e && e.stopPropagation && isFunction(e.stopPropagation)) {\n      e.stopPropagation();\n    } // Warn if form submission is triggered by a <button> without a\n    // specified `type` attribute during development. This mitigates\n    // a common gotcha in forms with both reset and submit buttons,\n    // where the dev forgets to add type=\"button\" to the reset button.\n\n\n    if ( true && typeof document !== 'undefined') {\n      // Safely get the active element (works with IE)\n      var activeElement = getActiveElement();\n\n      if (activeElement !== null && activeElement instanceof HTMLButtonElement) {\n        !(activeElement.attributes && activeElement.attributes.getNamedItem('type')) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You submitted a Formik form using a button with an unspecified `type` attribute.  Most browsers default button elements to `type=\"submit\"`. If this is not a submit button, please add `type=\"button\"`.') : 0 : void 0;\n      }\n    }\n\n    submitForm()[\"catch\"](function (reason) {\n      console.warn(\"Warning: An unhandled error was caught from submitForm()\", reason);\n    });\n  });\n  var imperativeMethods = {\n    resetForm: resetForm,\n    validateForm: validateFormWithHighPriority,\n    validateField: validateField,\n    setErrors: setErrors,\n    setFieldError: setFieldError,\n    setFieldTouched: setFieldTouched,\n    setFieldValue: setFieldValue,\n    setStatus: setStatus,\n    setSubmitting: setSubmitting,\n    setTouched: setTouched,\n    setValues: setValues,\n    setFormikState: setFormikState,\n    submitForm: submitForm\n  };\n  var executeSubmit = useEventCallback(function () {\n    return onSubmit(state.values, imperativeMethods);\n  });\n  var handleReset = useEventCallback(function (e) {\n    if (e && e.preventDefault && isFunction(e.preventDefault)) {\n      e.preventDefault();\n    }\n\n    if (e && e.stopPropagation && isFunction(e.stopPropagation)) {\n      e.stopPropagation();\n    }\n\n    resetForm();\n  });\n  var getFieldMeta = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (name) {\n    return {\n      value: getIn(state.values, name),\n      error: getIn(state.errors, name),\n      touched: !!getIn(state.touched, name),\n      initialValue: getIn(initialValues.current, name),\n      initialTouched: !!getIn(initialTouched.current, name),\n      initialError: getIn(initialErrors.current, name)\n    };\n  }, [state.errors, state.touched, state.values]);\n  var getFieldHelpers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (name) {\n    return {\n      setValue: function setValue(value, shouldValidate) {\n        return setFieldValue(name, value, shouldValidate);\n      },\n      setTouched: function setTouched(value, shouldValidate) {\n        return setFieldTouched(name, value, shouldValidate);\n      },\n      setError: function setError(value) {\n        return setFieldError(name, value);\n      }\n    };\n  }, [setFieldValue, setFieldTouched, setFieldError]);\n  var getFieldProps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (nameOrOptions) {\n    var isAnObject = isObject(nameOrOptions);\n    var name = isAnObject ? nameOrOptions.name : nameOrOptions;\n    var valueState = getIn(state.values, name);\n    var field = {\n      name: name,\n      value: valueState,\n      onChange: handleChange,\n      onBlur: handleBlur\n    };\n\n    if (isAnObject) {\n      var type = nameOrOptions.type,\n          valueProp = nameOrOptions.value,\n          is = nameOrOptions.as,\n          multiple = nameOrOptions.multiple;\n\n      if (type === 'checkbox') {\n        if (valueProp === undefined) {\n          field.checked = !!valueState;\n        } else {\n          field.checked = !!(Array.isArray(valueState) && ~valueState.indexOf(valueProp));\n          field.value = valueProp;\n        }\n      } else if (type === 'radio') {\n        field.checked = valueState === valueProp;\n        field.value = valueProp;\n      } else if (is === 'select' && multiple) {\n        field.value = field.value || [];\n        field.multiple = true;\n      }\n    }\n\n    return field;\n  }, [handleBlur, handleChange, state.values]);\n  var dirty = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {\n    return !react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(initialValues.current, state.values);\n  }, [initialValues.current, state.values]);\n  var isValid = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {\n    return typeof isInitialValid !== 'undefined' ? dirty ? state.errors && Object.keys(state.errors).length === 0 : isInitialValid !== false && isFunction(isInitialValid) ? isInitialValid(props) : isInitialValid : state.errors && Object.keys(state.errors).length === 0;\n  }, [isInitialValid, dirty, state.errors, props]);\n\n  var ctx = _extends({}, state, {\n    initialValues: initialValues.current,\n    initialErrors: initialErrors.current,\n    initialTouched: initialTouched.current,\n    initialStatus: initialStatus.current,\n    handleBlur: handleBlur,\n    handleChange: handleChange,\n    handleReset: handleReset,\n    handleSubmit: handleSubmit,\n    resetForm: resetForm,\n    setErrors: setErrors,\n    setFormikState: setFormikState,\n    setFieldTouched: setFieldTouched,\n    setFieldValue: setFieldValue,\n    setFieldError: setFieldError,\n    setStatus: setStatus,\n    setSubmitting: setSubmitting,\n    setTouched: setTouched,\n    setValues: setValues,\n    submitForm: submitForm,\n    validateForm: validateFormWithHighPriority,\n    validateField: validateField,\n    isValid: isValid,\n    dirty: dirty,\n    unregisterField: unregisterField,\n    registerField: registerField,\n    getFieldProps: getFieldProps,\n    getFieldMeta: getFieldMeta,\n    getFieldHelpers: getFieldHelpers,\n    validateOnBlur: validateOnBlur,\n    validateOnChange: validateOnChange,\n    validateOnMount: validateOnMount\n  });\n\n  return ctx;\n}\nfunction Formik(props) {\n  var formikbag = useFormik(props);\n  var component = props.component,\n      children = props.children,\n      render = props.render,\n      innerRef = props.innerRef; // This allows folks to pass a ref to <Formik />\n\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(innerRef, function () {\n    return formikbag;\n  });\n\n  if (true) {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n      !!props.render ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"<Formik render> has been deprecated and will be removed in future versions of Formik. Please use a child callback function instead. To get rid of this warning, replace <Formik render={(props) => ...} /> with <Formik>{(props) => ...}</Formik>\") : 0 : void 0; // eslint-disable-next-line\n    }, []);\n  }\n\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(FormikProvider, {\n    value: formikbag\n  }, component ? (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, formikbag) : render ? render(formikbag) : children // children come last, always called\n  ? isFunction(children) ? children(formikbag) : !isEmptyChildren(children) ? react__WEBPACK_IMPORTED_MODULE_1__.Children.only(children) : null : null);\n}\n\nfunction warnAboutMissingIdentifier(_ref4) {\n  var htmlContent = _ref4.htmlContent,\n      documentationAnchorLink = _ref4.documentationAnchorLink,\n      handlerName = _ref4.handlerName;\n  console.warn(\"Warning: Formik called `\" + handlerName + \"`, but you forgot to pass an `id` or `name` attribute to your input:\\n    \" + htmlContent + \"\\n    Formik cannot determine which value to update. For more info see https://formik.org/docs/api/formik#\" + documentationAnchorLink + \"\\n  \");\n}\n/**\r\n * Transform Yup ValidationError to a more usable object\r\n */\n\n\nfunction yupToFormErrors(yupError) {\n  var errors = {};\n\n  if (yupError.inner) {\n    if (yupError.inner.length === 0) {\n      return setIn(errors, yupError.path, yupError.message);\n    }\n\n    for (var _iterator = yupError.inner, _isArray = Array.isArray(_iterator), _i = 0, _iterator = _isArray ? _iterator : _iterator[Symbol.iterator]();;) {\n      var _ref5;\n\n      if (_isArray) {\n        if (_i >= _iterator.length) break;\n        _ref5 = _iterator[_i++];\n      } else {\n        _i = _iterator.next();\n        if (_i.done) break;\n        _ref5 = _i.value;\n      }\n\n      var err = _ref5;\n\n      if (!getIn(errors, err.path)) {\n        errors = setIn(errors, err.path, err.message);\n      }\n    }\n  }\n\n  return errors;\n}\n/**\r\n * Validate a yup schema.\r\n */\n\nfunction validateYupSchema(values, schema, sync, context) {\n  if (sync === void 0) {\n    sync = false;\n  }\n\n  var normalizedValues = prepareDataForValidation(values);\n  return schema[sync ? 'validateSync' : 'validate'](normalizedValues, {\n    abortEarly: false,\n    context: context || normalizedValues\n  });\n}\n/**\r\n * Recursively prepare values.\r\n */\n\nfunction prepareDataForValidation(values) {\n  var data = Array.isArray(values) ? [] : {};\n\n  for (var k in values) {\n    if (Object.prototype.hasOwnProperty.call(values, k)) {\n      var key = String(k);\n\n      if (Array.isArray(values[key]) === true) {\n        data[key] = values[key].map(function (value) {\n          if (Array.isArray(value) === true || (0,lodash_es_isPlainObject__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(value)) {\n            return prepareDataForValidation(value);\n          } else {\n            return value !== '' ? value : undefined;\n          }\n        });\n      } else if ((0,lodash_es_isPlainObject__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(values[key])) {\n        data[key] = prepareDataForValidation(values[key]);\n      } else {\n        data[key] = values[key] !== '' ? values[key] : undefined;\n      }\n    }\n  }\n\n  return data;\n}\n/**\r\n * deepmerge array merging algorithm\r\n * https://github.com/KyleAMathews/deepmerge#combine-array\r\n */\n\nfunction arrayMerge(target, source, options) {\n  var destination = target.slice();\n  source.forEach(function merge(e, i) {\n    if (typeof destination[i] === 'undefined') {\n      var cloneRequested = options.clone !== false;\n      var shouldClone = cloneRequested && options.isMergeableObject(e);\n      destination[i] = shouldClone ? (0,deepmerge__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(Array.isArray(e) ? [] : {}, e, options) : e;\n    } else if (options.isMergeableObject(e)) {\n      destination[i] = (0,deepmerge__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(target[i], e, options);\n    } else if (target.indexOf(e) === -1) {\n      destination.push(e);\n    }\n  });\n  return destination;\n}\n/** Return multi select values based on an array of options */\n\n\nfunction getSelectedValues(options) {\n  return Array.from(options).filter(function (el) {\n    return el.selected;\n  }).map(function (el) {\n    return el.value;\n  });\n}\n/** Return the next value for a checkbox */\n\n\nfunction getValueForCheckbox(currentValue, checked, valueProp) {\n  // If the current value was a boolean, return a boolean\n  if (typeof currentValue === 'boolean') {\n    return Boolean(checked);\n  } // If the currentValue was not a boolean we want to return an array\n\n\n  var currentArrayOfValues = [];\n  var isValueInArray = false;\n  var index = -1;\n\n  if (!Array.isArray(currentValue)) {\n    // eslint-disable-next-line eqeqeq\n    if (!valueProp || valueProp == 'true' || valueProp == 'false') {\n      return Boolean(checked);\n    }\n  } else {\n    // If the current value is already an array, use it\n    currentArrayOfValues = currentValue;\n    index = currentValue.indexOf(valueProp);\n    isValueInArray = index >= 0;\n  } // If the checkbox was checked and the value is not already present in the aray we want to add the new value to the array of values\n\n\n  if (checked && valueProp && !isValueInArray) {\n    return currentArrayOfValues.concat(valueProp);\n  } // If the checkbox was unchecked and the value is not in the array, simply return the already existing array of values\n\n\n  if (!isValueInArray) {\n    return currentArrayOfValues;\n  } // If the checkbox was unchecked and the value is in the array, remove the value and return the array\n\n\n  return currentArrayOfValues.slice(0, index).concat(currentArrayOfValues.slice(index + 1));\n} // React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\n// @see https://gist.github.com/gaearon/e7d97cdf38a2907924ea12e4ebdf3c85\n\n\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined' ? react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_1__.useEffect;\n\nfunction useEventCallback(fn) {\n  var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(fn); // we copy a ref to the callback scoped to the current state/props on each render\n\n  useIsomorphicLayoutEffect(function () {\n    ref.current = fn;\n  });\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return ref.current.apply(void 0, args);\n  }, []);\n}\n\nfunction useField(propsOrFieldName) {\n  var formik = useFormikContext();\n  var getFieldProps = formik.getFieldProps,\n      getFieldMeta = formik.getFieldMeta,\n      getFieldHelpers = formik.getFieldHelpers,\n      registerField = formik.registerField,\n      unregisterField = formik.unregisterField;\n  var isAnObject = isObject(propsOrFieldName); // Normalize propsOrFieldName to FieldHookConfig<Val>\n\n  var props = isAnObject ? propsOrFieldName : {\n    name: propsOrFieldName\n  };\n  var fieldName = props.name,\n      validateFn = props.validate;\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (fieldName) {\n      registerField(fieldName, {\n        validate: validateFn\n      });\n    }\n\n    return function () {\n      if (fieldName) {\n        unregisterField(fieldName);\n      }\n    };\n  }, [registerField, unregisterField, fieldName, validateFn]);\n\n  if (true) {\n    !formik ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'useField() / <Field /> must be used underneath a <Formik> component or withFormik() higher order component') : 0 : void 0;\n  }\n\n  !fieldName ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'Invalid field name. Either pass `useField` a string or an object containing a `name` key.') : 0 : void 0;\n  var fieldHelpers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {\n    return getFieldHelpers(fieldName);\n  }, [getFieldHelpers, fieldName]);\n  return [getFieldProps(props), getFieldMeta(fieldName), fieldHelpers];\n}\nfunction Field(_ref) {\n  var validate = _ref.validate,\n      name = _ref.name,\n      render = _ref.render,\n      children = _ref.children,\n      is = _ref.as,\n      component = _ref.component,\n      className = _ref.className,\n      props = _objectWithoutPropertiesLoose(_ref, [\"validate\", \"name\", \"render\", \"children\", \"as\", \"component\", \"className\"]);\n\n  var _useFormikContext = useFormikContext(),\n      formik = _objectWithoutPropertiesLoose(_useFormikContext, [\"validate\", \"validationSchema\"]);\n\n  if (true) {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n      !!render ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"<Field render> has been deprecated and will be removed in future versions of Formik. Please use a child callback function instead. To get rid of this warning, replace <Field name=\\\"\" + name + \"\\\" render={({field, form}) => ...} /> with <Field name=\\\"\" + name + \"\\\">{({field, form, meta}) => ...}</Field>\") : 0 : void 0;\n      !!(is && children && isFunction(children)) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You should not use <Field as> and <Field children> as a function in the same <Field> component; <Field as> will be ignored.') : 0 : void 0;\n      !!(component && children && isFunction(children)) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You should not use <Field component> and <Field children> as a function in the same <Field> component; <Field component> will be ignored.') : 0 : void 0;\n      !!(render && children && !isEmptyChildren(children)) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You should not use <Field render> and <Field children> in the same <Field> component; <Field children> will be ignored') : 0 : void 0; // eslint-disable-next-line\n    }, []);\n  } // Register field and field-level validation with parent <Formik>\n\n\n  var registerField = formik.registerField,\n      unregisterField = formik.unregisterField;\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    registerField(name, {\n      validate: validate\n    });\n    return function () {\n      unregisterField(name);\n    };\n  }, [registerField, unregisterField, name, validate]);\n  var field = formik.getFieldProps(_extends({\n    name: name\n  }, props));\n  var meta = formik.getFieldMeta(name);\n  var legacyBag = {\n    field: field,\n    form: formik\n  };\n\n  if (render) {\n    return render(_extends({}, legacyBag, {\n      meta: meta\n    }));\n  }\n\n  if (isFunction(children)) {\n    return children(_extends({}, legacyBag, {\n      meta: meta\n    }));\n  }\n\n  if (component) {\n    // This behavior is backwards compat with earlier Formik 0.9 to 1.x\n    if (typeof component === 'string') {\n      var innerRef = props.innerRef,\n          rest = _objectWithoutPropertiesLoose(props, [\"innerRef\"]);\n\n      return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, _extends({\n        ref: innerRef\n      }, field, rest, {\n        className: className\n      }), children);\n    } // We don't pass `meta` for backwards compat\n\n\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, _extends({\n      field: field,\n      form: formik\n    }, props, {\n      className: className\n    }), children);\n  } // default to input here so we can check for both `as` and `children` above\n\n\n  var asElement = is || 'input';\n\n  if (typeof asElement === 'string') {\n    var _innerRef = props.innerRef,\n        _rest = _objectWithoutPropertiesLoose(props, [\"innerRef\"]);\n\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(asElement, _extends({\n      ref: _innerRef\n    }, field, _rest, {\n      className: className\n    }), children);\n  }\n\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(asElement, _extends({}, field, props, {\n    className: className\n  }), children);\n}\n\nvar Form = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(function (props, ref) {\n  // iOS needs an \"action\" attribute for nice input: https://stackoverflow.com/a/39485162/406725\n  // We default the action to \"#\" in case the preventDefault fails (just updates the URL hash)\n  var action = props.action,\n      rest = _objectWithoutPropertiesLoose(props, [\"action\"]);\n\n  var _action = action != null ? action : '#';\n\n  var _useFormikContext = useFormikContext(),\n      handleReset = _useFormikContext.handleReset,\n      handleSubmit = _useFormikContext.handleSubmit;\n\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"form\", _extends({\n    onSubmit: handleSubmit,\n    ref: ref,\n    onReset: handleReset,\n    action: _action\n  }, rest));\n});\nForm.displayName = 'Form';\n\n/**\r\n * A public higher-order component to access the imperative API\r\n */\n\nfunction withFormik(_ref) {\n  var _ref$mapPropsToValues = _ref.mapPropsToValues,\n      mapPropsToValues = _ref$mapPropsToValues === void 0 ? function (vanillaProps) {\n    var val = {};\n\n    for (var k in vanillaProps) {\n      if (vanillaProps.hasOwnProperty(k) && typeof vanillaProps[k] !== 'function') {\n        // @todo TypeScript fix\n        val[k] = vanillaProps[k];\n      }\n    }\n\n    return val;\n  } : _ref$mapPropsToValues,\n      config = _objectWithoutPropertiesLoose(_ref, [\"mapPropsToValues\"]);\n\n  return function createFormik(Component$1) {\n    var componentDisplayName = Component$1.displayName || Component$1.name || Component$1.constructor && Component$1.constructor.name || 'Component';\n    /**\r\n     * We need to use closures here for to provide the wrapped component's props to\r\n     * the respective withFormik config methods.\r\n     */\n\n    var C = /*#__PURE__*/function (_React$Component) {\n      _inheritsLoose(C, _React$Component);\n\n      function C() {\n        var _this;\n\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n\n        _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n\n        _this.validate = function (values) {\n          return config.validate(values, _this.props);\n        };\n\n        _this.validationSchema = function () {\n          return isFunction(config.validationSchema) ? config.validationSchema(_this.props) : config.validationSchema;\n        };\n\n        _this.handleSubmit = function (values, actions) {\n          return config.handleSubmit(values, _extends({}, actions, {\n            props: _this.props\n          }));\n        };\n\n        _this.renderFormComponent = function (formikProps) {\n          return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(Component$1, _extends({}, _this.props, formikProps));\n        };\n\n        return _this;\n      }\n\n      var _proto = C.prototype;\n\n      _proto.render = function render() {\n        var _this$props = this.props,\n            props = _objectWithoutPropertiesLoose(_this$props, [\"children\"]);\n\n        return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(Formik, _extends({}, props, config, {\n          validate: config.validate && this.validate,\n          validationSchema: config.validationSchema && this.validationSchema,\n          initialValues: mapPropsToValues(this.props),\n          initialStatus: config.mapPropsToStatus && config.mapPropsToStatus(this.props),\n          initialErrors: config.mapPropsToErrors && config.mapPropsToErrors(this.props),\n          initialTouched: config.mapPropsToTouched && config.mapPropsToTouched(this.props),\n          onSubmit: this.handleSubmit,\n          children: this.renderFormComponent\n        }));\n      };\n\n      return C;\n    }(react__WEBPACK_IMPORTED_MODULE_1__.Component);\n\n    C.displayName = \"WithFormik(\" + componentDisplayName + \")\";\n    return hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3___default()(C, Component$1 // cast type to ComponentClass (even if SFC)\n    );\n  };\n}\n\n/**\r\n * Connect any component to Formik context, and inject as a prop called `formik`;\r\n * @param Comp React Component\r\n */\n\nfunction connect(Comp) {\n  var C = function C(props) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(FormikConsumer, null, function (formik) {\n      !!!formik ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"Formik context is undefined, please verify you are rendering <Form>, <Field>, <FastField>, <FieldArray>, or your custom context-using component as a child of a <Formik> component. Component name: \" + Comp.name) : 0 : void 0;\n      return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(Comp, _extends({}, props, {\n        formik: formik\n      }));\n    });\n  };\n\n  var componentDisplayName = Comp.displayName || Comp.name || Comp.constructor && Comp.constructor.name || 'Component'; // Assign Comp to C.WrappedComponent so we can access the inner component in tests\n  // For example, <Field.WrappedComponent /> gets us <FieldInner/>\n\n  C.WrappedComponent = Comp;\n  C.displayName = \"FormikConnect(\" + componentDisplayName + \")\";\n  return hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3___default()(C, Comp // cast type to ComponentClass (even if SFC)\n  );\n}\n\n/**\r\n * Some array helpers!\r\n */\n\nvar move = function move(array, from, to) {\n  var copy = copyArrayLike(array);\n  var value = copy[from];\n  copy.splice(from, 1);\n  copy.splice(to, 0, value);\n  return copy;\n};\nvar swap = function swap(arrayLike, indexA, indexB) {\n  var copy = copyArrayLike(arrayLike);\n  var a = copy[indexA];\n  copy[indexA] = copy[indexB];\n  copy[indexB] = a;\n  return copy;\n};\nvar insert = function insert(arrayLike, index, value) {\n  var copy = copyArrayLike(arrayLike);\n  copy.splice(index, 0, value);\n  return copy;\n};\nvar replace = function replace(arrayLike, index, value) {\n  var copy = copyArrayLike(arrayLike);\n  copy[index] = value;\n  return copy;\n};\n\nvar copyArrayLike = function copyArrayLike(arrayLike) {\n  if (!arrayLike) {\n    return [];\n  } else if (Array.isArray(arrayLike)) {\n    return [].concat(arrayLike);\n  } else {\n    var maxIndex = Object.keys(arrayLike).map(function (key) {\n      return parseInt(key);\n    }).reduce(function (max, el) {\n      return el > max ? el : max;\n    }, 0);\n    return Array.from(_extends({}, arrayLike, {\n      length: maxIndex + 1\n    }));\n  }\n};\n\nvar createAlterationHandler = function createAlterationHandler(alteration, defaultFunction) {\n  var fn = typeof alteration === 'function' ? alteration : defaultFunction;\n  return function (data) {\n    if (Array.isArray(data) || isObject(data)) {\n      var clone = copyArrayLike(data);\n      return fn(clone);\n    } // This can be assumed to be a primitive, which\n    // is a case for top level validation errors\n\n\n    return data;\n  };\n};\n\nvar FieldArrayInner = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(FieldArrayInner, _React$Component);\n\n  function FieldArrayInner(props) {\n    var _this;\n\n    _this = _React$Component.call(this, props) || this; // We need TypeScript generics on these, so we'll bind them in the constructor\n    // @todo Fix TS 3.2.1\n\n    _this.updateArrayField = function (fn, alterTouched, alterErrors) {\n      var _this$props = _this.props,\n          name = _this$props.name,\n          setFormikState = _this$props.formik.setFormikState;\n      setFormikState(function (prevState) {\n        var updateErrors = createAlterationHandler(alterErrors, fn);\n        var updateTouched = createAlterationHandler(alterTouched, fn); // values fn should be executed before updateErrors and updateTouched,\n        // otherwise it causes an error with unshift.\n\n        var values = setIn(prevState.values, name, fn(getIn(prevState.values, name)));\n        var fieldError = alterErrors ? updateErrors(getIn(prevState.errors, name)) : undefined;\n        var fieldTouched = alterTouched ? updateTouched(getIn(prevState.touched, name)) : undefined;\n\n        if (isEmptyArray(fieldError)) {\n          fieldError = undefined;\n        }\n\n        if (isEmptyArray(fieldTouched)) {\n          fieldTouched = undefined;\n        }\n\n        return _extends({}, prevState, {\n          values: values,\n          errors: alterErrors ? setIn(prevState.errors, name, fieldError) : prevState.errors,\n          touched: alterTouched ? setIn(prevState.touched, name, fieldTouched) : prevState.touched\n        });\n      });\n    };\n\n    _this.push = function (value) {\n      return _this.updateArrayField(function (arrayLike) {\n        return [].concat(copyArrayLike(arrayLike), [(0,lodash_es_cloneDeep__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(value)]);\n      }, false, false);\n    };\n\n    _this.handlePush = function (value) {\n      return function () {\n        return _this.push(value);\n      };\n    };\n\n    _this.swap = function (indexA, indexB) {\n      return _this.updateArrayField(function (array) {\n        return swap(array, indexA, indexB);\n      }, true, true);\n    };\n\n    _this.handleSwap = function (indexA, indexB) {\n      return function () {\n        return _this.swap(indexA, indexB);\n      };\n    };\n\n    _this.move = function (from, to) {\n      return _this.updateArrayField(function (array) {\n        return move(array, from, to);\n      }, true, true);\n    };\n\n    _this.handleMove = function (from, to) {\n      return function () {\n        return _this.move(from, to);\n      };\n    };\n\n    _this.insert = function (index, value) {\n      return _this.updateArrayField(function (array) {\n        return insert(array, index, value);\n      }, function (array) {\n        return insert(array, index, null);\n      }, function (array) {\n        return insert(array, index, null);\n      });\n    };\n\n    _this.handleInsert = function (index, value) {\n      return function () {\n        return _this.insert(index, value);\n      };\n    };\n\n    _this.replace = function (index, value) {\n      return _this.updateArrayField(function (array) {\n        return replace(array, index, value);\n      }, false, false);\n    };\n\n    _this.handleReplace = function (index, value) {\n      return function () {\n        return _this.replace(index, value);\n      };\n    };\n\n    _this.unshift = function (value) {\n      var length = -1;\n\n      _this.updateArrayField(function (array) {\n        var arr = array ? [value].concat(array) : [value];\n        length = arr.length;\n        return arr;\n      }, function (array) {\n        return array ? [null].concat(array) : [null];\n      }, function (array) {\n        return array ? [null].concat(array) : [null];\n      });\n\n      return length;\n    };\n\n    _this.handleUnshift = function (value) {\n      return function () {\n        return _this.unshift(value);\n      };\n    };\n\n    _this.handleRemove = function (index) {\n      return function () {\n        return _this.remove(index);\n      };\n    };\n\n    _this.handlePop = function () {\n      return function () {\n        return _this.pop();\n      };\n    };\n\n    _this.remove = _this.remove.bind(_assertThisInitialized(_this));\n    _this.pop = _this.pop.bind(_assertThisInitialized(_this));\n    return _this;\n  }\n\n  var _proto = FieldArrayInner.prototype;\n\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\n    if (this.props.validateOnChange && this.props.formik.validateOnChange && !react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(getIn(prevProps.formik.values, prevProps.name), getIn(this.props.formik.values, this.props.name))) {\n      this.props.formik.validateForm(this.props.formik.values);\n    }\n  };\n\n  _proto.remove = function remove(index) {\n    // We need to make sure we also remove relevant pieces of `touched` and `errors`\n    var result;\n    this.updateArrayField( // so this gets call 3 times\n    function (array) {\n      var copy = array ? copyArrayLike(array) : [];\n\n      if (!result) {\n        result = copy[index];\n      }\n\n      if (isFunction(copy.splice)) {\n        copy.splice(index, 1);\n      } // if the array only includes undefined values we have to return an empty array\n\n\n      return isFunction(copy.every) ? copy.every(function (v) {\n        return v === undefined;\n      }) ? [] : copy : copy;\n    }, true, true);\n    return result;\n  };\n\n  _proto.pop = function pop() {\n    // Remove relevant pieces of `touched` and `errors` too!\n    var result;\n    this.updateArrayField( // so this gets call 3 times\n    function (array) {\n      var tmp = array.slice();\n\n      if (!result) {\n        result = tmp && tmp.pop && tmp.pop();\n      }\n\n      return tmp;\n    }, true, true);\n    return result;\n  };\n\n  _proto.render = function render() {\n    var arrayHelpers = {\n      push: this.push,\n      pop: this.pop,\n      swap: this.swap,\n      move: this.move,\n      insert: this.insert,\n      replace: this.replace,\n      unshift: this.unshift,\n      remove: this.remove,\n      handlePush: this.handlePush,\n      handlePop: this.handlePop,\n      handleSwap: this.handleSwap,\n      handleMove: this.handleMove,\n      handleInsert: this.handleInsert,\n      handleReplace: this.handleReplace,\n      handleUnshift: this.handleUnshift,\n      handleRemove: this.handleRemove\n    };\n\n    var _this$props2 = this.props,\n        component = _this$props2.component,\n        render = _this$props2.render,\n        children = _this$props2.children,\n        name = _this$props2.name,\n        _this$props2$formik = _this$props2.formik,\n        restOfFormik = _objectWithoutPropertiesLoose(_this$props2$formik, [\"validate\", \"validationSchema\"]);\n\n    var props = _extends({}, arrayHelpers, {\n      form: restOfFormik,\n      name: name\n    });\n\n    return component ? (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, props) : render ? render(props) : children // children come last, always called\n    ? typeof children === 'function' ? children(props) : !isEmptyChildren(children) ? react__WEBPACK_IMPORTED_MODULE_1__.Children.only(children) : null : null;\n  };\n\n  return FieldArrayInner;\n}(react__WEBPACK_IMPORTED_MODULE_1__.Component);\n\nFieldArrayInner.defaultProps = {\n  validateOnChange: true\n};\nvar FieldArray = /*#__PURE__*/connect(FieldArrayInner);\n\nvar ErrorMessageImpl = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(ErrorMessageImpl, _React$Component);\n\n  function ErrorMessageImpl() {\n    return _React$Component.apply(this, arguments) || this;\n  }\n\n  var _proto = ErrorMessageImpl.prototype;\n\n  _proto.shouldComponentUpdate = function shouldComponentUpdate(props) {\n    if (getIn(this.props.formik.errors, this.props.name) !== getIn(props.formik.errors, this.props.name) || getIn(this.props.formik.touched, this.props.name) !== getIn(props.formik.touched, this.props.name) || Object.keys(this.props).length !== Object.keys(props).length) {\n      return true;\n    } else {\n      return false;\n    }\n  };\n\n  _proto.render = function render() {\n    var _this$props = this.props,\n        component = _this$props.component,\n        formik = _this$props.formik,\n        render = _this$props.render,\n        children = _this$props.children,\n        name = _this$props.name,\n        rest = _objectWithoutPropertiesLoose(_this$props, [\"component\", \"formik\", \"render\", \"children\", \"name\"]);\n\n    var touch = getIn(formik.touched, name);\n    var error = getIn(formik.errors, name);\n    return !!touch && !!error ? render ? isFunction(render) ? render(error) : null : children ? isFunction(children) ? children(error) : null : component ? (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, rest, error) : error : null;\n  };\n\n  return ErrorMessageImpl;\n}(react__WEBPACK_IMPORTED_MODULE_1__.Component);\n\nvar ErrorMessage = /*#__PURE__*/connect(ErrorMessageImpl);\n\n/**\r\n * Custom Field component for quickly hooking into Formik\r\n * context and wiring up forms.\r\n */\n\nvar FastFieldInner = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(FastFieldInner, _React$Component);\n\n  function FastFieldInner(props) {\n    var _this;\n\n    _this = _React$Component.call(this, props) || this;\n    var render = props.render,\n        children = props.children,\n        component = props.component,\n        is = props.as,\n        name = props.name;\n    !!render ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"<FastField render> has been deprecated. Please use a child callback function instead: <FastField name={\" + name + \"}>{props => ...}</FastField> instead.\") : 0 : void 0;\n    !!(component && render) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You should not use <FastField component> and <FastField render> in the same <FastField> component; <FastField component> will be ignored') : 0 : void 0;\n    !!(is && children && isFunction(children)) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You should not use <FastField as> and <FastField children> as a function in the same <FastField> component; <FastField as> will be ignored.') : 0 : void 0;\n    !!(component && children && isFunction(children)) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You should not use <FastField component> and <FastField children> as a function in the same <FastField> component; <FastField component> will be ignored.') : 0 : void 0;\n    !!(render && children && !isEmptyChildren(children)) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You should not use <FastField render> and <FastField children> in the same <FastField> component; <FastField children> will be ignored') : 0 : void 0;\n    return _this;\n  }\n\n  var _proto = FastFieldInner.prototype;\n\n  _proto.shouldComponentUpdate = function shouldComponentUpdate(props) {\n    if (this.props.shouldUpdate) {\n      return this.props.shouldUpdate(props, this.props);\n    } else if (props.name !== this.props.name || getIn(props.formik.values, this.props.name) !== getIn(this.props.formik.values, this.props.name) || getIn(props.formik.errors, this.props.name) !== getIn(this.props.formik.errors, this.props.name) || getIn(props.formik.touched, this.props.name) !== getIn(this.props.formik.touched, this.props.name) || Object.keys(this.props).length !== Object.keys(props).length || props.formik.isSubmitting !== this.props.formik.isSubmitting) {\n      return true;\n    } else {\n      return false;\n    }\n  };\n\n  _proto.componentDidMount = function componentDidMount() {\n    // Register the Field with the parent Formik. Parent will cycle through\n    // registered Field's validate fns right prior to submit\n    this.props.formik.registerField(this.props.name, {\n      validate: this.props.validate\n    });\n  };\n\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\n    if (this.props.name !== prevProps.name) {\n      this.props.formik.unregisterField(prevProps.name);\n      this.props.formik.registerField(this.props.name, {\n        validate: this.props.validate\n      });\n    }\n\n    if (this.props.validate !== prevProps.validate) {\n      this.props.formik.registerField(this.props.name, {\n        validate: this.props.validate\n      });\n    }\n  };\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.props.formik.unregisterField(this.props.name);\n  };\n\n  _proto.render = function render() {\n    var _this$props = this.props,\n        name = _this$props.name,\n        render = _this$props.render,\n        is = _this$props.as,\n        children = _this$props.children,\n        component = _this$props.component,\n        formik = _this$props.formik,\n        props = _objectWithoutPropertiesLoose(_this$props, [\"validate\", \"name\", \"render\", \"as\", \"children\", \"component\", \"shouldUpdate\", \"formik\"]);\n\n    var restOfFormik = _objectWithoutPropertiesLoose(formik, [\"validate\", \"validationSchema\"]);\n\n    var field = formik.getFieldProps(_extends({\n      name: name\n    }, props));\n    var meta = {\n      value: getIn(formik.values, name),\n      error: getIn(formik.errors, name),\n      touched: !!getIn(formik.touched, name),\n      initialValue: getIn(formik.initialValues, name),\n      initialTouched: !!getIn(formik.initialTouched, name),\n      initialError: getIn(formik.initialErrors, name)\n    };\n    var bag = {\n      field: field,\n      meta: meta,\n      form: restOfFormik\n    };\n\n    if (render) {\n      return render(bag);\n    }\n\n    if (isFunction(children)) {\n      return children(bag);\n    }\n\n    if (component) {\n      // This behavior is backwards compat with earlier Formik 0.9 to 1.x\n      if (typeof component === 'string') {\n        var innerRef = props.innerRef,\n            rest = _objectWithoutPropertiesLoose(props, [\"innerRef\"]);\n\n        return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, _extends({\n          ref: innerRef\n        }, field, rest), children);\n      } // We don't pass `meta` for backwards compat\n\n\n      return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, _extends({\n        field: field,\n        form: formik\n      }, props), children);\n    } // default to input here so we can check for both `as` and `children` above\n\n\n    var asElement = is || 'input';\n\n    if (typeof asElement === 'string') {\n      var _innerRef = props.innerRef,\n          _rest = _objectWithoutPropertiesLoose(props, [\"innerRef\"]);\n\n      return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(asElement, _extends({\n        ref: _innerRef\n      }, field, _rest), children);\n    }\n\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(asElement, _extends({}, field, props), children);\n  };\n\n  return FastFieldInner;\n}(react__WEBPACK_IMPORTED_MODULE_1__.Component);\n\nvar FastField = /*#__PURE__*/connect(FastFieldInner);\n\n\n//# sourceMappingURL=formik.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/formik/dist/formik.esm.js\n");

/***/ })

};
;