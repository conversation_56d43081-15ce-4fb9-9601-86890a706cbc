"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-hook-form";
exports.ids = ["vendor-chunks/react-hook-form"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/react-hook-form/dist/index.esm.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Controller: () => (/* binding */ Controller),\n/* harmony export */   Form: () => (/* binding */ Form),\n/* harmony export */   FormProvider: () => (/* binding */ FormProvider),\n/* harmony export */   appendErrors: () => (/* binding */ appendErrors),\n/* harmony export */   createFormControl: () => (/* binding */ createFormControl),\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   useController: () => (/* binding */ useController),\n/* harmony export */   useFieldArray: () => (/* binding */ useFieldArray),\n/* harmony export */   useForm: () => (/* binding */ useForm),\n/* harmony export */   useFormContext: () => (/* binding */ useFormContext),\n/* harmony export */   useFormState: () => (/* binding */ useFormState),\n/* harmony export */   useWatch: () => (/* binding */ useWatch)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\nvar isCheckBoxInput = (element) => element.type === 'checkbox';\n\nvar isDateObject = (value) => value instanceof Date;\n\nvar isNullOrUndefined = (value) => value == null;\n\nconst isObjectType = (value) => typeof value === 'object';\nvar isObject = (value) => !isNullOrUndefined(value) &&\n    !Array.isArray(value) &&\n    isObjectType(value) &&\n    !isDateObject(value);\n\nvar getEventValue = (event) => isObject(event) && event.target\n    ? isCheckBoxInput(event.target)\n        ? event.target.checked\n        : event.target.value\n    : event;\n\nvar getNodeParentName = (name) => name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n\nvar isNameInFieldArray = (names, name) => names.has(getNodeParentName(name));\n\nvar isPlainObject = (tempObject) => {\n    const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n    return (isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf'));\n};\n\nvar isWeb = typeof window !== 'undefined' &&\n    typeof window.HTMLElement !== 'undefined' &&\n    typeof document !== 'undefined';\n\nfunction cloneObject(data) {\n    let copy;\n    const isArray = Array.isArray(data);\n    const isFileListInstance = typeof FileList !== 'undefined' ? data instanceof FileList : false;\n    if (data instanceof Date) {\n        copy = new Date(data);\n    }\n    else if (!(isWeb && (data instanceof Blob || isFileListInstance)) &&\n        (isArray || isObject(data))) {\n        copy = isArray ? [] : {};\n        if (!isArray && !isPlainObject(data)) {\n            copy = data;\n        }\n        else {\n            for (const key in data) {\n                if (data.hasOwnProperty(key)) {\n                    copy[key] = cloneObject(data[key]);\n                }\n            }\n        }\n    }\n    else {\n        return data;\n    }\n    return copy;\n}\n\nvar isKey = (value) => /^\\w*$/.test(value);\n\nvar isUndefined = (val) => val === undefined;\n\nvar compact = (value) => Array.isArray(value) ? value.filter(Boolean) : [];\n\nvar stringToPath = (input) => compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n\nvar get = (object, path, defaultValue) => {\n    if (!path || !isObject(object)) {\n        return defaultValue;\n    }\n    const result = (isKey(path) ? [path] : stringToPath(path)).reduce((result, key) => isNullOrUndefined(result) ? result : result[key], object);\n    return isUndefined(result) || result === object\n        ? isUndefined(object[path])\n            ? defaultValue\n            : object[path]\n        : result;\n};\n\nvar isBoolean = (value) => typeof value === 'boolean';\n\nvar set = (object, path, value) => {\n    let index = -1;\n    const tempPath = isKey(path) ? [path] : stringToPath(path);\n    const length = tempPath.length;\n    const lastIndex = length - 1;\n    while (++index < length) {\n        const key = tempPath[index];\n        let newValue = value;\n        if (index !== lastIndex) {\n            const objValue = object[key];\n            newValue =\n                isObject(objValue) || Array.isArray(objValue)\n                    ? objValue\n                    : !isNaN(+tempPath[index + 1])\n                        ? []\n                        : {};\n        }\n        if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n            return;\n        }\n        object[key] = newValue;\n        object = object[key];\n    }\n};\n\nconst EVENTS = {\n    BLUR: 'blur',\n    FOCUS_OUT: 'focusout',\n    CHANGE: 'change',\n};\nconst VALIDATION_MODE = {\n    onBlur: 'onBlur',\n    onChange: 'onChange',\n    onSubmit: 'onSubmit',\n    onTouched: 'onTouched',\n    all: 'all',\n};\nconst INPUT_VALIDATION_RULES = {\n    max: 'max',\n    min: 'min',\n    maxLength: 'maxLength',\n    minLength: 'minLength',\n    pattern: 'pattern',\n    required: 'required',\n    validate: 'validate',\n};\n\nconst HookFormContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nHookFormContext.displayName = 'HookFormContext';\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst useFormContext = () => react__WEBPACK_IMPORTED_MODULE_0__.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst FormProvider = (props) => {\n    const { children, ...data } = props;\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(HookFormContext.Provider, { value: data }, children));\n};\n\nvar getProxyFormState = (formState, control, localProxyFormState, isRoot = true) => {\n    const result = {\n        defaultValues: control._defaultValues,\n    };\n    for (const key in formState) {\n        Object.defineProperty(result, key, {\n            get: () => {\n                const _key = key;\n                if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n                    control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n                }\n                localProxyFormState && (localProxyFormState[_key] = true);\n                return formState[_key];\n            },\n        });\n    }\n    return result;\n};\n\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFormState(props) {\n    const methods = useFormContext();\n    const { control = methods.control, disabled, name, exact } = props || {};\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._formState);\n    const _localProxyFormState = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        isDirty: false,\n        isLoading: false,\n        dirtyFields: false,\n        touchedFields: false,\n        validatingFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false,\n    });\n    useIsomorphicLayoutEffect(() => control._subscribe({\n        name,\n        formState: _localProxyFormState.current,\n        exact,\n        callback: (formState) => {\n            !disabled &&\n                updateFormState({\n                    ...control._formState,\n                    ...formState,\n                });\n        },\n    }), [name, disabled, exact]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        _localProxyFormState.current.isValid && control._setValid(true);\n    }, [control]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => getProxyFormState(formState, control, _localProxyFormState.current, false), [formState, control]);\n}\n\nvar isString = (value) => typeof value === 'string';\n\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue) => {\n    if (isString(names)) {\n        isGlobal && _names.watch.add(names);\n        return get(formValues, names, defaultValue);\n    }\n    if (Array.isArray(names)) {\n        return names.map((fieldName) => (isGlobal && _names.watch.add(fieldName),\n            get(formValues, fieldName)));\n    }\n    isGlobal && (_names.watchAll = true);\n    return formValues;\n};\n\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nfunction useWatch(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, defaultValue, disabled, exact, } = props || {};\n    const _defaultValue = react__WEBPACK_IMPORTED_MODULE_0__.useRef(defaultValue);\n    const [value, updateValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getWatch(name, _defaultValue.current));\n    useIsomorphicLayoutEffect(() => control._subscribe({\n        name,\n        formState: {\n            values: true,\n        },\n        exact,\n        callback: (formState) => !disabled &&\n            updateValue(generateWatchOutput(name, control._names, formState.values || control._formValues, false, _defaultValue.current)),\n    }), [name, control, disabled, exact]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => control._removeUnmounted());\n    return value;\n}\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nfunction useController(props) {\n    const methods = useFormContext();\n    const { name, disabled, control = methods.control, shouldUnregister } = props;\n    const isArrayField = isNameInFieldArray(control._names.array, name);\n    const value = useWatch({\n        control,\n        name,\n        defaultValue: get(control._formValues, name, get(control._defaultValues, name, props.defaultValue)),\n        exact: true,\n    });\n    const formState = useFormState({\n        control,\n        name,\n        exact: true,\n    });\n    const _props = react__WEBPACK_IMPORTED_MODULE_0__.useRef(props);\n    const _registerProps = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control.register(name, {\n        ...props.rules,\n        value,\n        ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),\n    }));\n    const fieldState = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => Object.defineProperties({}, {\n        invalid: {\n            enumerable: true,\n            get: () => !!get(formState.errors, name),\n        },\n        isDirty: {\n            enumerable: true,\n            get: () => !!get(formState.dirtyFields, name),\n        },\n        isTouched: {\n            enumerable: true,\n            get: () => !!get(formState.touchedFields, name),\n        },\n        isValidating: {\n            enumerable: true,\n            get: () => !!get(formState.validatingFields, name),\n        },\n        error: {\n            enumerable: true,\n            get: () => get(formState.errors, name),\n        },\n    }), [formState, name]);\n    const onChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event) => _registerProps.current.onChange({\n        target: {\n            value: getEventValue(event),\n            name: name,\n        },\n        type: EVENTS.CHANGE,\n    }), [name]);\n    const onBlur = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => _registerProps.current.onBlur({\n        target: {\n            value: get(control._formValues, name),\n            name: name,\n        },\n        type: EVENTS.BLUR,\n    }), [name, control._formValues]);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((elm) => {\n        const field = get(control._fields, name);\n        if (field && elm) {\n            field._f.ref = {\n                focus: () => elm.focus && elm.focus(),\n                select: () => elm.select && elm.select(),\n                setCustomValidity: (message) => elm.setCustomValidity(message),\n                reportValidity: () => elm.reportValidity(),\n            };\n        }\n    }, [control._fields, name]);\n    const field = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n        name,\n        value,\n        ...(isBoolean(disabled) || formState.disabled\n            ? { disabled: formState.disabled || disabled }\n            : {}),\n        onChange,\n        onBlur,\n        ref,\n    }), [name, disabled, formState.disabled, onChange, onBlur, ref, value]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n        control.register(name, {\n            ..._props.current.rules,\n            ...(isBoolean(_props.current.disabled)\n                ? { disabled: _props.current.disabled }\n                : {}),\n        });\n        const updateMounted = (name, value) => {\n            const field = get(control._fields, name);\n            if (field && field._f) {\n                field._f.mount = value;\n            }\n        };\n        updateMounted(name, true);\n        if (_shouldUnregisterField) {\n            const value = cloneObject(get(control._options.defaultValues, name));\n            set(control._defaultValues, name, value);\n            if (isUndefined(get(control._formValues, name))) {\n                set(control._formValues, name, value);\n            }\n        }\n        !isArrayField && control.register(name);\n        return () => {\n            (isArrayField\n                ? _shouldUnregisterField && !control._state.action\n                : _shouldUnregisterField)\n                ? control.unregister(name)\n                : updateMounted(name, false);\n        };\n    }, [name, control, isArrayField, shouldUnregister]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        control._setDisabledField({\n            disabled,\n            name,\n        });\n    }, [disabled, name, control]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n        field,\n        formState,\n        fieldState,\n    }), [field, formState, fieldState]);\n}\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = (props) => props.render(useController(props));\n\nconst flatten = (obj) => {\n    const output = {};\n    for (const key of Object.keys(obj)) {\n        if (isObjectType(obj[key]) && obj[key] !== null) {\n            const nested = flatten(obj[key]);\n            for (const nestedKey of Object.keys(nested)) {\n                output[`${key}.${nestedKey}`] = nested[nestedKey];\n            }\n        }\n        else {\n            output[key] = obj[key];\n        }\n    }\n    return output;\n};\n\nconst POST_REQUEST = 'post';\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form(props) {\n    const methods = useFormContext();\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const { control = methods.control, onSubmit, children, action, method = POST_REQUEST, headers, encType, onError, render, onSuccess, validateStatus, ...rest } = props;\n    const submit = async (event) => {\n        let hasError = false;\n        let type = '';\n        await control.handleSubmit(async (data) => {\n            const formData = new FormData();\n            let formDataJson = '';\n            try {\n                formDataJson = JSON.stringify(data);\n            }\n            catch (_a) { }\n            const flattenFormValues = flatten(control._formValues);\n            for (const key in flattenFormValues) {\n                formData.append(key, flattenFormValues[key]);\n            }\n            if (onSubmit) {\n                await onSubmit({\n                    data,\n                    event,\n                    method,\n                    formData,\n                    formDataJson,\n                });\n            }\n            if (action) {\n                try {\n                    const shouldStringifySubmissionData = [\n                        headers && headers['Content-Type'],\n                        encType,\n                    ].some((value) => value && value.includes('json'));\n                    const response = await fetch(String(action), {\n                        method,\n                        headers: {\n                            ...headers,\n                            ...(encType ? { 'Content-Type': encType } : {}),\n                        },\n                        body: shouldStringifySubmissionData ? formDataJson : formData,\n                    });\n                    if (response &&\n                        (validateStatus\n                            ? !validateStatus(response.status)\n                            : response.status < 200 || response.status >= 300)) {\n                        hasError = true;\n                        onError && onError({ response });\n                        type = String(response.status);\n                    }\n                    else {\n                        onSuccess && onSuccess({ response });\n                    }\n                }\n                catch (error) {\n                    hasError = true;\n                    onError && onError({ error });\n                }\n            }\n        })(event);\n        if (hasError && props.control) {\n            props.control._subjects.state.next({\n                isSubmitSuccessful: false,\n            });\n            props.control.setError('root.server', {\n                type,\n            });\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        setMounted(true);\n    }, []);\n    return render ? (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render({\n        submit,\n    }))) : (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"form\", { noValidate: mounted, action: action, method: method, encType: encType, onSubmit: submit, ...rest }, children));\n}\n\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message) => validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n            ...(errors[name] && errors[name].types ? errors[name].types : {}),\n            [type]: message || true,\n        },\n    }\n    : {};\n\nvar convertToArrayPayload = (value) => (Array.isArray(value) ? value : [value]);\n\nvar createSubject = () => {\n    let _observers = [];\n    const next = (value) => {\n        for (const observer of _observers) {\n            observer.next && observer.next(value);\n        }\n    };\n    const subscribe = (observer) => {\n        _observers.push(observer);\n        return {\n            unsubscribe: () => {\n                _observers = _observers.filter((o) => o !== observer);\n            },\n        };\n    };\n    const unsubscribe = () => {\n        _observers = [];\n    };\n    return {\n        get observers() {\n            return _observers;\n        },\n        next,\n        subscribe,\n        unsubscribe,\n    };\n};\n\nvar isPrimitive = (value) => isNullOrUndefined(value) || !isObjectType(value);\n\nfunction deepEqual(object1, object2, _internal_visited = new WeakSet()) {\n    if (isPrimitive(object1) || isPrimitive(object2)) {\n        return object1 === object2;\n    }\n    if (isDateObject(object1) && isDateObject(object2)) {\n        return object1.getTime() === object2.getTime();\n    }\n    const keys1 = Object.keys(object1);\n    const keys2 = Object.keys(object2);\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    if (_internal_visited.has(object1) || _internal_visited.has(object2)) {\n        return true;\n    }\n    _internal_visited.add(object1);\n    _internal_visited.add(object2);\n    for (const key of keys1) {\n        const val1 = object1[key];\n        if (!keys2.includes(key)) {\n            return false;\n        }\n        if (key !== 'ref') {\n            const val2 = object2[key];\n            if ((isDateObject(val1) && isDateObject(val2)) ||\n                (isObject(val1) && isObject(val2)) ||\n                (Array.isArray(val1) && Array.isArray(val2))\n                ? !deepEqual(val1, val2, _internal_visited)\n                : val1 !== val2) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\n\nvar isEmptyObject = (value) => isObject(value) && !Object.keys(value).length;\n\nvar isFileInput = (element) => element.type === 'file';\n\nvar isFunction = (value) => typeof value === 'function';\n\nvar isHTMLElement = (value) => {\n    if (!isWeb) {\n        return false;\n    }\n    const owner = value ? value.ownerDocument : 0;\n    return (value instanceof\n        (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement));\n};\n\nvar isMultipleSelect = (element) => element.type === `select-multiple`;\n\nvar isRadioInput = (element) => element.type === 'radio';\n\nvar isRadioOrCheckbox = (ref) => isRadioInput(ref) || isCheckBoxInput(ref);\n\nvar live = (ref) => isHTMLElement(ref) && ref.isConnected;\n\nfunction baseGet(object, updatePath) {\n    const length = updatePath.slice(0, -1).length;\n    let index = 0;\n    while (index < length) {\n        object = isUndefined(object) ? index++ : object[updatePath[index++]];\n    }\n    return object;\n}\nfunction isEmptyArray(obj) {\n    for (const key in obj) {\n        if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction unset(object, path) {\n    const paths = Array.isArray(path)\n        ? path\n        : isKey(path)\n            ? [path]\n            : stringToPath(path);\n    const childObject = paths.length === 1 ? object : baseGet(object, paths);\n    const index = paths.length - 1;\n    const key = paths[index];\n    if (childObject) {\n        delete childObject[key];\n    }\n    if (index !== 0 &&\n        ((isObject(childObject) && isEmptyObject(childObject)) ||\n            (Array.isArray(childObject) && isEmptyArray(childObject)))) {\n        unset(object, paths.slice(0, -1));\n    }\n    return object;\n}\n\nvar objectHasFunction = (data) => {\n    for (const key in data) {\n        if (isFunction(data[key])) {\n            return true;\n        }\n    }\n    return false;\n};\n\nfunction markFieldsDirty(data, fields = {}) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for (const key in data) {\n            if (Array.isArray(data[key]) ||\n                (isObject(data[key]) && !objectHasFunction(data[key]))) {\n                fields[key] = Array.isArray(data[key]) ? [] : {};\n                markFieldsDirty(data[key], fields[key]);\n            }\n            else if (!isNullOrUndefined(data[key])) {\n                fields[key] = true;\n            }\n        }\n    }\n    return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for (const key in data) {\n            if (Array.isArray(data[key]) ||\n                (isObject(data[key]) && !objectHasFunction(data[key]))) {\n                if (isUndefined(formValues) ||\n                    isPrimitive(dirtyFieldsFromValues[key])) {\n                    dirtyFieldsFromValues[key] = Array.isArray(data[key])\n                        ? markFieldsDirty(data[key], [])\n                        : { ...markFieldsDirty(data[key]) };\n                }\n                else {\n                    getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n                }\n            }\n            else {\n                dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n            }\n        }\n    }\n    return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues) => getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\n\nconst defaultResult = {\n    value: false,\n    isValid: false,\n};\nconst validResult = { value: true, isValid: true };\nvar getCheckboxValue = (options) => {\n    if (Array.isArray(options)) {\n        if (options.length > 1) {\n            const values = options\n                .filter((option) => option && option.checked && !option.disabled)\n                .map((option) => option.value);\n            return { value: values, isValid: !!values.length };\n        }\n        return options[0].checked && !options[0].disabled\n            ? // @ts-expect-error expected to work in the browser\n                options[0].attributes && !isUndefined(options[0].attributes.value)\n                    ? isUndefined(options[0].value) || options[0].value === ''\n                        ? validResult\n                        : { value: options[0].value, isValid: true }\n                    : validResult\n            : defaultResult;\n    }\n    return defaultResult;\n};\n\nvar getFieldValueAs = (value, { valueAsNumber, valueAsDate, setValueAs }) => isUndefined(value)\n    ? value\n    : valueAsNumber\n        ? value === ''\n            ? NaN\n            : value\n                ? +value\n                : value\n        : valueAsDate && isString(value)\n            ? new Date(value)\n            : setValueAs\n                ? setValueAs(value)\n                : value;\n\nconst defaultReturn = {\n    isValid: false,\n    value: null,\n};\nvar getRadioValue = (options) => Array.isArray(options)\n    ? options.reduce((previous, option) => option && option.checked && !option.disabled\n        ? {\n            isValid: true,\n            value: option.value,\n        }\n        : previous, defaultReturn)\n    : defaultReturn;\n\nfunction getFieldValue(_f) {\n    const ref = _f.ref;\n    if (isFileInput(ref)) {\n        return ref.files;\n    }\n    if (isRadioInput(ref)) {\n        return getRadioValue(_f.refs).value;\n    }\n    if (isMultipleSelect(ref)) {\n        return [...ref.selectedOptions].map(({ value }) => value);\n    }\n    if (isCheckBoxInput(ref)) {\n        return getCheckboxValue(_f.refs).value;\n    }\n    return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation) => {\n    const fields = {};\n    for (const name of fieldsNames) {\n        const field = get(_fields, name);\n        field && set(fields, name, field._f);\n    }\n    return {\n        criteriaMode,\n        names: [...fieldsNames],\n        fields,\n        shouldUseNativeValidation,\n    };\n};\n\nvar isRegex = (value) => value instanceof RegExp;\n\nvar getRuleValue = (rule) => isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n        ? rule.source\n        : isObject(rule)\n            ? isRegex(rule.value)\n                ? rule.value.source\n                : rule.value\n            : rule;\n\nvar getValidationModes = (mode) => ({\n    isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n    isOnBlur: mode === VALIDATION_MODE.onBlur,\n    isOnChange: mode === VALIDATION_MODE.onChange,\n    isOnAll: mode === VALIDATION_MODE.all,\n    isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n\nconst ASYNC_FUNCTION = 'AsyncFunction';\nvar hasPromiseValidation = (fieldReference) => !!fieldReference &&\n    !!fieldReference.validate &&\n    !!((isFunction(fieldReference.validate) &&\n        fieldReference.validate.constructor.name === ASYNC_FUNCTION) ||\n        (isObject(fieldReference.validate) &&\n            Object.values(fieldReference.validate).find((validateFunction) => validateFunction.constructor.name === ASYNC_FUNCTION)));\n\nvar hasValidation = (options) => options.mount &&\n    (options.required ||\n        options.min ||\n        options.max ||\n        options.maxLength ||\n        options.minLength ||\n        options.pattern ||\n        options.validate);\n\nvar isWatched = (name, _names, isBlurEvent) => !isBlurEvent &&\n    (_names.watchAll ||\n        _names.watch.has(name) ||\n        [..._names.watch].some((watchName) => name.startsWith(watchName) &&\n            /^\\.\\w+/.test(name.slice(watchName.length))));\n\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly) => {\n    for (const key of fieldsNames || Object.keys(fields)) {\n        const field = get(fields, key);\n        if (field) {\n            const { _f, ...currentField } = field;\n            if (_f) {\n                if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n                    return true;\n                }\n                else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n                    return true;\n                }\n                else {\n                    if (iterateFieldsByAction(currentField, action)) {\n                        break;\n                    }\n                }\n            }\n            else if (isObject(currentField)) {\n                if (iterateFieldsByAction(currentField, action)) {\n                    break;\n                }\n            }\n        }\n    }\n    return;\n};\n\nfunction schemaErrorLookup(errors, _fields, name) {\n    const error = get(errors, name);\n    if (error || isKey(name)) {\n        return {\n            error,\n            name,\n        };\n    }\n    const names = name.split('.');\n    while (names.length) {\n        const fieldName = names.join('.');\n        const field = get(_fields, fieldName);\n        const foundError = get(errors, fieldName);\n        if (field && !Array.isArray(field) && name !== fieldName) {\n            return { name };\n        }\n        if (foundError && foundError.type) {\n            return {\n                name: fieldName,\n                error: foundError,\n            };\n        }\n        if (foundError && foundError.root && foundError.root.type) {\n            return {\n                name: `${fieldName}.root`,\n                error: foundError.root,\n            };\n        }\n        names.pop();\n    }\n    return {\n        name,\n    };\n}\n\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot) => {\n    updateFormState(formStateData);\n    const { name, ...formState } = formStateData;\n    return (isEmptyObject(formState) ||\n        Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n        Object.keys(formState).find((key) => _proxyFormState[key] ===\n            (!isRoot || VALIDATION_MODE.all)));\n};\n\nvar shouldSubscribeByName = (name, signalName, exact) => !name ||\n    !signalName ||\n    name === signalName ||\n    convertToArrayPayload(name).some((currentName) => currentName &&\n        (exact\n            ? currentName === signalName\n            : currentName.startsWith(signalName) ||\n                signalName.startsWith(currentName)));\n\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode) => {\n    if (mode.isOnAll) {\n        return false;\n    }\n    else if (!isSubmitted && mode.isOnTouch) {\n        return !(isTouched || isBlurEvent);\n    }\n    else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n        return !isBlurEvent;\n    }\n    else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n        return isBlurEvent;\n    }\n    return true;\n};\n\nvar unsetEmptyArray = (ref, name) => !compact(get(ref, name)).length && unset(ref, name);\n\nvar updateFieldArrayRootError = (errors, error, name) => {\n    const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n    set(fieldArrayErrors, 'root', error[name]);\n    set(errors, name, fieldArrayErrors);\n    return errors;\n};\n\nvar isMessage = (value) => isString(value);\n\nfunction getValidateError(result, ref, type = 'validate') {\n    if (isMessage(result) ||\n        (Array.isArray(result) && result.every(isMessage)) ||\n        (isBoolean(result) && !result)) {\n        return {\n            type,\n            message: isMessage(result) ? result : '',\n            ref,\n        };\n    }\n}\n\nvar getValueAndMessage = (validationData) => isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n    };\n\nvar validateField = async (field, disabledFieldNames, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray) => {\n    const { ref, refs, required, maxLength, minLength, min, max, pattern, validate, name, valueAsNumber, mount, } = field._f;\n    const inputValue = get(formValues, name);\n    if (!mount || disabledFieldNames.has(name)) {\n        return {};\n    }\n    const inputRef = refs ? refs[0] : ref;\n    const setCustomValidity = (message) => {\n        if (shouldUseNativeValidation && inputRef.reportValidity) {\n            inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n            inputRef.reportValidity();\n        }\n    };\n    const error = {};\n    const isRadio = isRadioInput(ref);\n    const isCheckBox = isCheckBoxInput(ref);\n    const isRadioOrCheckbox = isRadio || isCheckBox;\n    const isEmpty = ((valueAsNumber || isFileInput(ref)) &&\n        isUndefined(ref.value) &&\n        isUndefined(inputValue)) ||\n        (isHTMLElement(ref) && ref.value === '') ||\n        inputValue === '' ||\n        (Array.isArray(inputValue) && !inputValue.length);\n    const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n    const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength) => {\n        const message = exceedMax ? maxLengthMessage : minLengthMessage;\n        error[name] = {\n            type: exceedMax ? maxType : minType,\n            message,\n            ref,\n            ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n        };\n    };\n    if (isFieldArray\n        ? !Array.isArray(inputValue) || !inputValue.length\n        : required &&\n            ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n                (isBoolean(inputValue) && !inputValue) ||\n                (isCheckBox && !getCheckboxValue(refs).isValid) ||\n                (isRadio && !getRadioValue(refs).isValid))) {\n        const { value, message } = isMessage(required)\n            ? { value: !!required, message: required }\n            : getValueAndMessage(required);\n        if (value) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.required,\n                message,\n                ref: inputRef,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n        let exceedMax;\n        let exceedMin;\n        const maxOutput = getValueAndMessage(max);\n        const minOutput = getValueAndMessage(min);\n        if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n            const valueNumber = ref.valueAsNumber ||\n                (inputValue ? +inputValue : inputValue);\n            if (!isNullOrUndefined(maxOutput.value)) {\n                exceedMax = valueNumber > maxOutput.value;\n            }\n            if (!isNullOrUndefined(minOutput.value)) {\n                exceedMin = valueNumber < minOutput.value;\n            }\n        }\n        else {\n            const valueDate = ref.valueAsDate || new Date(inputValue);\n            const convertTimeToDate = (time) => new Date(new Date().toDateString() + ' ' + time);\n            const isTime = ref.type == 'time';\n            const isWeek = ref.type == 'week';\n            if (isString(maxOutput.value) && inputValue) {\n                exceedMax = isTime\n                    ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n                    : isWeek\n                        ? inputValue > maxOutput.value\n                        : valueDate > new Date(maxOutput.value);\n            }\n            if (isString(minOutput.value) && inputValue) {\n                exceedMin = isTime\n                    ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n                    : isWeek\n                        ? inputValue < minOutput.value\n                        : valueDate < new Date(minOutput.value);\n            }\n        }\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if ((maxLength || minLength) &&\n        !isEmpty &&\n        (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))) {\n        const maxLengthOutput = getValueAndMessage(maxLength);\n        const minLengthOutput = getValueAndMessage(minLength);\n        const exceedMax = !isNullOrUndefined(maxLengthOutput.value) &&\n            inputValue.length > +maxLengthOutput.value;\n        const exceedMin = !isNullOrUndefined(minLengthOutput.value) &&\n            inputValue.length < +minLengthOutput.value;\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if (pattern && !isEmpty && isString(inputValue)) {\n        const { value: patternValue, message } = getValueAndMessage(pattern);\n        if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.pattern,\n                message,\n                ref,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (validate) {\n        if (isFunction(validate)) {\n            const result = await validate(inputValue, formValues);\n            const validateError = getValidateError(result, inputRef);\n            if (validateError) {\n                error[name] = {\n                    ...validateError,\n                    ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message),\n                };\n                if (!validateAllFieldCriteria) {\n                    setCustomValidity(validateError.message);\n                    return error;\n                }\n            }\n        }\n        else if (isObject(validate)) {\n            let validationResult = {};\n            for (const key in validate) {\n                if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n                    break;\n                }\n                const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n                if (validateError) {\n                    validationResult = {\n                        ...validateError,\n                        ...appendErrorsCurry(key, validateError.message),\n                    };\n                    setCustomValidity(validateError.message);\n                    if (validateAllFieldCriteria) {\n                        error[name] = validationResult;\n                    }\n                }\n            }\n            if (!isEmptyObject(validationResult)) {\n                error[name] = {\n                    ref: inputRef,\n                    ...validationResult,\n                };\n                if (!validateAllFieldCriteria) {\n                    return error;\n                }\n            }\n        }\n    }\n    setCustomValidity(true);\n    return error;\n};\n\nconst defaultOptions = {\n    mode: VALIDATION_MODE.onSubmit,\n    reValidateMode: VALIDATION_MODE.onChange,\n    shouldFocusError: true,\n};\nfunction createFormControl(props = {}) {\n    let _options = {\n        ...defaultOptions,\n        ...props,\n    };\n    let _formState = {\n        submitCount: 0,\n        isDirty: false,\n        isReady: false,\n        isLoading: isFunction(_options.defaultValues),\n        isValidating: false,\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        touchedFields: {},\n        dirtyFields: {},\n        validatingFields: {},\n        errors: _options.errors || {},\n        disabled: _options.disabled || false,\n    };\n    let _fields = {};\n    let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values)\n        ? cloneObject(_options.defaultValues || _options.values) || {}\n        : {};\n    let _formValues = _options.shouldUnregister\n        ? {}\n        : cloneObject(_defaultValues);\n    let _state = {\n        action: false,\n        mount: false,\n        watch: false,\n    };\n    let _names = {\n        mount: new Set(),\n        disabled: new Set(),\n        unMount: new Set(),\n        array: new Set(),\n        watch: new Set(),\n    };\n    let delayErrorCallback;\n    let timer = 0;\n    const _proxyFormState = {\n        isDirty: false,\n        dirtyFields: false,\n        validatingFields: false,\n        touchedFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false,\n    };\n    let _proxySubscribeFormState = {\n        ..._proxyFormState,\n    };\n    const _subjects = {\n        array: createSubject(),\n        state: createSubject(),\n    };\n    const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n    const debounce = (callback) => (wait) => {\n        clearTimeout(timer);\n        timer = setTimeout(callback, wait);\n    };\n    const _setValid = async (shouldUpdateValid) => {\n        if (!_options.disabled &&\n            (_proxyFormState.isValid ||\n                _proxySubscribeFormState.isValid ||\n                shouldUpdateValid)) {\n            const isValid = _options.resolver\n                ? isEmptyObject((await _runSchema()).errors)\n                : await executeBuiltInValidation(_fields, true);\n            if (isValid !== _formState.isValid) {\n                _subjects.state.next({\n                    isValid,\n                });\n            }\n        }\n    };\n    const _updateIsValidating = (names, isValidating) => {\n        if (!_options.disabled &&\n            (_proxyFormState.isValidating ||\n                _proxyFormState.validatingFields ||\n                _proxySubscribeFormState.isValidating ||\n                _proxySubscribeFormState.validatingFields)) {\n            (names || Array.from(_names.mount)).forEach((name) => {\n                if (name) {\n                    isValidating\n                        ? set(_formState.validatingFields, name, isValidating)\n                        : unset(_formState.validatingFields, name);\n                }\n            });\n            _subjects.state.next({\n                validatingFields: _formState.validatingFields,\n                isValidating: !isEmptyObject(_formState.validatingFields),\n            });\n        }\n    };\n    const _setFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true) => {\n        if (args && method && !_options.disabled) {\n            _state.action = true;\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n                const fieldValues = method(get(_fields, name), args.argA, args.argB);\n                shouldSetValues && set(_fields, name, fieldValues);\n            }\n            if (shouldUpdateFieldsAndState &&\n                Array.isArray(get(_formState.errors, name))) {\n                const errors = method(get(_formState.errors, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.errors, name, errors);\n                unsetEmptyArray(_formState.errors, name);\n            }\n            if ((_proxyFormState.touchedFields ||\n                _proxySubscribeFormState.touchedFields) &&\n                shouldUpdateFieldsAndState &&\n                Array.isArray(get(_formState.touchedFields, name))) {\n                const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n            }\n            if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n                _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n            }\n            _subjects.state.next({\n                name,\n                isDirty: _getDirty(name, values),\n                dirtyFields: _formState.dirtyFields,\n                errors: _formState.errors,\n                isValid: _formState.isValid,\n            });\n        }\n        else {\n            set(_formValues, name, values);\n        }\n    };\n    const updateErrors = (name, error) => {\n        set(_formState.errors, name, error);\n        _subjects.state.next({\n            errors: _formState.errors,\n        });\n    };\n    const _setErrors = (errors) => {\n        _formState.errors = errors;\n        _subjects.state.next({\n            errors: _formState.errors,\n            isValid: false,\n        });\n    };\n    const updateValidAndValue = (name, shouldSkipSetValueAs, value, ref) => {\n        const field = get(_fields, name);\n        if (field) {\n            const defaultValue = get(_formValues, name, isUndefined(value) ? get(_defaultValues, name) : value);\n            isUndefined(defaultValue) ||\n                (ref && ref.defaultChecked) ||\n                shouldSkipSetValueAs\n                ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f))\n                : setFieldValue(name, defaultValue);\n            _state.mount && _setValid();\n        }\n    };\n    const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender) => {\n        let shouldUpdateField = false;\n        let isPreviousDirty = false;\n        const output = {\n            name,\n        };\n        if (!_options.disabled) {\n            if (!isBlurEvent || shouldDirty) {\n                if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n                    isPreviousDirty = _formState.isDirty;\n                    _formState.isDirty = output.isDirty = _getDirty();\n                    shouldUpdateField = isPreviousDirty !== output.isDirty;\n                }\n                const isCurrentFieldPristine = deepEqual(get(_defaultValues, name), fieldValue);\n                isPreviousDirty = !!get(_formState.dirtyFields, name);\n                isCurrentFieldPristine\n                    ? unset(_formState.dirtyFields, name)\n                    : set(_formState.dirtyFields, name, true);\n                output.dirtyFields = _formState.dirtyFields;\n                shouldUpdateField =\n                    shouldUpdateField ||\n                        ((_proxyFormState.dirtyFields ||\n                            _proxySubscribeFormState.dirtyFields) &&\n                            isPreviousDirty !== !isCurrentFieldPristine);\n            }\n            if (isBlurEvent) {\n                const isPreviousFieldTouched = get(_formState.touchedFields, name);\n                if (!isPreviousFieldTouched) {\n                    set(_formState.touchedFields, name, isBlurEvent);\n                    output.touchedFields = _formState.touchedFields;\n                    shouldUpdateField =\n                        shouldUpdateField ||\n                            ((_proxyFormState.touchedFields ||\n                                _proxySubscribeFormState.touchedFields) &&\n                                isPreviousFieldTouched !== isBlurEvent);\n                }\n            }\n            shouldUpdateField && shouldRender && _subjects.state.next(output);\n        }\n        return shouldUpdateField ? output : {};\n    };\n    const shouldRenderByError = (name, isValid, error, fieldState) => {\n        const previousFieldError = get(_formState.errors, name);\n        const shouldUpdateValid = (_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n            isBoolean(isValid) &&\n            _formState.isValid !== isValid;\n        if (_options.delayError && error) {\n            delayErrorCallback = debounce(() => updateErrors(name, error));\n            delayErrorCallback(_options.delayError);\n        }\n        else {\n            clearTimeout(timer);\n            delayErrorCallback = null;\n            error\n                ? set(_formState.errors, name, error)\n                : unset(_formState.errors, name);\n        }\n        if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n            !isEmptyObject(fieldState) ||\n            shouldUpdateValid) {\n            const updatedFormState = {\n                ...fieldState,\n                ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n                errors: _formState.errors,\n                name,\n            };\n            _formState = {\n                ..._formState,\n                ...updatedFormState,\n            };\n            _subjects.state.next(updatedFormState);\n        }\n    };\n    const _runSchema = async (name) => {\n        _updateIsValidating(name, true);\n        const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n        _updateIsValidating(name);\n        return result;\n    };\n    const executeSchemaAndUpdateState = async (names) => {\n        const { errors } = await _runSchema(names);\n        if (names) {\n            for (const name of names) {\n                const error = get(errors, name);\n                error\n                    ? set(_formState.errors, name, error)\n                    : unset(_formState.errors, name);\n            }\n        }\n        else {\n            _formState.errors = errors;\n        }\n        return errors;\n    };\n    const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {\n        valid: true,\n    }) => {\n        for (const name in fields) {\n            const field = fields[name];\n            if (field) {\n                const { _f, ...fieldValue } = field;\n                if (_f) {\n                    const isFieldArrayRoot = _names.array.has(_f.name);\n                    const isPromiseFunction = field._f && hasPromiseValidation(field._f);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([name], true);\n                    }\n                    const fieldError = await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([name]);\n                    }\n                    if (fieldError[_f.name]) {\n                        context.valid = false;\n                        if (shouldOnlyCheckValid) {\n                            break;\n                        }\n                    }\n                    !shouldOnlyCheckValid &&\n                        (get(fieldError, _f.name)\n                            ? isFieldArrayRoot\n                                ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name)\n                                : set(_formState.errors, _f.name, fieldError[_f.name])\n                            : unset(_formState.errors, _f.name));\n                }\n                !isEmptyObject(fieldValue) &&\n                    (await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context));\n            }\n        }\n        return context.valid;\n    };\n    const _removeUnmounted = () => {\n        for (const name of _names.unMount) {\n            const field = get(_fields, name);\n            field &&\n                (field._f.refs\n                    ? field._f.refs.every((ref) => !live(ref))\n                    : !live(field._f.ref)) &&\n                unregister(name);\n        }\n        _names.unMount = new Set();\n    };\n    const _getDirty = (name, data) => !_options.disabled &&\n        (name && data && set(_formValues, name, data),\n            !deepEqual(getValues(), _defaultValues));\n    const _getWatch = (names, defaultValue, isGlobal) => generateWatchOutput(names, _names, {\n        ...(_state.mount\n            ? _formValues\n            : isUndefined(defaultValue)\n                ? _defaultValues\n                : isString(names)\n                    ? { [names]: defaultValue }\n                    : defaultValue),\n    }, isGlobal, defaultValue);\n    const _getFieldArray = (name) => compact(get(_state.mount ? _formValues : _defaultValues, name, _options.shouldUnregister ? get(_defaultValues, name, []) : []));\n    const setFieldValue = (name, value, options = {}) => {\n        const field = get(_fields, name);\n        let fieldValue = value;\n        if (field) {\n            const fieldReference = field._f;\n            if (fieldReference) {\n                !fieldReference.disabled &&\n                    set(_formValues, name, getFieldValueAs(value, fieldReference));\n                fieldValue =\n                    isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n                        ? ''\n                        : value;\n                if (isMultipleSelect(fieldReference.ref)) {\n                    [...fieldReference.ref.options].forEach((optionRef) => (optionRef.selected = fieldValue.includes(optionRef.value)));\n                }\n                else if (fieldReference.refs) {\n                    if (isCheckBoxInput(fieldReference.ref)) {\n                        fieldReference.refs.forEach((checkboxRef) => {\n                            if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                                if (Array.isArray(fieldValue)) {\n                                    checkboxRef.checked = !!fieldValue.find((data) => data === checkboxRef.value);\n                                }\n                                else {\n                                    checkboxRef.checked =\n                                        fieldValue === checkboxRef.value || !!fieldValue;\n                                }\n                            }\n                        });\n                    }\n                    else {\n                        fieldReference.refs.forEach((radioRef) => (radioRef.checked = radioRef.value === fieldValue));\n                    }\n                }\n                else if (isFileInput(fieldReference.ref)) {\n                    fieldReference.ref.value = '';\n                }\n                else {\n                    fieldReference.ref.value = fieldValue;\n                    if (!fieldReference.ref.type) {\n                        _subjects.state.next({\n                            name,\n                            values: cloneObject(_formValues),\n                        });\n                    }\n                }\n            }\n        }\n        (options.shouldDirty || options.shouldTouch) &&\n            updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n        options.shouldValidate && trigger(name);\n    };\n    const setValues = (name, value, options) => {\n        for (const fieldKey in value) {\n            if (!value.hasOwnProperty(fieldKey)) {\n                return;\n            }\n            const fieldValue = value[fieldKey];\n            const fieldName = name + '.' + fieldKey;\n            const field = get(_fields, fieldName);\n            (_names.array.has(name) ||\n                isObject(fieldValue) ||\n                (field && !field._f)) &&\n                !isDateObject(fieldValue)\n                ? setValues(fieldName, fieldValue, options)\n                : setFieldValue(fieldName, fieldValue, options);\n        }\n    };\n    const setValue = (name, value, options = {}) => {\n        const field = get(_fields, name);\n        const isFieldArray = _names.array.has(name);\n        const cloneValue = cloneObject(value);\n        set(_formValues, name, cloneValue);\n        if (isFieldArray) {\n            _subjects.array.next({\n                name,\n                values: cloneObject(_formValues),\n            });\n            if ((_proxyFormState.isDirty ||\n                _proxyFormState.dirtyFields ||\n                _proxySubscribeFormState.isDirty ||\n                _proxySubscribeFormState.dirtyFields) &&\n                options.shouldDirty) {\n                _subjects.state.next({\n                    name,\n                    dirtyFields: getDirtyFields(_defaultValues, _formValues),\n                    isDirty: _getDirty(name, cloneValue),\n                });\n            }\n        }\n        else {\n            field && !field._f && !isNullOrUndefined(cloneValue)\n                ? setValues(name, cloneValue, options)\n                : setFieldValue(name, cloneValue, options);\n        }\n        isWatched(name, _names) && _subjects.state.next({ ..._formState });\n        _subjects.state.next({\n            name: _state.mount ? name : undefined,\n            values: cloneObject(_formValues),\n        });\n    };\n    const onChange = async (event) => {\n        _state.mount = true;\n        const target = event.target;\n        let name = target.name;\n        let isFieldValueUpdated = true;\n        const field = get(_fields, name);\n        const _updateIsFieldValueUpdated = (fieldValue) => {\n            isFieldValueUpdated =\n                Number.isNaN(fieldValue) ||\n                    (isDateObject(fieldValue) && isNaN(fieldValue.getTime())) ||\n                    deepEqual(fieldValue, get(_formValues, name, fieldValue));\n        };\n        const validationModeBeforeSubmit = getValidationModes(_options.mode);\n        const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n        if (field) {\n            let error;\n            let isValid;\n            const fieldValue = target.type\n                ? getFieldValue(field._f)\n                : getEventValue(event);\n            const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n            const shouldSkipValidation = (!hasValidation(field._f) &&\n                !_options.resolver &&\n                !get(_formState.errors, name) &&\n                !field._f.deps) ||\n                skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n            const watched = isWatched(name, _names, isBlurEvent);\n            set(_formValues, name, fieldValue);\n            if (isBlurEvent) {\n                field._f.onBlur && field._f.onBlur(event);\n                delayErrorCallback && delayErrorCallback(0);\n            }\n            else if (field._f.onChange) {\n                field._f.onChange(event);\n            }\n            const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n            const shouldRender = !isEmptyObject(fieldState) || watched;\n            !isBlurEvent &&\n                _subjects.state.next({\n                    name,\n                    type: event.type,\n                    values: cloneObject(_formValues),\n                });\n            if (shouldSkipValidation) {\n                if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n                    if (_options.mode === 'onBlur') {\n                        if (isBlurEvent) {\n                            _setValid();\n                        }\n                    }\n                    else if (!isBlurEvent) {\n                        _setValid();\n                    }\n                }\n                return (shouldRender &&\n                    _subjects.state.next({ name, ...(watched ? {} : fieldState) }));\n            }\n            !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n            if (_options.resolver) {\n                const { errors } = await _runSchema([name]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n                    const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n                    error = errorLookupResult.error;\n                    name = errorLookupResult.name;\n                    isValid = isEmptyObject(errors);\n                }\n            }\n            else {\n                _updateIsValidating([name], true);\n                error = (await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n                _updateIsValidating([name]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    if (error) {\n                        isValid = false;\n                    }\n                    else if (_proxyFormState.isValid ||\n                        _proxySubscribeFormState.isValid) {\n                        isValid = await executeBuiltInValidation(_fields, true);\n                    }\n                }\n            }\n            if (isFieldValueUpdated) {\n                field._f.deps &&\n                    trigger(field._f.deps);\n                shouldRenderByError(name, isValid, error, fieldState);\n            }\n        }\n    };\n    const _focusInput = (ref, key) => {\n        if (get(_formState.errors, key) && ref.focus) {\n            ref.focus();\n            return 1;\n        }\n        return;\n    };\n    const trigger = async (name, options = {}) => {\n        let isValid;\n        let validationResult;\n        const fieldNames = convertToArrayPayload(name);\n        if (_options.resolver) {\n            const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n            isValid = isEmptyObject(errors);\n            validationResult = name\n                ? !fieldNames.some((name) => get(errors, name))\n                : isValid;\n        }\n        else if (name) {\n            validationResult = (await Promise.all(fieldNames.map(async (fieldName) => {\n                const field = get(_fields, fieldName);\n                return await executeBuiltInValidation(field && field._f ? { [fieldName]: field } : field);\n            }))).every(Boolean);\n            !(!validationResult && !_formState.isValid) && _setValid();\n        }\n        else {\n            validationResult = isValid = await executeBuiltInValidation(_fields);\n        }\n        _subjects.state.next({\n            ...(!isString(name) ||\n                ((_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n                    isValid !== _formState.isValid)\n                ? {}\n                : { name }),\n            ...(_options.resolver || !name ? { isValid } : {}),\n            errors: _formState.errors,\n        });\n        options.shouldFocus &&\n            !validationResult &&\n            iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n        return validationResult;\n    };\n    const getValues = (fieldNames) => {\n        const values = {\n            ...(_state.mount ? _formValues : _defaultValues),\n        };\n        return isUndefined(fieldNames)\n            ? values\n            : isString(fieldNames)\n                ? get(values, fieldNames)\n                : fieldNames.map((name) => get(values, name));\n    };\n    const getFieldState = (name, formState) => ({\n        invalid: !!get((formState || _formState).errors, name),\n        isDirty: !!get((formState || _formState).dirtyFields, name),\n        error: get((formState || _formState).errors, name),\n        isValidating: !!get(_formState.validatingFields, name),\n        isTouched: !!get((formState || _formState).touchedFields, name),\n    });\n    const clearErrors = (name) => {\n        name &&\n            convertToArrayPayload(name).forEach((inputName) => unset(_formState.errors, inputName));\n        _subjects.state.next({\n            errors: name ? _formState.errors : {},\n        });\n    };\n    const setError = (name, error, options) => {\n        const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n        const currentError = get(_formState.errors, name) || {};\n        // Don't override existing error messages elsewhere in the object tree.\n        const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n        set(_formState.errors, name, {\n            ...restOfErrorTree,\n            ...error,\n            ref,\n        });\n        _subjects.state.next({\n            name,\n            errors: _formState.errors,\n            isValid: false,\n        });\n        options && options.shouldFocus && ref && ref.focus && ref.focus();\n    };\n    const watch = (name, defaultValue) => isFunction(name)\n        ? _subjects.state.subscribe({\n            next: (payload) => name(_getWatch(undefined, defaultValue), payload),\n        })\n        : _getWatch(name, defaultValue, true);\n    const _subscribe = (props) => _subjects.state.subscribe({\n        next: (formState) => {\n            if (shouldSubscribeByName(props.name, formState.name, props.exact) &&\n                shouldRenderFormState(formState, props.formState || _proxyFormState, _setFormState, props.reRenderRoot)) {\n                props.callback({\n                    values: { ..._formValues },\n                    ..._formState,\n                    ...formState,\n                });\n            }\n        },\n    }).unsubscribe;\n    const subscribe = (props) => {\n        _state.mount = true;\n        _proxySubscribeFormState = {\n            ..._proxySubscribeFormState,\n            ...props.formState,\n        };\n        return _subscribe({\n            ...props,\n            formState: _proxySubscribeFormState,\n        });\n    };\n    const unregister = (name, options = {}) => {\n        for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n            _names.mount.delete(fieldName);\n            _names.array.delete(fieldName);\n            if (!options.keepValue) {\n                unset(_fields, fieldName);\n                unset(_formValues, fieldName);\n            }\n            !options.keepError && unset(_formState.errors, fieldName);\n            !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n            !options.keepTouched && unset(_formState.touchedFields, fieldName);\n            !options.keepIsValidating &&\n                unset(_formState.validatingFields, fieldName);\n            !_options.shouldUnregister &&\n                !options.keepDefaultValue &&\n                unset(_defaultValues, fieldName);\n        }\n        _subjects.state.next({\n            values: cloneObject(_formValues),\n        });\n        _subjects.state.next({\n            ..._formState,\n            ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n        });\n        !options.keepIsValid && _setValid();\n    };\n    const _setDisabledField = ({ disabled, name, }) => {\n        if ((isBoolean(disabled) && _state.mount) ||\n            !!disabled ||\n            _names.disabled.has(name)) {\n            disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n        }\n    };\n    const register = (name, options = {}) => {\n        let field = get(_fields, name);\n        const disabledIsDefined = isBoolean(options.disabled) || isBoolean(_options.disabled);\n        set(_fields, name, {\n            ...(field || {}),\n            _f: {\n                ...(field && field._f ? field._f : { ref: { name } }),\n                name,\n                mount: true,\n                ...options,\n            },\n        });\n        _names.mount.add(name);\n        if (field) {\n            _setDisabledField({\n                disabled: isBoolean(options.disabled)\n                    ? options.disabled\n                    : _options.disabled,\n                name,\n            });\n        }\n        else {\n            updateValidAndValue(name, true, options.value);\n        }\n        return {\n            ...(disabledIsDefined\n                ? { disabled: options.disabled || _options.disabled }\n                : {}),\n            ...(_options.progressive\n                ? {\n                    required: !!options.required,\n                    min: getRuleValue(options.min),\n                    max: getRuleValue(options.max),\n                    minLength: getRuleValue(options.minLength),\n                    maxLength: getRuleValue(options.maxLength),\n                    pattern: getRuleValue(options.pattern),\n                }\n                : {}),\n            name,\n            onChange,\n            onBlur: onChange,\n            ref: (ref) => {\n                if (ref) {\n                    register(name, options);\n                    field = get(_fields, name);\n                    const fieldRef = isUndefined(ref.value)\n                        ? ref.querySelectorAll\n                            ? ref.querySelectorAll('input,select,textarea')[0] || ref\n                            : ref\n                        : ref;\n                    const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n                    const refs = field._f.refs || [];\n                    if (radioOrCheckbox\n                        ? refs.find((option) => option === fieldRef)\n                        : fieldRef === field._f.ref) {\n                        return;\n                    }\n                    set(_fields, name, {\n                        _f: {\n                            ...field._f,\n                            ...(radioOrCheckbox\n                                ? {\n                                    refs: [\n                                        ...refs.filter(live),\n                                        fieldRef,\n                                        ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                                    ],\n                                    ref: { type: fieldRef.type, name },\n                                }\n                                : { ref: fieldRef }),\n                        },\n                    });\n                    updateValidAndValue(name, false, undefined, fieldRef);\n                }\n                else {\n                    field = get(_fields, name, {});\n                    if (field._f) {\n                        field._f.mount = false;\n                    }\n                    (_options.shouldUnregister || options.shouldUnregister) &&\n                        !(isNameInFieldArray(_names.array, name) && _state.action) &&\n                        _names.unMount.add(name);\n                }\n            },\n        };\n    };\n    const _focusError = () => _options.shouldFocusError &&\n        iterateFieldsByAction(_fields, _focusInput, _names.mount);\n    const _disableForm = (disabled) => {\n        if (isBoolean(disabled)) {\n            _subjects.state.next({ disabled });\n            iterateFieldsByAction(_fields, (ref, name) => {\n                const currentField = get(_fields, name);\n                if (currentField) {\n                    ref.disabled = currentField._f.disabled || disabled;\n                    if (Array.isArray(currentField._f.refs)) {\n                        currentField._f.refs.forEach((inputRef) => {\n                            inputRef.disabled = currentField._f.disabled || disabled;\n                        });\n                    }\n                }\n            }, 0, false);\n        }\n    };\n    const handleSubmit = (onValid, onInvalid) => async (e) => {\n        let onValidError = undefined;\n        if (e) {\n            e.preventDefault && e.preventDefault();\n            e.persist &&\n                e.persist();\n        }\n        let fieldValues = cloneObject(_formValues);\n        _subjects.state.next({\n            isSubmitting: true,\n        });\n        if (_options.resolver) {\n            const { errors, values } = await _runSchema();\n            _formState.errors = errors;\n            fieldValues = cloneObject(values);\n        }\n        else {\n            await executeBuiltInValidation(_fields);\n        }\n        if (_names.disabled.size) {\n            for (const name of _names.disabled) {\n                unset(fieldValues, name);\n            }\n        }\n        unset(_formState.errors, 'root');\n        if (isEmptyObject(_formState.errors)) {\n            _subjects.state.next({\n                errors: {},\n            });\n            try {\n                await onValid(fieldValues, e);\n            }\n            catch (error) {\n                onValidError = error;\n            }\n        }\n        else {\n            if (onInvalid) {\n                await onInvalid({ ..._formState.errors }, e);\n            }\n            _focusError();\n            setTimeout(_focusError);\n        }\n        _subjects.state.next({\n            isSubmitted: true,\n            isSubmitting: false,\n            isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n            submitCount: _formState.submitCount + 1,\n            errors: _formState.errors,\n        });\n        if (onValidError) {\n            throw onValidError;\n        }\n    };\n    const resetField = (name, options = {}) => {\n        if (get(_fields, name)) {\n            if (isUndefined(options.defaultValue)) {\n                setValue(name, cloneObject(get(_defaultValues, name)));\n            }\n            else {\n                setValue(name, options.defaultValue);\n                set(_defaultValues, name, cloneObject(options.defaultValue));\n            }\n            if (!options.keepTouched) {\n                unset(_formState.touchedFields, name);\n            }\n            if (!options.keepDirty) {\n                unset(_formState.dirtyFields, name);\n                _formState.isDirty = options.defaultValue\n                    ? _getDirty(name, cloneObject(get(_defaultValues, name)))\n                    : _getDirty();\n            }\n            if (!options.keepError) {\n                unset(_formState.errors, name);\n                _proxyFormState.isValid && _setValid();\n            }\n            _subjects.state.next({ ..._formState });\n        }\n    };\n    const _reset = (formValues, keepStateOptions = {}) => {\n        const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n        const cloneUpdatedValues = cloneObject(updatedValues);\n        const isEmptyResetValues = isEmptyObject(formValues);\n        const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n        if (!keepStateOptions.keepDefaultValues) {\n            _defaultValues = updatedValues;\n        }\n        if (!keepStateOptions.keepValues) {\n            if (keepStateOptions.keepDirtyValues) {\n                const fieldsToCheck = new Set([\n                    ..._names.mount,\n                    ...Object.keys(getDirtyFields(_defaultValues, _formValues)),\n                ]);\n                for (const fieldName of Array.from(fieldsToCheck)) {\n                    get(_formState.dirtyFields, fieldName)\n                        ? set(values, fieldName, get(_formValues, fieldName))\n                        : setValue(fieldName, get(values, fieldName));\n                }\n            }\n            else {\n                if (isWeb && isUndefined(formValues)) {\n                    for (const name of _names.mount) {\n                        const field = get(_fields, name);\n                        if (field && field._f) {\n                            const fieldReference = Array.isArray(field._f.refs)\n                                ? field._f.refs[0]\n                                : field._f.ref;\n                            if (isHTMLElement(fieldReference)) {\n                                const form = fieldReference.closest('form');\n                                if (form) {\n                                    form.reset();\n                                    break;\n                                }\n                            }\n                        }\n                    }\n                }\n                if (keepStateOptions.keepFieldsRef) {\n                    for (const fieldName of _names.mount) {\n                        setValue(fieldName, get(values, fieldName));\n                    }\n                }\n                else {\n                    _fields = {};\n                }\n            }\n            _formValues = _options.shouldUnregister\n                ? keepStateOptions.keepDefaultValues\n                    ? cloneObject(_defaultValues)\n                    : {}\n                : cloneObject(values);\n            _subjects.array.next({\n                values: { ...values },\n            });\n            _subjects.state.next({\n                values: { ...values },\n            });\n        }\n        _names = {\n            mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n            unMount: new Set(),\n            array: new Set(),\n            disabled: new Set(),\n            watch: new Set(),\n            watchAll: false,\n            focus: '',\n        };\n        _state.mount =\n            !_proxyFormState.isValid ||\n                !!keepStateOptions.keepIsValid ||\n                !!keepStateOptions.keepDirtyValues;\n        _state.watch = !!_options.shouldUnregister;\n        _subjects.state.next({\n            submitCount: keepStateOptions.keepSubmitCount\n                ? _formState.submitCount\n                : 0,\n            isDirty: isEmptyResetValues\n                ? false\n                : keepStateOptions.keepDirty\n                    ? _formState.isDirty\n                    : !!(keepStateOptions.keepDefaultValues &&\n                        !deepEqual(formValues, _defaultValues)),\n            isSubmitted: keepStateOptions.keepIsSubmitted\n                ? _formState.isSubmitted\n                : false,\n            dirtyFields: isEmptyResetValues\n                ? {}\n                : keepStateOptions.keepDirtyValues\n                    ? keepStateOptions.keepDefaultValues && _formValues\n                        ? getDirtyFields(_defaultValues, _formValues)\n                        : _formState.dirtyFields\n                    : keepStateOptions.keepDefaultValues && formValues\n                        ? getDirtyFields(_defaultValues, formValues)\n                        : keepStateOptions.keepDirty\n                            ? _formState.dirtyFields\n                            : {},\n            touchedFields: keepStateOptions.keepTouched\n                ? _formState.touchedFields\n                : {},\n            errors: keepStateOptions.keepErrors ? _formState.errors : {},\n            isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful\n                ? _formState.isSubmitSuccessful\n                : false,\n            isSubmitting: false,\n        });\n    };\n    const reset = (formValues, keepStateOptions) => _reset(isFunction(formValues)\n        ? formValues(_formValues)\n        : formValues, keepStateOptions);\n    const setFocus = (name, options = {}) => {\n        const field = get(_fields, name);\n        const fieldReference = field && field._f;\n        if (fieldReference) {\n            const fieldRef = fieldReference.refs\n                ? fieldReference.refs[0]\n                : fieldReference.ref;\n            if (fieldRef.focus) {\n                fieldRef.focus();\n                options.shouldSelect &&\n                    isFunction(fieldRef.select) &&\n                    fieldRef.select();\n            }\n        }\n    };\n    const _setFormState = (updatedFormState) => {\n        _formState = {\n            ..._formState,\n            ...updatedFormState,\n        };\n    };\n    const _resetDefaultValues = () => isFunction(_options.defaultValues) &&\n        _options.defaultValues().then((values) => {\n            reset(values, _options.resetOptions);\n            _subjects.state.next({\n                isLoading: false,\n            });\n        });\n    const methods = {\n        control: {\n            register,\n            unregister,\n            getFieldState,\n            handleSubmit,\n            setError,\n            _subscribe,\n            _runSchema,\n            _focusError,\n            _getWatch,\n            _getDirty,\n            _setValid,\n            _setFieldArray,\n            _setDisabledField,\n            _setErrors,\n            _getFieldArray,\n            _reset,\n            _resetDefaultValues,\n            _removeUnmounted,\n            _disableForm,\n            _subjects,\n            _proxyFormState,\n            get _fields() {\n                return _fields;\n            },\n            get _formValues() {\n                return _formValues;\n            },\n            get _state() {\n                return _state;\n            },\n            set _state(value) {\n                _state = value;\n            },\n            get _defaultValues() {\n                return _defaultValues;\n            },\n            get _names() {\n                return _names;\n            },\n            set _names(value) {\n                _names = value;\n            },\n            get _formState() {\n                return _formState;\n            },\n            get _options() {\n                return _options;\n            },\n            set _options(value) {\n                _options = {\n                    ..._options,\n                    ...value,\n                };\n            },\n        },\n        subscribe,\n        trigger,\n        register,\n        handleSubmit,\n        watch,\n        setValue,\n        getValues,\n        reset,\n        resetField,\n        clearErrors,\n        unregister,\n        setError,\n        setFocus,\n        getFieldState,\n    };\n    return {\n        ...methods,\n        formControl: methods,\n    };\n}\n\nvar generateId = () => {\n    if (typeof crypto !== 'undefined' && crypto.randomUUID) {\n        return crypto.randomUUID();\n    }\n    const d = typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n        const r = (Math.random() * 16 + d) % 16 | 0;\n        return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n    });\n};\n\nvar getFocusFieldName = (name, index, options = {}) => options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n        `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n\nvar appendAt = (data, value) => [\n    ...data,\n    ...convertToArrayPayload(value),\n];\n\nvar fillEmptyArray = (value) => Array.isArray(value) ? value.map(() => undefined) : undefined;\n\nfunction insert(data, index, value) {\n    return [\n        ...data.slice(0, index),\n        ...convertToArrayPayload(value),\n        ...data.slice(index),\n    ];\n}\n\nvar moveArrayAt = (data, from, to) => {\n    if (!Array.isArray(data)) {\n        return [];\n    }\n    if (isUndefined(data[to])) {\n        data[to] = undefined;\n    }\n    data.splice(to, 0, data.splice(from, 1)[0]);\n    return data;\n};\n\nvar prependAt = (data, value) => [\n    ...convertToArrayPayload(value),\n    ...convertToArrayPayload(data),\n];\n\nfunction removeAtIndexes(data, indexes) {\n    let i = 0;\n    const temp = [...data];\n    for (const index of indexes) {\n        temp.splice(index - i, 1);\n        i++;\n    }\n    return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index) => isUndefined(index)\n    ? []\n    : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b) => a - b));\n\nvar swapArrayAt = (data, indexA, indexB) => {\n    [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\n\nvar updateAt = (fieldValues, index, value) => {\n    fieldValues[index] = value;\n    return fieldValues;\n};\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFieldArray(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, keyName = 'id', shouldUnregister, rules, } = props;\n    const [fields, setFields] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getFieldArray(name));\n    const ids = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control._getFieldArray(name).map(generateId));\n    const _fieldIds = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fields);\n    const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n    const _actioned = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    _name.current = name;\n    _fieldIds.current = fields;\n    control._names.array.add(name);\n    rules &&\n        control.register(name, rules);\n    useIsomorphicLayoutEffect(() => control._subjects.array.subscribe({\n        next: ({ values, name: fieldArrayName, }) => {\n            if (fieldArrayName === _name.current || !fieldArrayName) {\n                const fieldValues = get(values, _name.current);\n                if (Array.isArray(fieldValues)) {\n                    setFields(fieldValues);\n                    ids.current = fieldValues.map(generateId);\n                }\n            }\n        },\n    }).unsubscribe, [control]);\n    const updateValues = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((updatedFieldArrayValues) => {\n        _actioned.current = true;\n        control._setFieldArray(name, updatedFieldArrayValues);\n    }, [control, name]);\n    const append = (value, options) => {\n        const appendValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n        control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n        ids.current = appendAt(ids.current, appendValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n            argA: fillEmptyArray(value),\n        });\n    };\n    const prepend = (value, options) => {\n        const prependValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n        control._names.focus = getFocusFieldName(name, 0, options);\n        ids.current = prependAt(ids.current, prependValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n            argA: fillEmptyArray(value),\n        });\n    };\n    const remove = (index) => {\n        const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n        ids.current = removeArrayAt(ids.current, index);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        !Array.isArray(get(control._fields, name)) &&\n            set(control._fields, name, undefined);\n        control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n            argA: index,\n        });\n    };\n    const insert$1 = (index, value, options) => {\n        const insertValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n        control._names.focus = getFocusFieldName(name, index, options);\n        ids.current = insert(ids.current, index, insertValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, insert, {\n            argA: index,\n            argB: fillEmptyArray(value),\n        });\n    };\n    const swap = (indexA, indexB) => {\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n        swapArrayAt(ids.current, indexA, indexB);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n            argA: indexA,\n            argB: indexB,\n        }, false);\n    };\n    const move = (from, to) => {\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        moveArrayAt(updatedFieldArrayValues, from, to);\n        moveArrayAt(ids.current, from, to);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n            argA: from,\n            argB: to,\n        }, false);\n    };\n    const update = (index, value) => {\n        const updateValue = cloneObject(value);\n        const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n        ids.current = [...updatedFieldArrayValues].map((item, i) => !item || i === index ? generateId() : ids.current[i]);\n        updateValues(updatedFieldArrayValues);\n        setFields([...updatedFieldArrayValues]);\n        control._setFieldArray(name, updatedFieldArrayValues, updateAt, {\n            argA: index,\n            argB: updateValue,\n        }, true, false);\n    };\n    const replace = (value) => {\n        const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n        ids.current = updatedFieldArrayValues.map(generateId);\n        updateValues([...updatedFieldArrayValues]);\n        setFields([...updatedFieldArrayValues]);\n        control._setFieldArray(name, [...updatedFieldArrayValues], (data) => data, {}, true, false);\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        control._state.action = false;\n        isWatched(name, control._names) &&\n            control._subjects.state.next({\n                ...control._formState,\n            });\n        if (_actioned.current &&\n            (!getValidationModes(control._options.mode).isOnSubmit ||\n                control._formState.isSubmitted) &&\n            !getValidationModes(control._options.reValidateMode).isOnSubmit) {\n            if (control._options.resolver) {\n                control._runSchema([name]).then((result) => {\n                    const error = get(result.errors, name);\n                    const existingError = get(control._formState.errors, name);\n                    if (existingError\n                        ? (!error && existingError.type) ||\n                            (error &&\n                                (existingError.type !== error.type ||\n                                    existingError.message !== error.message))\n                        : error && error.type) {\n                        error\n                            ? set(control._formState.errors, name, error)\n                            : unset(control._formState.errors, name);\n                        control._subjects.state.next({\n                            errors: control._formState.errors,\n                        });\n                    }\n                });\n            }\n            else {\n                const field = get(control._fields, name);\n                if (field &&\n                    field._f &&\n                    !(getValidationModes(control._options.reValidateMode).isOnSubmit &&\n                        getValidationModes(control._options.mode).isOnSubmit)) {\n                    validateField(field, control._names.disabled, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then((error) => !isEmptyObject(error) &&\n                        control._subjects.state.next({\n                            errors: updateFieldArrayRootError(control._formState.errors, error, name),\n                        }));\n                }\n            }\n        }\n        control._subjects.state.next({\n            name,\n            values: cloneObject(control._formValues),\n        });\n        control._names.focus &&\n            iterateFieldsByAction(control._fields, (ref, key) => {\n                if (control._names.focus &&\n                    key.startsWith(control._names.focus) &&\n                    ref.focus) {\n                    ref.focus();\n                    return 1;\n                }\n                return;\n            });\n        control._names.focus = '';\n        control._setValid();\n        _actioned.current = false;\n    }, [fields, name, control]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        !get(control._formValues, name) && control._setFieldArray(name);\n        return () => {\n            const updateMounted = (name, value) => {\n                const field = get(control._fields, name);\n                if (field && field._f) {\n                    field._f.mount = value;\n                }\n            };\n            control._options.shouldUnregister || shouldUnregister\n                ? control.unregister(name)\n                : updateMounted(name, false);\n        };\n    }, [name, control, keyName, shouldUnregister]);\n    return {\n        swap: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(swap, [updateValues, name, control]),\n        move: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(move, [updateValues, name, control]),\n        prepend: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(prepend, [updateValues, name, control]),\n        append: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(append, [updateValues, name, control]),\n        remove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(remove, [updateValues, name, control]),\n        insert: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(insert$1, [updateValues, name, control]),\n        update: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(update, [updateValues, name, control]),\n        replace: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(replace, [updateValues, name, control]),\n        fields: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => fields.map((field, index) => ({\n            ...field,\n            [keyName]: ids.current[index] || generateId(),\n        })), [fields, keyName]),\n    };\n}\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useForm(props = {}) {\n    const _formControl = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const _values = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        isDirty: false,\n        isValidating: false,\n        isLoading: isFunction(props.defaultValues),\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        submitCount: 0,\n        dirtyFields: {},\n        touchedFields: {},\n        validatingFields: {},\n        errors: props.errors || {},\n        disabled: props.disabled || false,\n        isReady: false,\n        defaultValues: isFunction(props.defaultValues)\n            ? undefined\n            : props.defaultValues,\n    });\n    if (!_formControl.current) {\n        if (props.formControl) {\n            _formControl.current = {\n                ...props.formControl,\n                formState,\n            };\n            if (props.defaultValues && !isFunction(props.defaultValues)) {\n                props.formControl.reset(props.defaultValues, props.resetOptions);\n            }\n        }\n        else {\n            const { formControl, ...rest } = createFormControl(props);\n            _formControl.current = {\n                ...rest,\n                formState,\n            };\n        }\n    }\n    const control = _formControl.current.control;\n    control._options = props;\n    useIsomorphicLayoutEffect(() => {\n        const sub = control._subscribe({\n            formState: control._proxyFormState,\n            callback: () => updateFormState({ ...control._formState }),\n            reRenderRoot: true,\n        });\n        updateFormState((data) => ({\n            ...data,\n            isReady: true,\n        }));\n        control._formState.isReady = true;\n        return sub;\n    }, [control]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => control._disableForm(props.disabled), [control, props.disabled]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (props.mode) {\n            control._options.mode = props.mode;\n        }\n        if (props.reValidateMode) {\n            control._options.reValidateMode = props.reValidateMode;\n        }\n    }, [control, props.mode, props.reValidateMode]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (props.errors) {\n            control._setErrors(props.errors);\n            control._focusError();\n        }\n    }, [control, props.errors]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        props.shouldUnregister &&\n            control._subjects.state.next({\n                values: control._getWatch(),\n            });\n    }, [control, props.shouldUnregister]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (control._proxyFormState.isDirty) {\n            const isDirty = control._getDirty();\n            if (isDirty !== formState.isDirty) {\n                control._subjects.state.next({\n                    isDirty,\n                });\n            }\n        }\n    }, [control, formState.isDirty]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (props.values && !deepEqual(props.values, _values.current)) {\n            control._reset(props.values, {\n                keepFieldsRef: true,\n                ...control._options.resetOptions,\n            });\n            _values.current = props.values;\n            updateFormState((state) => ({ ...state }));\n        }\n        else {\n            control._resetDefaultValues();\n        }\n    }, [control, props.values]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (!control._state.mount) {\n            control._setValid();\n            control._state.mount = true;\n        }\n        if (control._state.watch) {\n            control._state.watch = false;\n            control._subjects.state.next({ ...control._formState });\n        }\n        control._removeUnmounted();\n    });\n    _formControl.current.formState = getProxyFormState(formState, control);\n    return _formControl.current;\n}\n\n\n//# sourceMappingURL=index.esm.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\n");

/***/ })

};
;