// ✅ ملف: src/app/_Components/pdf/QuizPDFGenerator.jsx
import jsPDF from "jspdf";
import "jspdf-autotable";
import "../../../fonts/Amiri-Regular-normal";

function toArabicNumber(number) {
  const arabicDigits = ["٠", "١", "٢", "٣", "٤", "٥", "٦", "٧", "٨", "٩"];
  return number.toString().replace(/\d/g, (d) => arabicDigits[d]);
}

const toBase64 = (url) =>
  fetch(url)
    .then((res) => res.blob())
    .then(
      (blob) =>
        new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onloadend = () => resolve(reader.result);
          reader.onerror = reject;
          reader.readAsDataURL(blob);
        })
    );

// دالة لحساب أبعاد الصورة مع الحفاظ على النسبة الأصلية
const calculateImageDimensions = (originalWidth, originalHeight, maxWidth, maxHeight) => {
  const aspectRatio = originalWidth / originalHeight;

  let newWidth = maxWidth;
  let newHeight = maxWidth / aspectRatio;

  // إذا كان الارتفاع أكبر من المسموح، نقلل العرض
  if (newHeight > maxHeight) {
    newHeight = maxHeight;
    newWidth = maxHeight * aspectRatio;
  }

  return { width: newWidth, height: newHeight };
};

// دالة لتحميل الصورة والحصول على أبعادها الأصلية
const loadImageWithDimensions = (url) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = "anonymous";
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      canvas.width = img.naturalWidth;
      canvas.height = img.naturalHeight;

      ctx.drawImage(img, 0, 0);

      const base64 = canvas.toDataURL('image/jpeg', 0.9); // جودة عالية

      resolve({
        base64,
        originalWidth: img.naturalWidth,
        originalHeight: img.naturalHeight
      });
    };
    img.onerror = reject;
    img.src = url;
  });
};

export async function generateQuizPDF(quiz, user) {
  // تحسين إعدادات PDF - zakialkholy
  const doc = new jsPDF({
    orientation: "portrait",
    unit: "mm",
    format: "a4",
    compress: true // ضغط الملف لتقليل الحجم
  });

  doc.setFont("Amiri-Regular", "normal");
  doc.setFontSize(14);

  const pageHeight = 297;
  const pageWidth = 210;
  const marginHorizontal = 15;
  const contentMarginTop = 35; // زيادة المساحة العلوية - zakialkholy
  const contentMarginBottom = 30; // زيادة المساحة السفلية - zakialkholy
  const labels = ["أ", "ب", "ج", "د", "هـ", "و"];
  let y = contentMarginTop;

  const estimateQuestionHeight = (q) => {
    let height = 10;

    // السؤال في سطر واحد
    height += 10;

    // الصورة تأخذ مساحة متغيرة حسب حجمها الأصلي (حد أقصى 85)
    if (q.image_url) height += 85;

    // الاختيارات تحت الصورة - حساب عدد الصفوف المطلوبة
    const answersPerRow = 2; // عدد الاختيارات في كل صف
    const answerRows = Math.ceil(q.answers.length / answersPerRow);
    height += answerRows * 10;

    return height + 15;
  };

  const renderHeader = () => {
    const topY = 15;

    // تحسين تصميم الهيدر - zakialkholy
    doc.setFontSize(16);
    doc.setFont("Amiri-Regular", "bold");
    doc.text(`${quiz.title}`, pageWidth / 2, topY, { align: "center" });

    doc.setFontSize(10);
    doc.setFont("Amiri-Regular", "normal");
    doc.text(`اسم المعلم: ${user.first_name || user.username}`, marginHorizontal + 35, topY + 8, { align: "right" });
    doc.text(`رقم الهاتف: ${user.phone_number || "غير محدد"}`, pageWidth - marginHorizontal - 35, topY + 8, {
      align: "left",
    });

    // إضافة معلومات إضافية - zakialkholy
    doc.setFontSize(9);
    doc.text(`نوع التقييم: ${quiz.quiz_type === "exam" ? "امتحان" : "واجب"}`, marginHorizontal, topY + 16, { align: "right" });
    doc.text(`الدرجة النهائية: ${quiz.max_score}`, pageWidth / 2, topY + 16, { align: "center" });
    doc.text(`عدد الأسئلة: ${quiz.questions?.length || 0}`, pageWidth - marginHorizontal, topY + 16, { align: "left" });

    // خط فاصل محسن - zakialkholy
    doc.setLineWidth(0.5);
    doc.line(marginHorizontal, topY + 20, pageWidth - marginHorizontal, topY + 20);
    doc.setLineWidth(0.2);

    doc.setFontSize(14);
    doc.setFont("Amiri-Regular", "normal");
  };

  const renderFooter = () => {
    const footerY = pageHeight - 15;

    // خط فوق الفوتر محسن - zakialkholy
    doc.setLineWidth(0.3);
    doc.line(marginHorizontal, footerY - 8, pageWidth - marginHorizontal, footerY - 8);

    // تحسين تصميم الفوتر - zakialkholy
    doc.setFontSize(8);
    doc.setFont("Amiri-Regular", "normal");

    // إضافة تاريخ الإنشاء - zakialkholy
    const currentDate = new Date().toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    doc.text(`تاريخ الإنشاء: ${currentDate}`, marginHorizontal, footerY, { align: "right" });
    doc.text("تم إنشاء هذا الملف بواسطة منصة منصة", pageWidth / 2, footerY, { align: "center" });
    doc.text(`صفحة ${doc.internal.getCurrentPageInfo().pageNumber}`, pageWidth - marginHorizontal, footerY, { align: "left" });

    doc.setFontSize(14);
    doc.setFont("Amiri-Regular", "normal");
  };

  renderHeader();

  for (let i = 0; i < quiz.questions.length; i++) {
    const q = quiz.questions[i];
    const qNumber = toArabicNumber(i + 1);
    const estimatedHeight = estimateQuestionHeight(q);

    if (y + estimatedHeight > pageHeight - contentMarginBottom) {
      renderFooter();
      doc.addPage();
      renderHeader();
      y = contentMarginTop;
    }

    const boxTopY = y;
    let contentY = y + 5;

    // السؤال في سطر واحد
    const qText = `${qNumber}- ${q.text}`;
    doc.text(qText, pageWidth - marginHorizontal - 5, contentY, { align: "right" });
    contentY += 10;

    let maxContentY = contentY;

    // الصورة بجودة عالية مع الحفاظ على النسبة الأصلية
    if (q.image_url) {
      try {
        const imageData = await loadImageWithDimensions(q.image_url);

        // الحد الأقصى للعرض والارتفاع
        const maxWidth = pageWidth - 2 * marginHorizontal - 10;
        const maxHeight = 80; // ارتفاع أقصى معقول

        // حساب الأبعاد مع الحفاظ على النسبة الأصلية
        const { width: imgWidth, height: imgHeight } = calculateImageDimensions(
          imageData.originalWidth,
          imageData.originalHeight,
          maxWidth,
          maxHeight
        );

        // توسيط الصورة أفقياً
        const imgX = marginHorizontal + (maxWidth - imgWidth) / 2;

        doc.addImage(imageData.base64, "JPEG", imgX, contentY, imgWidth, imgHeight);
        contentY += imgHeight + 5; // مسافة بعد الصورة
        maxContentY = contentY;
      } catch (err) {
        console.warn("⚠️ فشل تحميل الصورة:", err);
        // في حالة فشل التحميل، نستخدم الطريقة القديمة
        try {
          const base64 = await toBase64(q.image_url);
          const imgWidth = 100; // حجم معقول
          const imgHeight = 60;
          const imgX = marginHorizontal + 5;
          doc.addImage(base64, "JPEG", imgX, contentY, imgWidth, imgHeight);
          contentY += imgHeight + 5;
          maxContentY = contentY;
        } catch (fallbackErr) {
          console.warn("⚠️ فشل تحميل الصورة نهائياً:", fallbackErr);
        }
      }
    }

    // الاختيارات تحت الصورة وجنب بعض
    let answerY = contentY;
    const answersPerRow = 2; // عدد الاختيارات في كل صف
    const answerWidth = (pageWidth - 2 * marginHorizontal - 20) / answersPerRow; // عرض كل اختيار

    for (let j = 0; j < q.answers.length; j += answersPerRow) {
      // الاختيار الأول (على اليمين)
      if (q.answers[j]) {
        const a1 = `${labels[j] || toArabicNumber(j + 1)}- ${q.answers[j].text}`;
        const a1X = pageWidth - marginHorizontal - 5;
        doc.text(a1, a1X, answerY, { align: "right" });
      }

      // الاختيار الثاني (على اليسار)
      if (q.answers[j + 1]) {
        const a2 = `${labels[j + 1] || toArabicNumber(j + 2)}- ${q.answers[j + 1].text}`;
        const a2X = pageWidth - marginHorizontal - answerWidth;
        doc.text(a2, a2X, answerY, { align: "right" });
      }

      answerY += 10;
    }

    const bottomY = Math.max(answerY, maxContentY);
    const boxHeight = bottomY - boxTopY + 5;

    // تحسين شكل مربع السؤال - zakialkholy
    doc.setLineWidth(0.3);
    doc.setDrawColor(100, 100, 100); // لون رمادي فاتح
    doc.rect(marginHorizontal, boxTopY, pageWidth - 2 * marginHorizontal, boxHeight);
    doc.setDrawColor(0, 0, 0); // إعادة اللون للأسود

    y = bottomY + 12; // زيادة المسافة بين الأسئلة - zakialkholy
  }

  // إضافة صفحة إجابات فارغة إذا كان امتحان - zakialkholy
  if (quiz.quiz_type === "exam" && quiz.questions.length > 0) {
    doc.addPage();
    renderHeader();

    doc.setFontSize(16);
    doc.setFont("Amiri-Regular", "bold");
    doc.text("ورقة الإجابات", pageWidth / 2, contentMarginTop + 10, { align: "center" });

    doc.setFontSize(12);
    doc.setFont("Amiri-Regular", "normal");
    let answerY = contentMarginTop + 30;

    for (let i = 0; i < quiz.questions.length; i++) {
      const qNumber = toArabicNumber(i + 1);
      doc.text(`السؤال ${qNumber}: _______________`, pageWidth - marginHorizontal - 5, answerY, { align: "right" });
      answerY += 15;

      if (answerY > pageHeight - contentMarginBottom) {
        renderFooter();
        doc.addPage();
        renderHeader();
        answerY = contentMarginTop;
      }
    }
  }

  renderFooter();

  // تحسين اسم الملف - zakialkholy
  const fileName = `${quiz.title.replace(/[^\w\s-]/g, '')}_${quiz.quiz_type === "exam" ? "امتحان" : "واجب"}.pdf`;
  doc.save(fileName);

  // إظهار رسالة نجاح - zakialkholy
  const { toast } = await import("react-hot-toast");
  toast.success(`تم تحميل ${quiz.quiz_type === "exam" ? "الامتحان" : "الواجب"} بصيغة PDF بنجاح!`);
}
