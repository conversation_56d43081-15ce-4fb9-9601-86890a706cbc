/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_plyr_dist_plyr_min_js"],{

/***/ "(app-pages-browser)/./node_modules/plyr/dist/plyr.min.js":
/*!********************************************!*\
  !*** ./node_modules/plyr/dist/plyr.min.js ***!
  \********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\"object\"==typeof navigator&&function(e,t){ true?module.exports=t():0}(this,(function(){\"use strict\";function e(e,t,i){return(t=function(e){var t=function(e,t){if(\"object\"!=typeof e||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var s=i.call(e,t||\"default\");if(\"object\"!=typeof s)return s;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===t?String:Number)(e)}(e,\"string\");return\"symbol\"==typeof t?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function t(e,t){for(var i=0;i<t.length;i++){var s=t[i];s.enumerable=s.enumerable||!1,s.configurable=!0,\"value\"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}function i(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function s(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,s)}return i}function n(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){i(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var a={addCSS:!0,thumbWidth:15,watch:!0};var l=function(e){return null!=e?e.constructor:null},r=function(e,t){return!!(e&&t&&e instanceof t)},o=function(e){return null==e},c=function(e){return l(e)===Object},u=function(e){return l(e)===String},h=function(e){return Array.isArray(e)},d=function(e){return r(e,NodeList)},m={nullOrUndefined:o,object:c,number:function(e){return l(e)===Number&&!Number.isNaN(e)},string:u,boolean:function(e){return l(e)===Boolean},function:function(e){return l(e)===Function},array:h,nodeList:d,element:function(e){return r(e,Element)},event:function(e){return r(e,Event)},empty:function(e){return o(e)||(u(e)||h(e)||d(e))&&!e.length||c(e)&&!Object.keys(e).length}};function p(e,t){if(1>t){var i=function(e){var t=\"\".concat(e).match(/(?:\\.(\\d+))?(?:[eE]([+-]?\\d+))?$/);return t?Math.max(0,(t[1]?t[1].length:0)-(t[2]?+t[2]:0)):0}(t);return parseFloat(e.toFixed(i))}return Math.round(e/t)*t}var g=function(){function e(t,i){(function(e,t){if(!(e instanceof t))throw new TypeError(\"Cannot call a class as a function\")})(this,e),m.element(t)?this.element=t:m.string(t)&&(this.element=document.querySelector(t)),m.element(this.element)&&m.empty(this.element.rangeTouch)&&(this.config=n({},a,{},i),this.init())}return function(e,i,s){i&&t(e.prototype,i),s&&t(e,s)}(e,[{key:\"init\",value:function(){e.enabled&&(this.config.addCSS&&(this.element.style.userSelect=\"none\",this.element.style.webKitUserSelect=\"none\",this.element.style.touchAction=\"manipulation\"),this.listeners(!0),this.element.rangeTouch=this)}},{key:\"destroy\",value:function(){e.enabled&&(this.config.addCSS&&(this.element.style.userSelect=\"\",this.element.style.webKitUserSelect=\"\",this.element.style.touchAction=\"\"),this.listeners(!1),this.element.rangeTouch=null)}},{key:\"listeners\",value:function(e){var t=this,i=e?\"addEventListener\":\"removeEventListener\";[\"touchstart\",\"touchmove\",\"touchend\"].forEach((function(e){t.element[i](e,(function(e){return t.set(e)}),!1)}))}},{key:\"get\",value:function(t){if(!e.enabled||!m.event(t))return null;var i,s=t.target,n=t.changedTouches[0],a=parseFloat(s.getAttribute(\"min\"))||0,l=parseFloat(s.getAttribute(\"max\"))||100,r=parseFloat(s.getAttribute(\"step\"))||1,o=s.getBoundingClientRect(),c=100/o.width*(this.config.thumbWidth/2)/100;return 0>(i=100/o.width*(n.clientX-o.left))?i=0:100<i&&(i=100),50>i?i-=(100-2*i)*c:50<i&&(i+=2*(i-50)*c),a+p(i/100*(l-a),r)}},{key:\"set\",value:function(t){e.enabled&&m.event(t)&&!t.target.disabled&&(t.preventDefault(),t.target.value=this.get(t),function(e,t){if(e&&t){var i=new Event(t,{bubbles:!0});e.dispatchEvent(i)}}(t.target,\"touchend\"===t.type?\"change\":\"input\"))}}],[{key:\"setup\",value:function(t){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},s=null;if(m.empty(t)||m.string(t)?s=Array.from(document.querySelectorAll(m.string(t)?t:'input[type=\"range\"]')):m.element(t)?s=[t]:m.nodeList(t)?s=Array.from(t):m.array(t)&&(s=t.filter(m.element)),m.empty(s))return null;var l=n({},a,{},i);if(m.string(t)&&l.watch){var r=new MutationObserver((function(i){Array.from(i).forEach((function(i){Array.from(i.addedNodes).forEach((function(i){m.element(i)&&function(e,t){return function(){return Array.from(document.querySelectorAll(t)).includes(this)}.call(e,t)}(i,t)&&new e(i,l)}))}))}));r.observe(document.body,{childList:!0,subtree:!0})}return s.map((function(t){return new e(t,i)}))}},{key:\"enabled\",get:function(){return\"ontouchstart\"in document.documentElement}}]),e}();const f=e=>null!=e?e.constructor:null,y=(e,t)=>Boolean(e&&t&&e instanceof t),b=e=>null==e,v=e=>f(e)===Object,w=e=>f(e)===String,T=e=>\"function\"==typeof e,k=e=>Array.isArray(e),C=e=>y(e,NodeList),A=e=>b(e)||(w(e)||k(e)||C(e))&&!e.length||v(e)&&!Object.keys(e).length;var S={nullOrUndefined:b,object:v,number:e=>f(e)===Number&&!Number.isNaN(e),string:w,boolean:e=>f(e)===Boolean,function:T,array:k,weakMap:e=>y(e,WeakMap),nodeList:C,element:e=>null!==e&&\"object\"==typeof e&&1===e.nodeType&&\"object\"==typeof e.style&&\"object\"==typeof e.ownerDocument,textNode:e=>f(e)===Text,event:e=>y(e,Event),keyboardEvent:e=>y(e,KeyboardEvent),cue:e=>y(e,window.TextTrackCue)||y(e,window.VTTCue),track:e=>y(e,TextTrack)||!b(e)&&w(e.kind),promise:e=>y(e,Promise)&&T(e.then),url:e=>{if(y(e,window.URL))return!0;if(!w(e))return!1;let t=e;e.startsWith(\"http://\")&&e.startsWith(\"https://\")||(t=`http://${e}`);try{return!A(new URL(t).hostname)}catch(e){return!1}},empty:A};const E=(()=>{const e=document.createElement(\"span\"),t={WebkitTransition:\"webkitTransitionEnd\",MozTransition:\"transitionend\",OTransition:\"oTransitionEnd otransitionend\",transition:\"transitionend\"},i=Object.keys(t).find((t=>void 0!==e.style[t]));return!!S.string(i)&&t[i]})();function P(e,t){setTimeout((()=>{try{e.hidden=!0,e.offsetHeight,e.hidden=!1}catch(e){}}),t)}var M={isIE:Boolean(window.document.documentMode),isEdge:/Edge/g.test(navigator.userAgent),isWebKit:\"WebkitAppearance\"in document.documentElement.style&&!/Edge/g.test(navigator.userAgent),isIPhone:/iPhone|iPod/gi.test(navigator.userAgent)&&navigator.maxTouchPoints>1,isIPadOS:\"MacIntel\"===navigator.platform&&navigator.maxTouchPoints>1,isIos:/iPad|iPhone|iPod/gi.test(navigator.userAgent)&&navigator.maxTouchPoints>1};function N(e,t){return t.split(\".\").reduce(((e,t)=>e&&e[t]),e)}function x(e={},...t){if(!t.length)return e;const i=t.shift();return S.object(i)?(Object.keys(i).forEach((t=>{S.object(i[t])?(Object.keys(e).includes(t)||Object.assign(e,{[t]:{}}),x(e[t],i[t])):Object.assign(e,{[t]:i[t]})})),x(e,...t)):e}function L(e,t){const i=e.length?e:[e];Array.from(i).reverse().forEach(((e,i)=>{const s=i>0?t.cloneNode(!0):t,n=e.parentNode,a=e.nextSibling;s.appendChild(e),a?n.insertBefore(s,a):n.appendChild(s)}))}function I(e,t){S.element(e)&&!S.empty(t)&&Object.entries(t).filter((([,e])=>!S.nullOrUndefined(e))).forEach((([t,i])=>e.setAttribute(t,i)))}function $(e,t,i){const s=document.createElement(e);return S.object(t)&&I(s,t),S.string(i)&&(s.innerText=i),s}function _(e,t,i,s){S.element(t)&&t.appendChild($(e,i,s))}function O(e){S.nodeList(e)||S.array(e)?Array.from(e).forEach(O):S.element(e)&&S.element(e.parentNode)&&e.parentNode.removeChild(e)}function j(e){if(!S.element(e))return;let{length:t}=e.childNodes;for(;t>0;)e.removeChild(e.lastChild),t-=1}function q(e,t){return S.element(t)&&S.element(t.parentNode)&&S.element(e)?(t.parentNode.replaceChild(e,t),e):null}function D(e,t){if(!S.string(e)||S.empty(e))return{};const i={},s=x({},t);return e.split(\",\").forEach((e=>{const t=e.trim(),n=t.replace(\".\",\"\"),a=t.replace(/[[\\]]/g,\"\").split(\"=\"),[l]=a,r=a.length>1?a[1].replace(/[\"']/g,\"\"):\"\";switch(t.charAt(0)){case\".\":S.string(s.class)?i.class=`${s.class} ${n}`:i.class=n;break;case\"#\":i.id=t.replace(\"#\",\"\");break;case\"[\":i[l]=r}})),x(s,i)}function H(e,t){if(!S.element(e))return;let i=t;S.boolean(i)||(i=!e.hidden),e.hidden=i}function R(e,t,i){if(S.nodeList(e))return Array.from(e).map((e=>R(e,t,i)));if(S.element(e)){let s=\"toggle\";return void 0!==i&&(s=i?\"add\":\"remove\"),e.classList[s](t),e.classList.contains(t)}return!1}function F(e,t){return S.element(e)&&e.classList.contains(t)}function V(e,t){const{prototype:i}=Element;return(i.matches||i.webkitMatchesSelector||i.mozMatchesSelector||i.msMatchesSelector||function(){return Array.from(document.querySelectorAll(t)).includes(this)}).call(e,t)}function U(e){return this.elements.container.querySelectorAll(e)}function B(e){return this.elements.container.querySelector(e)}function W(e=null,t=!1){S.element(e)&&e.focus({preventScroll:!0,focusVisible:t})}const z={\"audio/ogg\":\"vorbis\",\"audio/wav\":\"1\",\"video/webm\":\"vp8, vorbis\",\"video/mp4\":\"avc1.42E01E, mp4a.40.2\",\"video/ogg\":\"theora\"},K={audio:\"canPlayType\"in document.createElement(\"audio\"),video:\"canPlayType\"in document.createElement(\"video\"),check(e,t){const i=K[e]||\"html5\"!==t;return{api:i,ui:i&&K.rangeInput}},pip:!(M.isIPhone||!S.function($(\"video\").webkitSetPresentationMode)&&(!document.pictureInPictureEnabled||$(\"video\").disablePictureInPicture)),airplay:S.function(window.WebKitPlaybackTargetAvailabilityEvent),playsinline:\"playsInline\"in document.createElement(\"video\"),mime(e){if(S.empty(e))return!1;const[t]=e.split(\"/\");let i=e;if(!this.isHTML5||t!==this.type)return!1;Object.keys(z).includes(i)&&(i+=`; codecs=\"${z[e]}\"`);try{return Boolean(i&&this.media.canPlayType(i).replace(/no/,\"\"))}catch(e){return!1}},textTracks:\"textTracks\"in document.createElement(\"video\"),rangeInput:(()=>{const e=document.createElement(\"input\");return e.type=\"range\",\"range\"===e.type})(),touch:\"ontouchstart\"in document.documentElement,transitions:!1!==E,reducedMotion:\"matchMedia\"in window&&window.matchMedia(\"(prefers-reduced-motion)\").matches},Y=(()=>{let e=!1;try{const t=Object.defineProperty({},\"passive\",{get:()=>(e=!0,null)});window.addEventListener(\"test\",null,t),window.removeEventListener(\"test\",null,t)}catch(e){}return e})();function Q(e,t,i,s=!1,n=!0,a=!1){if(!e||!(\"addEventListener\"in e)||S.empty(t)||!S.function(i))return;const l=t.split(\" \");let r=a;Y&&(r={passive:n,capture:a}),l.forEach((t=>{this&&this.eventListeners&&s&&this.eventListeners.push({element:e,type:t,callback:i,options:r}),e[s?\"addEventListener\":\"removeEventListener\"](t,i,r)}))}function X(e,t=\"\",i,s=!0,n=!1){Q.call(this,e,t,i,!0,s,n)}function J(e,t=\"\",i,s=!0,n=!1){Q.call(this,e,t,i,!1,s,n)}function G(e,t=\"\",i,s=!0,n=!1){const a=(...l)=>{J(e,t,a,s,n),i.apply(this,l)};Q.call(this,e,t,a,!0,s,n)}function Z(e,t=\"\",i=!1,s={}){if(!S.element(e)||S.empty(t))return;const n=new CustomEvent(t,{bubbles:i,detail:{...s,plyr:this}});e.dispatchEvent(n)}function ee(){this&&this.eventListeners&&(this.eventListeners.forEach((e=>{const{element:t,type:i,callback:s,options:n}=e;t.removeEventListener(i,s,n)})),this.eventListeners=[])}function te(){return new Promise((e=>this.ready?setTimeout(e,0):X.call(this,this.elements.container,\"ready\",e))).then((()=>{}))}function ie(e){S.promise(e)&&e.then(null,(()=>{}))}function se(e){return S.array(e)?e.filter(((t,i)=>e.indexOf(t)===i)):e}function ne(e,t){return S.array(e)&&e.length?e.reduce(((e,i)=>Math.abs(i-t)<Math.abs(e-t)?i:e)):null}function ae(e){return!(!window||!window.CSS)&&window.CSS.supports(e)}const le=[[1,1],[4,3],[3,4],[5,4],[4,5],[3,2],[2,3],[16,10],[10,16],[16,9],[9,16],[21,9],[9,21],[32,9],[9,32]].reduce(((e,[t,i])=>({...e,[t/i]:[t,i]})),{});function re(e){if(!(S.array(e)||S.string(e)&&e.includes(\":\")))return!1;return(S.array(e)?e:e.split(\":\")).map(Number).every(S.number)}function oe(e){if(!S.array(e)||!e.every(S.number))return null;const[t,i]=e,s=(e,t)=>0===t?e:s(t,e%t),n=s(t,i);return[t/n,i/n]}function ce(e){const t=e=>re(e)?e.split(\":\").map(Number):null;let i=t(e);if(null===i&&(i=t(this.config.ratio)),null===i&&!S.empty(this.embed)&&S.array(this.embed.ratio)&&({ratio:i}=this.embed),null===i&&this.isHTML5){const{videoWidth:e,videoHeight:t}=this.media;i=[e,t]}return oe(i)}function ue(e){if(!this.isVideo)return{};const{wrapper:t}=this.elements,i=ce.call(this,e);if(!S.array(i))return{};const[s,n]=oe(i),a=100/s*n;if(ae(`aspect-ratio: ${s}/${n}`)?t.style.aspectRatio=`${s}/${n}`:t.style.paddingBottom=`${a}%`,this.isVimeo&&!this.config.vimeo.premium&&this.supported.ui){const e=100/this.media.offsetWidth*parseInt(window.getComputedStyle(this.media).paddingBottom,10),i=(e-a)/(e/50);this.fullscreen.active?t.style.paddingBottom=null:this.media.style.transform=`translateY(-${i}%)`}else this.isHTML5&&t.classList.add(this.config.classNames.videoFixedRatio);return{padding:a,ratio:i}}function he(e,t,i=.05){const s=e/t,n=ne(Object.keys(le),s);return Math.abs(n-s)<=i?le[n]:[e,t]}const de={getSources(){if(!this.isHTML5)return[];return Array.from(this.media.querySelectorAll(\"source\")).filter((e=>{const t=e.getAttribute(\"type\");return!!S.empty(t)||K.mime.call(this,t)}))},getQualityOptions(){return this.config.quality.forced?this.config.quality.options:de.getSources.call(this).map((e=>Number(e.getAttribute(\"size\")))).filter(Boolean)},setup(){if(!this.isHTML5)return;const e=this;e.options.speed=e.config.speed.options,S.empty(this.config.ratio)||ue.call(e),Object.defineProperty(e.media,\"quality\",{get(){const t=de.getSources.call(e).find((t=>t.getAttribute(\"src\")===e.source));return t&&Number(t.getAttribute(\"size\"))},set(t){if(e.quality!==t){if(e.config.quality.forced&&S.function(e.config.quality.onChange))e.config.quality.onChange(t);else{const i=de.getSources.call(e).find((e=>Number(e.getAttribute(\"size\"))===t));if(!i)return;const{currentTime:s,paused:n,preload:a,readyState:l,playbackRate:r}=e.media;e.media.src=i.getAttribute(\"src\"),(\"none\"!==a||l)&&(e.once(\"loadedmetadata\",(()=>{e.speed=r,e.currentTime=s,n||ie(e.play())})),e.media.load())}Z.call(e,e.media,\"qualitychange\",!1,{quality:t})}}})},cancelRequests(){this.isHTML5&&(O(de.getSources.call(this)),this.media.setAttribute(\"src\",this.config.blankVideo),this.media.load(),this.debug.log(\"Cancelled network requests\"))}};function me(e,...t){return S.empty(e)?e:e.toString().replace(/{(\\d+)}/g,((e,i)=>t[i].toString()))}const pe=(e=\"\",t=\"\",i=\"\")=>e.replace(new RegExp(t.toString().replace(/([.*+?^=!:${}()|[\\]/\\\\])/g,\"\\\\$1\"),\"g\"),i.toString()),ge=(e=\"\")=>e.toString().replace(/\\w\\S*/g,(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()));function fe(e=\"\"){let t=e.toString();return t=function(e=\"\"){let t=e.toString();return t=pe(t,\"-\",\" \"),t=pe(t,\"_\",\" \"),t=ge(t),pe(t,\" \",\"\")}(t),t.charAt(0).toLowerCase()+t.slice(1)}function ye(e){const t=document.createElement(\"div\");return t.appendChild(e),t.innerHTML}const be={pip:\"PIP\",airplay:\"AirPlay\",html5:\"HTML5\",vimeo:\"Vimeo\",youtube:\"YouTube\"},ve={get(e=\"\",t={}){if(S.empty(e)||S.empty(t))return\"\";let i=N(t.i18n,e);if(S.empty(i))return Object.keys(be).includes(e)?be[e]:\"\";const s={\"{seektime}\":t.seekTime,\"{title}\":t.title};return Object.entries(s).forEach((([e,t])=>{i=pe(i,e,t)})),i}};class we{constructor(t){e(this,\"get\",(e=>{if(!we.supported||!this.enabled)return null;const t=window.localStorage.getItem(this.key);if(S.empty(t))return null;const i=JSON.parse(t);return S.string(e)&&e.length?i[e]:i})),e(this,\"set\",(e=>{if(!we.supported||!this.enabled)return;if(!S.object(e))return;let t=this.get();S.empty(t)&&(t={}),x(t,e);try{window.localStorage.setItem(this.key,JSON.stringify(t))}catch(e){}})),this.enabled=t.config.storage.enabled,this.key=t.config.storage.key}static get supported(){try{if(!(\"localStorage\"in window))return!1;const e=\"___test\";return window.localStorage.setItem(e,e),window.localStorage.removeItem(e),!0}catch(e){return!1}}}function Te(e,t=\"text\"){return new Promise(((i,s)=>{try{const s=new XMLHttpRequest;if(!(\"withCredentials\"in s))return;s.addEventListener(\"load\",(()=>{if(\"text\"===t)try{i(JSON.parse(s.responseText))}catch(e){i(s.responseText)}else i(s.response)})),s.addEventListener(\"error\",(()=>{throw new Error(s.status)})),s.open(\"GET\",e,!0),s.responseType=t,s.send()}catch(e){s(e)}}))}function ke(e,t){if(!S.string(e))return;const i=\"cache\",s=S.string(t);let n=!1;const a=()=>null!==document.getElementById(t),l=(e,t)=>{e.innerHTML=t,s&&a()||document.body.insertAdjacentElement(\"afterbegin\",e)};if(!s||!a()){const a=we.supported,r=document.createElement(\"div\");if(r.setAttribute(\"hidden\",\"\"),s&&r.setAttribute(\"id\",t),a){const e=window.localStorage.getItem(`${i}-${t}`);if(n=null!==e,n){const t=JSON.parse(e);l(r,t.content)}}Te(e).then((e=>{if(!S.empty(e)){if(a)try{window.localStorage.setItem(`${i}-${t}`,JSON.stringify({content:e}))}catch(e){}l(r,e)}})).catch((()=>{}))}}const Ce=e=>Math.trunc(e/60/60%60,10),Ae=e=>Math.trunc(e/60%60,10),Se=e=>Math.trunc(e%60,10);function Ee(e=0,t=!1,i=!1){if(!S.number(e))return Ee(void 0,t,i);const s=e=>`0${e}`.slice(-2);let n=Ce(e);const a=Ae(e),l=Se(e);return n=t||n>0?`${n}:`:\"\",`${i&&e>0?\"-\":\"\"}${n}${s(a)}:${s(l)}`}const Pe={getIconUrl(){const e=new URL(this.config.iconUrl,window.location),t=window.location.host?window.location.host:window.top.location.host,i=e.host!==t||M.isIE&&!window.svg4everybody;return{url:this.config.iconUrl,cors:i}},findElements(){try{return this.elements.controls=B.call(this,this.config.selectors.controls.wrapper),this.elements.buttons={play:U.call(this,this.config.selectors.buttons.play),pause:B.call(this,this.config.selectors.buttons.pause),restart:B.call(this,this.config.selectors.buttons.restart),rewind:B.call(this,this.config.selectors.buttons.rewind),fastForward:B.call(this,this.config.selectors.buttons.fastForward),mute:B.call(this,this.config.selectors.buttons.mute),pip:B.call(this,this.config.selectors.buttons.pip),airplay:B.call(this,this.config.selectors.buttons.airplay),settings:B.call(this,this.config.selectors.buttons.settings),captions:B.call(this,this.config.selectors.buttons.captions),fullscreen:B.call(this,this.config.selectors.buttons.fullscreen)},this.elements.progress=B.call(this,this.config.selectors.progress),this.elements.inputs={seek:B.call(this,this.config.selectors.inputs.seek),volume:B.call(this,this.config.selectors.inputs.volume)},this.elements.display={buffer:B.call(this,this.config.selectors.display.buffer),currentTime:B.call(this,this.config.selectors.display.currentTime),duration:B.call(this,this.config.selectors.display.duration)},S.element(this.elements.progress)&&(this.elements.display.seekTooltip=this.elements.progress.querySelector(`.${this.config.classNames.tooltip}`)),!0}catch(e){return this.debug.warn(\"It looks like there is a problem with your custom controls HTML\",e),this.toggleNativeControls(!0),!1}},createIcon(e,t){const i=\"http://www.w3.org/2000/svg\",s=Pe.getIconUrl.call(this),n=`${s.cors?\"\":s.url}#${this.config.iconPrefix}`,a=document.createElementNS(i,\"svg\");I(a,x(t,{\"aria-hidden\":\"true\",focusable:\"false\"}));const l=document.createElementNS(i,\"use\"),r=`${n}-${e}`;return\"href\"in l&&l.setAttributeNS(\"http://www.w3.org/1999/xlink\",\"href\",r),l.setAttributeNS(\"http://www.w3.org/1999/xlink\",\"xlink:href\",r),a.appendChild(l),a},createLabel(e,t={}){const i=ve.get(e,this.config);return $(\"span\",{...t,class:[t.class,this.config.classNames.hidden].filter(Boolean).join(\" \")},i)},createBadge(e){if(S.empty(e))return null;const t=$(\"span\",{class:this.config.classNames.menu.value});return t.appendChild($(\"span\",{class:this.config.classNames.menu.badge},e)),t},createButton(e,t){const i=x({},t);let s=fe(e);const n={element:\"button\",toggle:!1,label:null,icon:null,labelPressed:null,iconPressed:null};switch([\"element\",\"icon\",\"label\"].forEach((e=>{Object.keys(i).includes(e)&&(n[e]=i[e],delete i[e])})),\"button\"!==n.element||Object.keys(i).includes(\"type\")||(i.type=\"button\"),Object.keys(i).includes(\"class\")?i.class.split(\" \").some((e=>e===this.config.classNames.control))||x(i,{class:`${i.class} ${this.config.classNames.control}`}):i.class=this.config.classNames.control,e){case\"play\":n.toggle=!0,n.label=\"play\",n.labelPressed=\"pause\",n.icon=\"play\",n.iconPressed=\"pause\";break;case\"mute\":n.toggle=!0,n.label=\"mute\",n.labelPressed=\"unmute\",n.icon=\"volume\",n.iconPressed=\"muted\";break;case\"captions\":n.toggle=!0,n.label=\"enableCaptions\",n.labelPressed=\"disableCaptions\",n.icon=\"captions-off\",n.iconPressed=\"captions-on\";break;case\"fullscreen\":n.toggle=!0,n.label=\"enterFullscreen\",n.labelPressed=\"exitFullscreen\",n.icon=\"enter-fullscreen\",n.iconPressed=\"exit-fullscreen\";break;case\"play-large\":i.class+=` ${this.config.classNames.control}--overlaid`,s=\"play\",n.label=\"play\",n.icon=\"play\";break;default:S.empty(n.label)&&(n.label=s),S.empty(n.icon)&&(n.icon=e)}const a=$(n.element);return n.toggle?(a.appendChild(Pe.createIcon.call(this,n.iconPressed,{class:\"icon--pressed\"})),a.appendChild(Pe.createIcon.call(this,n.icon,{class:\"icon--not-pressed\"})),a.appendChild(Pe.createLabel.call(this,n.labelPressed,{class:\"label--pressed\"})),a.appendChild(Pe.createLabel.call(this,n.label,{class:\"label--not-pressed\"}))):(a.appendChild(Pe.createIcon.call(this,n.icon)),a.appendChild(Pe.createLabel.call(this,n.label))),x(i,D(this.config.selectors.buttons[s],i)),I(a,i),\"play\"===s?(S.array(this.elements.buttons[s])||(this.elements.buttons[s]=[]),this.elements.buttons[s].push(a)):this.elements.buttons[s]=a,a},createRange(e,t){const i=$(\"input\",x(D(this.config.selectors.inputs[e]),{type:\"range\",min:0,max:100,step:.01,value:0,autocomplete:\"off\",role:\"slider\",\"aria-label\":ve.get(e,this.config),\"aria-valuemin\":0,\"aria-valuemax\":100,\"aria-valuenow\":0},t));return this.elements.inputs[e]=i,Pe.updateRangeFill.call(this,i),g.setup(i),i},createProgress(e,t){const i=$(\"progress\",x(D(this.config.selectors.display[e]),{min:0,max:100,value:0,role:\"progressbar\",\"aria-hidden\":!0},t));if(\"volume\"!==e){i.appendChild($(\"span\",null,\"0\"));const t={played:\"played\",buffer:\"buffered\"}[e],s=t?ve.get(t,this.config):\"\";i.innerText=`% ${s.toLowerCase()}`}return this.elements.display[e]=i,i},createTime(e,t){const i=D(this.config.selectors.display[e],t),s=$(\"div\",x(i,{class:`${i.class?i.class:\"\"} ${this.config.classNames.display.time} `.trim(),\"aria-label\":ve.get(e,this.config),role:\"timer\"}),\"00:00\");return this.elements.display[e]=s,s},bindMenuItemShortcuts(e,t){X.call(this,e,\"keydown keyup\",(i=>{if(![\" \",\"ArrowUp\",\"ArrowDown\",\"ArrowRight\"].includes(i.key))return;if(i.preventDefault(),i.stopPropagation(),\"keydown\"===i.type)return;const s=V(e,'[role=\"menuitemradio\"]');if(!s&&[\" \",\"ArrowRight\"].includes(i.key))Pe.showMenuPanel.call(this,t,!0);else{let t;\" \"!==i.key&&(\"ArrowDown\"===i.key||s&&\"ArrowRight\"===i.key?(t=e.nextElementSibling,S.element(t)||(t=e.parentNode.firstElementChild)):(t=e.previousElementSibling,S.element(t)||(t=e.parentNode.lastElementChild)),W.call(this,t,!0))}}),!1),X.call(this,e,\"keyup\",(e=>{\"Return\"===e.key&&Pe.focusFirstMenuItem.call(this,null,!0)}))},createMenuItem({value:e,list:t,type:i,title:s,badge:n=null,checked:a=!1}){const l=D(this.config.selectors.inputs[i]),r=$(\"button\",x(l,{type:\"button\",role:\"menuitemradio\",class:`${this.config.classNames.control} ${l.class?l.class:\"\"}`.trim(),\"aria-checked\":a,value:e})),o=$(\"span\");o.innerHTML=s,S.element(n)&&o.appendChild(n),r.appendChild(o),Object.defineProperty(r,\"checked\",{enumerable:!0,get:()=>\"true\"===r.getAttribute(\"aria-checked\"),set(e){e&&Array.from(r.parentNode.children).filter((e=>V(e,'[role=\"menuitemradio\"]'))).forEach((e=>e.setAttribute(\"aria-checked\",\"false\"))),r.setAttribute(\"aria-checked\",e?\"true\":\"false\")}}),this.listeners.bind(r,\"click keyup\",(t=>{if(!S.keyboardEvent(t)||\" \"===t.key){switch(t.preventDefault(),t.stopPropagation(),r.checked=!0,i){case\"language\":this.currentTrack=Number(e);break;case\"quality\":this.quality=e;break;case\"speed\":this.speed=parseFloat(e)}Pe.showMenuPanel.call(this,\"home\",S.keyboardEvent(t))}}),i,!1),Pe.bindMenuItemShortcuts.call(this,r,i),t.appendChild(r)},formatTime(e=0,t=!1){if(!S.number(e))return e;return Ee(e,Ce(this.duration)>0,t)},updateTimeDisplay(e=null,t=0,i=!1){S.element(e)&&S.number(t)&&(e.innerText=Pe.formatTime(t,i))},updateVolume(){this.supported.ui&&(S.element(this.elements.inputs.volume)&&Pe.setRange.call(this,this.elements.inputs.volume,this.muted?0:this.volume),S.element(this.elements.buttons.mute)&&(this.elements.buttons.mute.pressed=this.muted||0===this.volume))},setRange(e,t=0){S.element(e)&&(e.value=t,Pe.updateRangeFill.call(this,e))},updateProgress(e){if(!this.supported.ui||!S.event(e))return;let t=0;const i=(e,t)=>{const i=S.number(t)?t:0,s=S.element(e)?e:this.elements.display.buffer;if(S.element(s)){s.value=i;const e=s.getElementsByTagName(\"span\")[0];S.element(e)&&(e.childNodes[0].nodeValue=i)}};if(e)switch(e.type){case\"timeupdate\":case\"seeking\":case\"seeked\":s=this.currentTime,n=this.duration,t=0===s||0===n||Number.isNaN(s)||Number.isNaN(n)?0:(s/n*100).toFixed(2),\"timeupdate\"===e.type&&Pe.setRange.call(this,this.elements.inputs.seek,t);break;case\"playing\":case\"progress\":i(this.elements.display.buffer,100*this.buffered)}var s,n},updateRangeFill(e){const t=S.event(e)?e.target:e;if(S.element(t)&&\"range\"===t.getAttribute(\"type\")){if(V(t,this.config.selectors.inputs.seek)){t.setAttribute(\"aria-valuenow\",this.currentTime);const e=Pe.formatTime(this.currentTime),i=Pe.formatTime(this.duration),s=ve.get(\"seekLabel\",this.config);t.setAttribute(\"aria-valuetext\",s.replace(\"{currentTime}\",e).replace(\"{duration}\",i))}else if(V(t,this.config.selectors.inputs.volume)){const e=100*t.value;t.setAttribute(\"aria-valuenow\",e),t.setAttribute(\"aria-valuetext\",`${e.toFixed(1)}%`)}else t.setAttribute(\"aria-valuenow\",t.value);(M.isWebKit||M.isIPadOS)&&t.style.setProperty(\"--value\",t.value/t.max*100+\"%\")}},updateSeekTooltip(e){var t,i;if(!this.config.tooltips.seek||!S.element(this.elements.inputs.seek)||!S.element(this.elements.display.seekTooltip)||0===this.duration)return;const s=this.elements.display.seekTooltip,n=`${this.config.classNames.tooltip}--visible`,a=e=>R(s,n,e);if(this.touch)return void a(!1);let l=0;const r=this.elements.progress.getBoundingClientRect();if(S.event(e))l=100/r.width*(e.pageX-r.left);else{if(!F(s,n))return;l=parseFloat(s.style.left,10)}l<0?l=0:l>100&&(l=100);const o=this.duration/100*l;s.innerText=Pe.formatTime(o);const c=null===(t=this.config.markers)||void 0===t||null===(i=t.points)||void 0===i?void 0:i.find((({time:e})=>e===Math.round(o)));c&&s.insertAdjacentHTML(\"afterbegin\",`${c.label}<br>`),s.style.left=`${l}%`,S.event(e)&&[\"mouseenter\",\"mouseleave\"].includes(e.type)&&a(\"mouseenter\"===e.type)},timeUpdate(e){const t=!S.element(this.elements.display.duration)&&this.config.invertTime;Pe.updateTimeDisplay.call(this,this.elements.display.currentTime,t?this.duration-this.currentTime:this.currentTime,t),e&&\"timeupdate\"===e.type&&this.media.seeking||Pe.updateProgress.call(this,e)},durationUpdate(){if(!this.supported.ui||!this.config.invertTime&&this.currentTime)return;if(this.duration>=2**32)return H(this.elements.display.currentTime,!0),void H(this.elements.progress,!0);S.element(this.elements.inputs.seek)&&this.elements.inputs.seek.setAttribute(\"aria-valuemax\",this.duration);const e=S.element(this.elements.display.duration);!e&&this.config.displayDuration&&this.paused&&Pe.updateTimeDisplay.call(this,this.elements.display.currentTime,this.duration),e&&Pe.updateTimeDisplay.call(this,this.elements.display.duration,this.duration),this.config.markers.enabled&&Pe.setMarkers.call(this),Pe.updateSeekTooltip.call(this)},toggleMenuButton(e,t){H(this.elements.settings.buttons[e],!t)},updateSetting(e,t,i){const s=this.elements.settings.panels[e];let n=null,a=t;if(\"captions\"===e)n=this.currentTrack;else{if(n=S.empty(i)?this[e]:i,S.empty(n)&&(n=this.config[e].default),!S.empty(this.options[e])&&!this.options[e].includes(n))return void this.debug.warn(`Unsupported value of '${n}' for ${e}`);if(!this.config[e].options.includes(n))return void this.debug.warn(`Disabled value of '${n}' for ${e}`)}if(S.element(a)||(a=s&&s.querySelector('[role=\"menu\"]')),!S.element(a))return;this.elements.settings.buttons[e].querySelector(`.${this.config.classNames.menu.value}`).innerHTML=Pe.getLabel.call(this,e,n);const l=a&&a.querySelector(`[value=\"${n}\"]`);S.element(l)&&(l.checked=!0)},getLabel(e,t){switch(e){case\"speed\":return 1===t?ve.get(\"normal\",this.config):`${t}&times;`;case\"quality\":if(S.number(t)){const e=ve.get(`qualityLabel.${t}`,this.config);return e.length?e:`${t}p`}return ge(t);case\"captions\":return xe.getLabel.call(this);default:return null}},setQualityMenu(e){if(!S.element(this.elements.settings.panels.quality))return;const t=\"quality\",i=this.elements.settings.panels.quality.querySelector('[role=\"menu\"]');S.array(e)&&(this.options.quality=se(e).filter((e=>this.config.quality.options.includes(e))));const s=!S.empty(this.options.quality)&&this.options.quality.length>1;if(Pe.toggleMenuButton.call(this,t,s),j(i),Pe.checkMenu.call(this),!s)return;const n=e=>{const t=ve.get(`qualityBadge.${e}`,this.config);return t.length?Pe.createBadge.call(this,t):null};this.options.quality.sort(((e,t)=>{const i=this.config.quality.options;return i.indexOf(e)>i.indexOf(t)?1:-1})).forEach((e=>{Pe.createMenuItem.call(this,{value:e,list:i,type:t,title:Pe.getLabel.call(this,\"quality\",e),badge:n(e)})})),Pe.updateSetting.call(this,t,i)},setCaptionsMenu(){if(!S.element(this.elements.settings.panels.captions))return;const e=\"captions\",t=this.elements.settings.panels.captions.querySelector('[role=\"menu\"]'),i=xe.getTracks.call(this),s=Boolean(i.length);if(Pe.toggleMenuButton.call(this,e,s),j(t),Pe.checkMenu.call(this),!s)return;const n=i.map(((e,i)=>({value:i,checked:this.captions.toggled&&this.currentTrack===i,title:xe.getLabel.call(this,e),badge:e.language&&Pe.createBadge.call(this,e.language.toUpperCase()),list:t,type:\"language\"})));n.unshift({value:-1,checked:!this.captions.toggled,title:ve.get(\"disabled\",this.config),list:t,type:\"language\"}),n.forEach(Pe.createMenuItem.bind(this)),Pe.updateSetting.call(this,e,t)},setSpeedMenu(){if(!S.element(this.elements.settings.panels.speed))return;const e=\"speed\",t=this.elements.settings.panels.speed.querySelector('[role=\"menu\"]');this.options.speed=this.options.speed.filter((e=>e>=this.minimumSpeed&&e<=this.maximumSpeed));const i=!S.empty(this.options.speed)&&this.options.speed.length>1;Pe.toggleMenuButton.call(this,e,i),j(t),Pe.checkMenu.call(this),i&&(this.options.speed.forEach((i=>{Pe.createMenuItem.call(this,{value:i,list:t,type:e,title:Pe.getLabel.call(this,\"speed\",i)})})),Pe.updateSetting.call(this,e,t))},checkMenu(){const{buttons:e}=this.elements.settings,t=!S.empty(e)&&Object.values(e).some((e=>!e.hidden));H(this.elements.settings.menu,!t)},focusFirstMenuItem(e,t=!1){if(this.elements.settings.popup.hidden)return;let i=e;S.element(i)||(i=Object.values(this.elements.settings.panels).find((e=>!e.hidden)));const s=i.querySelector('[role^=\"menuitem\"]');W.call(this,s,t)},toggleMenu(e){const{popup:t}=this.elements.settings,i=this.elements.buttons.settings;if(!S.element(t)||!S.element(i))return;const{hidden:s}=t;let n=s;if(S.boolean(e))n=e;else if(S.keyboardEvent(e)&&\"Escape\"===e.key)n=!1;else if(S.event(e)){const s=S.function(e.composedPath)?e.composedPath()[0]:e.target,a=t.contains(s);if(a||!a&&e.target!==i&&n)return}i.setAttribute(\"aria-expanded\",n),H(t,!n),R(this.elements.container,this.config.classNames.menu.open,n),n&&S.keyboardEvent(e)?Pe.focusFirstMenuItem.call(this,null,!0):n||s||W.call(this,i,S.keyboardEvent(e))},getMenuSize(e){const t=e.cloneNode(!0);t.style.position=\"absolute\",t.style.opacity=0,t.removeAttribute(\"hidden\"),e.parentNode.appendChild(t);const i=t.scrollWidth,s=t.scrollHeight;return O(t),{width:i,height:s}},showMenuPanel(e=\"\",t=!1){const i=this.elements.container.querySelector(`#plyr-settings-${this.id}-${e}`);if(!S.element(i))return;const s=i.parentNode,n=Array.from(s.children).find((e=>!e.hidden));if(K.transitions&&!K.reducedMotion){s.style.width=`${n.scrollWidth}px`,s.style.height=`${n.scrollHeight}px`;const e=Pe.getMenuSize.call(this,i),t=e=>{e.target===s&&[\"width\",\"height\"].includes(e.propertyName)&&(s.style.width=\"\",s.style.height=\"\",J.call(this,s,E,t))};X.call(this,s,E,t),s.style.width=`${e.width}px`,s.style.height=`${e.height}px`}H(n,!0),H(i,!1),Pe.focusFirstMenuItem.call(this,i,t)},setDownloadUrl(){const e=this.elements.buttons.download;S.element(e)&&e.setAttribute(\"href\",this.download)},create(e){const{bindMenuItemShortcuts:t,createButton:i,createProgress:s,createRange:n,createTime:a,setQualityMenu:l,setSpeedMenu:r,showMenuPanel:o}=Pe;this.elements.controls=null,S.array(this.config.controls)&&this.config.controls.includes(\"play-large\")&&this.elements.container.appendChild(i.call(this,\"play-large\"));const c=$(\"div\",D(this.config.selectors.controls.wrapper));this.elements.controls=c;const u={class:\"plyr__controls__item\"};return se(S.array(this.config.controls)?this.config.controls:[]).forEach((l=>{if(\"restart\"===l&&c.appendChild(i.call(this,\"restart\",u)),\"rewind\"===l&&c.appendChild(i.call(this,\"rewind\",u)),\"play\"===l&&c.appendChild(i.call(this,\"play\",u)),\"fast-forward\"===l&&c.appendChild(i.call(this,\"fast-forward\",u)),\"progress\"===l){const t=$(\"div\",{class:`${u.class} plyr__progress__container`}),i=$(\"div\",D(this.config.selectors.progress));if(i.appendChild(n.call(this,\"seek\",{id:`plyr-seek-${e.id}`})),i.appendChild(s.call(this,\"buffer\")),this.config.tooltips.seek){const e=$(\"span\",{class:this.config.classNames.tooltip},\"00:00\");i.appendChild(e),this.elements.display.seekTooltip=e}this.elements.progress=i,t.appendChild(this.elements.progress),c.appendChild(t)}if(\"current-time\"===l&&c.appendChild(a.call(this,\"currentTime\",u)),\"duration\"===l&&c.appendChild(a.call(this,\"duration\",u)),\"mute\"===l||\"volume\"===l){let{volume:t}=this.elements;if(S.element(t)&&c.contains(t)||(t=$(\"div\",x({},u,{class:`${u.class} plyr__volume`.trim()})),this.elements.volume=t,c.appendChild(t)),\"mute\"===l&&t.appendChild(i.call(this,\"mute\")),\"volume\"===l&&!M.isIos&&!M.isIPadOS){const i={max:1,step:.05,value:this.config.volume};t.appendChild(n.call(this,\"volume\",x(i,{id:`plyr-volume-${e.id}`})))}}if(\"captions\"===l&&c.appendChild(i.call(this,\"captions\",u)),\"settings\"===l&&!S.empty(this.config.settings)){const s=$(\"div\",x({},u,{class:`${u.class} plyr__menu`.trim(),hidden:\"\"}));s.appendChild(i.call(this,\"settings\",{\"aria-haspopup\":!0,\"aria-controls\":`plyr-settings-${e.id}`,\"aria-expanded\":!1}));const n=$(\"div\",{class:\"plyr__menu__container\",id:`plyr-settings-${e.id}`,hidden:\"\"}),a=$(\"div\"),l=$(\"div\",{id:`plyr-settings-${e.id}-home`}),r=$(\"div\",{role:\"menu\"});l.appendChild(r),a.appendChild(l),this.elements.settings.panels.home=l,this.config.settings.forEach((i=>{const s=$(\"button\",x(D(this.config.selectors.buttons.settings),{type:\"button\",class:`${this.config.classNames.control} ${this.config.classNames.control}--forward`,role:\"menuitem\",\"aria-haspopup\":!0,hidden:\"\"}));t.call(this,s,i),X.call(this,s,\"click\",(()=>{o.call(this,i,!1)}));const n=$(\"span\",null,ve.get(i,this.config)),l=$(\"span\",{class:this.config.classNames.menu.value});l.innerHTML=e[i],n.appendChild(l),s.appendChild(n),r.appendChild(s);const c=$(\"div\",{id:`plyr-settings-${e.id}-${i}`,hidden:\"\"}),u=$(\"button\",{type:\"button\",class:`${this.config.classNames.control} ${this.config.classNames.control}--back`});u.appendChild($(\"span\",{\"aria-hidden\":!0},ve.get(i,this.config))),u.appendChild($(\"span\",{class:this.config.classNames.hidden},ve.get(\"menuBack\",this.config))),X.call(this,c,\"keydown\",(e=>{\"ArrowLeft\"===e.key&&(e.preventDefault(),e.stopPropagation(),o.call(this,\"home\",!0))}),!1),X.call(this,u,\"click\",(()=>{o.call(this,\"home\",!1)})),c.appendChild(u),c.appendChild($(\"div\",{role:\"menu\"})),a.appendChild(c),this.elements.settings.buttons[i]=s,this.elements.settings.panels[i]=c})),n.appendChild(a),s.appendChild(n),c.appendChild(s),this.elements.settings.popup=n,this.elements.settings.menu=s}if(\"pip\"===l&&K.pip&&c.appendChild(i.call(this,\"pip\",u)),\"airplay\"===l&&K.airplay&&c.appendChild(i.call(this,\"airplay\",u)),\"download\"===l){const e=x({},u,{element:\"a\",href:this.download,target:\"_blank\"});this.isHTML5&&(e.download=\"\");const{download:t}=this.config.urls;!S.url(t)&&this.isEmbed&&x(e,{icon:`logo-${this.provider}`,label:this.provider}),c.appendChild(i.call(this,\"download\",e))}\"fullscreen\"===l&&c.appendChild(i.call(this,\"fullscreen\",u))})),this.isHTML5&&l.call(this,de.getQualityOptions.call(this)),r.call(this),c},inject(){if(this.config.loadSprite){const e=Pe.getIconUrl.call(this);e.cors&&ke(e.url,\"sprite-plyr\")}this.id=Math.floor(1e4*Math.random());let e=null;this.elements.controls=null;const t={id:this.id,seektime:this.config.seekTime,title:this.config.title};let i=!0;S.function(this.config.controls)&&(this.config.controls=this.config.controls.call(this,t)),this.config.controls||(this.config.controls=[]),S.element(this.config.controls)||S.string(this.config.controls)?e=this.config.controls:(e=Pe.create.call(this,{id:this.id,seektime:this.config.seekTime,speed:this.speed,quality:this.quality,captions:xe.getLabel.call(this)}),i=!1);let s;i&&S.string(this.config.controls)&&(e=(e=>{let i=e;return Object.entries(t).forEach((([e,t])=>{i=pe(i,`{${e}}`,t)})),i})(e)),S.string(this.config.selectors.controls.container)&&(s=document.querySelector(this.config.selectors.controls.container)),S.element(s)||(s=this.elements.container);if(s[S.element(e)?\"insertAdjacentElement\":\"insertAdjacentHTML\"](\"afterbegin\",e),S.element(this.elements.controls)||Pe.findElements.call(this),!S.empty(this.elements.buttons)){const e=e=>{const t=this.config.classNames.controlPressed;e.setAttribute(\"aria-pressed\",\"false\"),Object.defineProperty(e,\"pressed\",{configurable:!0,enumerable:!0,get:()=>F(e,t),set(i=!1){R(e,t,i),e.setAttribute(\"aria-pressed\",i?\"true\":\"false\")}})};Object.values(this.elements.buttons).filter(Boolean).forEach((t=>{S.array(t)||S.nodeList(t)?Array.from(t).filter(Boolean).forEach(e):e(t)}))}if(M.isEdge&&P(s),this.config.tooltips.controls){const{classNames:e,selectors:t}=this.config,i=`${t.controls.wrapper} ${t.labels} .${e.hidden}`,s=U.call(this,i);Array.from(s).forEach((e=>{R(e,this.config.classNames.hidden,!1),R(e,this.config.classNames.tooltip,!0)}))}},setMediaMetadata(){try{\"mediaSession\"in navigator&&(navigator.mediaSession.metadata=new window.MediaMetadata({title:this.config.mediaMetadata.title,artist:this.config.mediaMetadata.artist,album:this.config.mediaMetadata.album,artwork:this.config.mediaMetadata.artwork}))}catch(e){}},setMarkers(){var e,t;if(!this.duration||this.elements.markers)return;const i=null===(e=this.config.markers)||void 0===e||null===(t=e.points)||void 0===t?void 0:t.filter((({time:e})=>e>0&&e<this.duration));if(null==i||!i.length)return;const s=document.createDocumentFragment(),n=document.createDocumentFragment();let a=null;const l=`${this.config.classNames.tooltip}--visible`,r=e=>R(a,l,e);i.forEach((e=>{const t=$(\"span\",{class:this.config.classNames.marker},\"\"),i=e.time/this.duration*100+\"%\";a&&(t.addEventListener(\"mouseenter\",(()=>{e.label||(a.style.left=i,a.innerHTML=e.label,r(!0))})),t.addEventListener(\"mouseleave\",(()=>{r(!1)}))),t.addEventListener(\"click\",(()=>{this.currentTime=e.time})),t.style.left=i,n.appendChild(t)})),s.appendChild(n),this.config.tooltips.seek||(a=$(\"span\",{class:this.config.classNames.tooltip},\"\"),s.appendChild(a)),this.elements.markers={points:n,tip:a},this.elements.progress.appendChild(s)}};function Me(e,t=!0){let i=e;if(t){const e=document.createElement(\"a\");e.href=i,i=e.href}try{return new URL(i)}catch(e){return null}}function Ne(e){const t=new URLSearchParams;return S.object(e)&&Object.entries(e).forEach((([e,i])=>{t.set(e,i)})),t}const xe={setup(){if(!this.supported.ui)return;if(!this.isVideo||this.isYouTube||this.isHTML5&&!K.textTracks)return void(S.array(this.config.controls)&&this.config.controls.includes(\"settings\")&&this.config.settings.includes(\"captions\")&&Pe.setCaptionsMenu.call(this));var e,t;if(S.element(this.elements.captions)||(this.elements.captions=$(\"div\",D(this.config.selectors.captions)),this.elements.captions.setAttribute(\"dir\",\"auto\"),e=this.elements.captions,t=this.elements.wrapper,S.element(e)&&S.element(t)&&t.parentNode.insertBefore(e,t.nextSibling)),M.isIE&&window.URL){const e=this.media.querySelectorAll(\"track\");Array.from(e).forEach((e=>{const t=e.getAttribute(\"src\"),i=Me(t);null!==i&&i.hostname!==window.location.href.hostname&&[\"http:\",\"https:\"].includes(i.protocol)&&Te(t,\"blob\").then((t=>{e.setAttribute(\"src\",window.URL.createObjectURL(t))})).catch((()=>{O(e)}))}))}const i=se((navigator.languages||[navigator.language||navigator.userLanguage||\"en\"]).map((e=>e.split(\"-\")[0])));let s=(this.storage.get(\"language\")||this.config.captions.language||\"auto\").toLowerCase();\"auto\"===s&&([s]=i);let n=this.storage.get(\"captions\");if(S.boolean(n)||({active:n}=this.config.captions),Object.assign(this.captions,{toggled:!1,active:n,language:s,languages:i}),this.isHTML5){const e=this.config.captions.update?\"addtrack removetrack\":\"removetrack\";X.call(this,this.media.textTracks,e,xe.update.bind(this))}setTimeout(xe.update.bind(this),0)},update(){const e=xe.getTracks.call(this,!0),{active:t,language:i,meta:s,currentTrackNode:n}=this.captions,a=Boolean(e.find((e=>e.language===i)));this.isHTML5&&this.isVideo&&e.filter((e=>!s.get(e))).forEach((e=>{this.debug.log(\"Track added\",e),s.set(e,{default:\"showing\"===e.mode}),\"showing\"===e.mode&&(e.mode=\"hidden\"),X.call(this,e,\"cuechange\",(()=>xe.updateCues.call(this)))})),(a&&this.language!==i||!e.includes(n))&&(xe.setLanguage.call(this,i),xe.toggle.call(this,t&&a)),this.elements&&R(this.elements.container,this.config.classNames.captions.enabled,!S.empty(e)),S.array(this.config.controls)&&this.config.controls.includes(\"settings\")&&this.config.settings.includes(\"captions\")&&Pe.setCaptionsMenu.call(this)},toggle(e,t=!0){if(!this.supported.ui)return;const{toggled:i}=this.captions,s=this.config.classNames.captions.active,n=S.nullOrUndefined(e)?!i:e;if(n!==i){if(t||(this.captions.active=n,this.storage.set({captions:n})),!this.language&&n&&!t){const e=xe.getTracks.call(this),t=xe.findTrack.call(this,[this.captions.language,...this.captions.languages],!0);return this.captions.language=t.language,void xe.set.call(this,e.indexOf(t))}this.elements.buttons.captions&&(this.elements.buttons.captions.pressed=n),R(this.elements.container,s,n),this.captions.toggled=n,Pe.updateSetting.call(this,\"captions\"),Z.call(this,this.media,n?\"captionsenabled\":\"captionsdisabled\")}setTimeout((()=>{n&&this.captions.toggled&&(this.captions.currentTrackNode.mode=\"hidden\")}))},set(e,t=!0){const i=xe.getTracks.call(this);if(-1!==e)if(S.number(e))if(e in i){if(this.captions.currentTrack!==e){this.captions.currentTrack=e;const s=i[e],{language:n}=s||{};this.captions.currentTrackNode=s,Pe.updateSetting.call(this,\"captions\"),t||(this.captions.language=n,this.storage.set({language:n})),this.isVimeo&&this.embed.enableTextTrack(n),Z.call(this,this.media,\"languagechange\")}xe.toggle.call(this,!0,t),this.isHTML5&&this.isVideo&&xe.updateCues.call(this)}else this.debug.warn(\"Track not found\",e);else this.debug.warn(\"Invalid caption argument\",e);else xe.toggle.call(this,!1,t)},setLanguage(e,t=!0){if(!S.string(e))return void this.debug.warn(\"Invalid language argument\",e);const i=e.toLowerCase();this.captions.language=i;const s=xe.getTracks.call(this),n=xe.findTrack.call(this,[i]);xe.set.call(this,s.indexOf(n),t)},getTracks(e=!1){return Array.from((this.media||{}).textTracks||[]).filter((t=>!this.isHTML5||e||this.captions.meta.has(t))).filter((e=>[\"captions\",\"subtitles\"].includes(e.kind)))},findTrack(e,t=!1){const i=xe.getTracks.call(this),s=e=>Number((this.captions.meta.get(e)||{}).default),n=Array.from(i).sort(((e,t)=>s(t)-s(e)));let a;return e.every((e=>(a=n.find((t=>t.language===e)),!a))),a||(t?n[0]:void 0)},getCurrentTrack(){return xe.getTracks.call(this)[this.currentTrack]},getLabel(e){let t=e;return!S.track(t)&&K.textTracks&&this.captions.toggled&&(t=xe.getCurrentTrack.call(this)),S.track(t)?S.empty(t.label)?S.empty(t.language)?ve.get(\"enabled\",this.config):e.language.toUpperCase():t.label:ve.get(\"disabled\",this.config)},updateCues(e){if(!this.supported.ui)return;if(!S.element(this.elements.captions))return void this.debug.warn(\"No captions element to render to\");if(!S.nullOrUndefined(e)&&!Array.isArray(e))return void this.debug.warn(\"updateCues: Invalid input\",e);let t=e;if(!t){const e=xe.getCurrentTrack.call(this);t=Array.from((e||{}).activeCues||[]).map((e=>e.getCueAsHTML())).map(ye)}const i=t.map((e=>e.trim())).join(\"\\n\");if(i!==this.elements.captions.innerHTML){j(this.elements.captions);const e=$(\"span\",D(this.config.selectors.caption));e.innerHTML=i,this.elements.captions.appendChild(e),Z.call(this,this.media,\"cuechange\")}}},Le={enabled:!0,title:\"\",debug:!1,autoplay:!1,autopause:!0,playsinline:!0,seekTime:10,volume:1,muted:!1,duration:null,displayDuration:!0,invertTime:!0,toggleInvert:!0,ratio:null,clickToPlay:!0,hideControls:!0,resetOnEnd:!1,disableContextMenu:!0,loadSprite:!0,iconPrefix:\"plyr\",iconUrl:\"https://cdn.plyr.io/3.7.8/plyr.svg\",blankVideo:\"https://cdn.plyr.io/static/blank.mp4\",quality:{default:576,options:[4320,2880,2160,1440,1080,720,576,480,360,240],forced:!1,onChange:null},loop:{active:!1},speed:{selected:1,options:[.5,.75,1,1.25,1.5,1.75,2,4]},keyboard:{focused:!0,global:!1},tooltips:{controls:!1,seek:!0},captions:{active:!1,language:\"auto\",update:!1},fullscreen:{enabled:!0,fallback:!0,iosNative:!1},storage:{enabled:!0,key:\"plyr\"},controls:[\"play-large\",\"play\",\"progress\",\"current-time\",\"mute\",\"volume\",\"captions\",\"settings\",\"pip\",\"airplay\",\"fullscreen\"],settings:[\"captions\",\"quality\",\"speed\"],i18n:{restart:\"Restart\",rewind:\"Rewind {seektime}s\",play:\"Play\",pause:\"Pause\",fastForward:\"Forward {seektime}s\",seek:\"Seek\",seekLabel:\"{currentTime} of {duration}\",played:\"Played\",buffered:\"Buffered\",currentTime:\"Current time\",duration:\"Duration\",volume:\"Volume\",mute:\"Mute\",unmute:\"Unmute\",enableCaptions:\"Enable captions\",disableCaptions:\"Disable captions\",download:\"Download\",enterFullscreen:\"Enter fullscreen\",exitFullscreen:\"Exit fullscreen\",frameTitle:\"Player for {title}\",captions:\"Captions\",settings:\"Settings\",pip:\"PIP\",menuBack:\"Go back to previous menu\",speed:\"Speed\",normal:\"Normal\",quality:\"Quality\",loop:\"Loop\",start:\"Start\",end:\"End\",all:\"All\",reset:\"Reset\",disabled:\"Disabled\",enabled:\"Enabled\",advertisement:\"Ad\",qualityBadge:{2160:\"4K\",1440:\"HD\",1080:\"HD\",720:\"HD\",576:\"SD\",480:\"SD\"}},urls:{download:null,vimeo:{sdk:\"https://player.vimeo.com/api/player.js\",iframe:\"https://player.vimeo.com/video/{0}?{1}\",api:\"https://vimeo.com/api/oembed.json?url={0}\"},youtube:{sdk:\"https://www.youtube.com/iframe_api\",api:\"https://noembed.com/embed?url=https://www.youtube.com/watch?v={0}\"},googleIMA:{sdk:\"https://imasdk.googleapis.com/js/sdkloader/ima3.js\"}},listeners:{seek:null,play:null,pause:null,restart:null,rewind:null,fastForward:null,mute:null,volume:null,captions:null,download:null,fullscreen:null,pip:null,airplay:null,speed:null,quality:null,loop:null,language:null},events:[\"ended\",\"progress\",\"stalled\",\"playing\",\"waiting\",\"canplay\",\"canplaythrough\",\"loadstart\",\"loadeddata\",\"loadedmetadata\",\"timeupdate\",\"volumechange\",\"play\",\"pause\",\"error\",\"seeking\",\"seeked\",\"emptied\",\"ratechange\",\"cuechange\",\"download\",\"enterfullscreen\",\"exitfullscreen\",\"captionsenabled\",\"captionsdisabled\",\"languagechange\",\"controlshidden\",\"controlsshown\",\"ready\",\"statechange\",\"qualitychange\",\"adsloaded\",\"adscontentpause\",\"adscontentresume\",\"adstarted\",\"adsmidpoint\",\"adscomplete\",\"adsallcomplete\",\"adsimpression\",\"adsclick\"],selectors:{editable:\"input, textarea, select, [contenteditable]\",container:\".plyr\",controls:{container:null,wrapper:\".plyr__controls\"},labels:\"[data-plyr]\",buttons:{play:'[data-plyr=\"play\"]',pause:'[data-plyr=\"pause\"]',restart:'[data-plyr=\"restart\"]',rewind:'[data-plyr=\"rewind\"]',fastForward:'[data-plyr=\"fast-forward\"]',mute:'[data-plyr=\"mute\"]',captions:'[data-plyr=\"captions\"]',download:'[data-plyr=\"download\"]',fullscreen:'[data-plyr=\"fullscreen\"]',pip:'[data-plyr=\"pip\"]',airplay:'[data-plyr=\"airplay\"]',settings:'[data-plyr=\"settings\"]',loop:'[data-plyr=\"loop\"]'},inputs:{seek:'[data-plyr=\"seek\"]',volume:'[data-plyr=\"volume\"]',speed:'[data-plyr=\"speed\"]',language:'[data-plyr=\"language\"]',quality:'[data-plyr=\"quality\"]'},display:{currentTime:\".plyr__time--current\",duration:\".plyr__time--duration\",buffer:\".plyr__progress__buffer\",loop:\".plyr__progress__loop\",volume:\".plyr__volume--display\"},progress:\".plyr__progress\",captions:\".plyr__captions\",caption:\".plyr__caption\"},classNames:{type:\"plyr--{0}\",provider:\"plyr--{0}\",video:\"plyr__video-wrapper\",embed:\"plyr__video-embed\",videoFixedRatio:\"plyr__video-wrapper--fixed-ratio\",embedContainer:\"plyr__video-embed__container\",poster:\"plyr__poster\",posterEnabled:\"plyr__poster-enabled\",ads:\"plyr__ads\",control:\"plyr__control\",controlPressed:\"plyr__control--pressed\",playing:\"plyr--playing\",paused:\"plyr--paused\",stopped:\"plyr--stopped\",loading:\"plyr--loading\",hover:\"plyr--hover\",tooltip:\"plyr__tooltip\",cues:\"plyr__cues\",marker:\"plyr__progress__marker\",hidden:\"plyr__sr-only\",hideControls:\"plyr--hide-controls\",isTouch:\"plyr--is-touch\",uiSupported:\"plyr--full-ui\",noTransition:\"plyr--no-transition\",display:{time:\"plyr__time\"},menu:{value:\"plyr__menu__value\",badge:\"plyr__badge\",open:\"plyr--menu-open\"},captions:{enabled:\"plyr--captions-enabled\",active:\"plyr--captions-active\"},fullscreen:{enabled:\"plyr--fullscreen-enabled\",fallback:\"plyr--fullscreen-fallback\"},pip:{supported:\"plyr--pip-supported\",active:\"plyr--pip-active\"},airplay:{supported:\"plyr--airplay-supported\",active:\"plyr--airplay-active\"},previewThumbnails:{thumbContainer:\"plyr__preview-thumb\",thumbContainerShown:\"plyr__preview-thumb--is-shown\",imageContainer:\"plyr__preview-thumb__image-container\",timeContainer:\"plyr__preview-thumb__time-container\",scrubbingContainer:\"plyr__preview-scrubbing\",scrubbingContainerShown:\"plyr__preview-scrubbing--is-shown\"}},attributes:{embed:{provider:\"data-plyr-provider\",id:\"data-plyr-embed-id\",hash:\"data-plyr-embed-hash\"}},ads:{enabled:!1,publisherId:\"\",tagUrl:\"\"},previewThumbnails:{enabled:!1,src:\"\"},vimeo:{byline:!1,portrait:!1,title:!1,speed:!0,transparent:!1,customControls:!0,referrerPolicy:null,premium:!1},youtube:{rel:0,showinfo:0,iv_load_policy:3,modestbranding:1,customControls:!0,noCookie:!1},mediaMetadata:{title:\"\",artist:\"\",album:\"\",artwork:[]},markers:{enabled:!1,points:[]}},Ie=\"picture-in-picture\",$e=\"inline\",_e={html5:\"html5\",youtube:\"youtube\",vimeo:\"vimeo\"},Oe=\"audio\",je=\"video\";const qe=()=>{};class De{constructor(e=!1){this.enabled=window.console&&e,this.enabled&&this.log(\"Debugging enabled\")}get log(){return this.enabled?Function.prototype.bind.call(console.log,console):qe}get warn(){return this.enabled?Function.prototype.bind.call(console.warn,console):qe}get error(){return this.enabled?Function.prototype.bind.call(console.error,console):qe}}class He{constructor(t){e(this,\"onChange\",(()=>{if(!this.supported)return;const e=this.player.elements.buttons.fullscreen;S.element(e)&&(e.pressed=this.active);const t=this.target===this.player.media?this.target:this.player.elements.container;Z.call(this.player,t,this.active?\"enterfullscreen\":\"exitfullscreen\",!0)})),e(this,\"toggleFallback\",((e=!1)=>{if(e?this.scrollPosition={x:window.scrollX??0,y:window.scrollY??0}:window.scrollTo(this.scrollPosition.x,this.scrollPosition.y),document.body.style.overflow=e?\"hidden\":\"\",R(this.target,this.player.config.classNames.fullscreen.fallback,e),M.isIos){let t=document.head.querySelector('meta[name=\"viewport\"]');const i=\"viewport-fit=cover\";t||(t=document.createElement(\"meta\"),t.setAttribute(\"name\",\"viewport\"));const s=S.string(t.content)&&t.content.includes(i);e?(this.cleanupViewport=!s,s||(t.content+=`,${i}`)):this.cleanupViewport&&(t.content=t.content.split(\",\").filter((e=>e.trim()!==i)).join(\",\"))}this.onChange()})),e(this,\"trapFocus\",(e=>{if(M.isIos||M.isIPadOS||!this.active||\"Tab\"!==e.key)return;const t=document.activeElement,i=U.call(this.player,\"a[href], button:not(:disabled), input:not(:disabled), [tabindex]\"),[s]=i,n=i[i.length-1];t!==n||e.shiftKey?t===s&&e.shiftKey&&(n.focus(),e.preventDefault()):(s.focus(),e.preventDefault())})),e(this,\"update\",(()=>{if(this.supported){let e;e=this.forceFallback?\"Fallback (forced)\":He.nativeSupported?\"Native\":\"Fallback\",this.player.debug.log(`${e} fullscreen enabled`)}else this.player.debug.log(\"Fullscreen not supported and fallback disabled\");R(this.player.elements.container,this.player.config.classNames.fullscreen.enabled,this.supported)})),e(this,\"enter\",(()=>{this.supported&&(M.isIos&&this.player.config.fullscreen.iosNative?this.player.isVimeo?this.player.embed.requestFullscreen():this.target.webkitEnterFullscreen():!He.nativeSupported||this.forceFallback?this.toggleFallback(!0):this.prefix?S.empty(this.prefix)||this.target[`${this.prefix}Request${this.property}`]():this.target.requestFullscreen({navigationUI:\"hide\"}))})),e(this,\"exit\",(()=>{if(this.supported)if(M.isIos&&this.player.config.fullscreen.iosNative)this.player.isVimeo?this.player.embed.exitFullscreen():this.target.webkitEnterFullscreen(),ie(this.player.play());else if(!He.nativeSupported||this.forceFallback)this.toggleFallback(!1);else if(this.prefix){if(!S.empty(this.prefix)){const e=\"moz\"===this.prefix?\"Cancel\":\"Exit\";document[`${this.prefix}${e}${this.property}`]()}}else(document.cancelFullScreen||document.exitFullscreen).call(document)})),e(this,\"toggle\",(()=>{this.active?this.exit():this.enter()})),this.player=t,this.prefix=He.prefix,this.property=He.property,this.scrollPosition={x:0,y:0},this.forceFallback=\"force\"===t.config.fullscreen.fallback,this.player.elements.fullscreen=t.config.fullscreen.container&&function(e,t){const{prototype:i}=Element;return(i.closest||function(){let e=this;do{if(V.matches(e,t))return e;e=e.parentElement||e.parentNode}while(null!==e&&1===e.nodeType);return null}).call(e,t)}(this.player.elements.container,t.config.fullscreen.container),X.call(this.player,document,\"ms\"===this.prefix?\"MSFullscreenChange\":`${this.prefix}fullscreenchange`,(()=>{this.onChange()})),X.call(this.player,this.player.elements.container,\"dblclick\",(e=>{S.element(this.player.elements.controls)&&this.player.elements.controls.contains(e.target)||this.player.listeners.proxy(e,this.toggle,\"fullscreen\")})),X.call(this,this.player.elements.container,\"keydown\",(e=>this.trapFocus(e))),this.update()}static get nativeSupported(){return!!(document.fullscreenEnabled||document.webkitFullscreenEnabled||document.mozFullScreenEnabled||document.msFullscreenEnabled)}get useNative(){return He.nativeSupported&&!this.forceFallback}static get prefix(){if(S.function(document.exitFullscreen))return\"\";let e=\"\";return[\"webkit\",\"moz\",\"ms\"].some((t=>!(!S.function(document[`${t}ExitFullscreen`])&&!S.function(document[`${t}CancelFullScreen`]))&&(e=t,!0))),e}static get property(){return\"moz\"===this.prefix?\"FullScreen\":\"Fullscreen\"}get supported(){return[this.player.config.fullscreen.enabled,this.player.isVideo,He.nativeSupported||this.player.config.fullscreen.fallback,!this.player.isYouTube||He.nativeSupported||!M.isIos||this.player.config.playsinline&&!this.player.config.fullscreen.iosNative].every(Boolean)}get active(){if(!this.supported)return!1;if(!He.nativeSupported||this.forceFallback)return F(this.target,this.player.config.classNames.fullscreen.fallback);const e=this.prefix?this.target.getRootNode()[`${this.prefix}${this.property}Element`]:this.target.getRootNode().fullscreenElement;return e&&e.shadowRoot?e===this.target.getRootNode().host:e===this.target}get target(){return M.isIos&&this.player.config.fullscreen.iosNative?this.player.media:this.player.elements.fullscreen??this.player.elements.container}}function Re(e,t=1){return new Promise(((i,s)=>{const n=new Image,a=()=>{delete n.onload,delete n.onerror,(n.naturalWidth>=t?i:s)(n)};Object.assign(n,{onload:a,onerror:a,src:e})}))}const Fe={addStyleHook(){R(this.elements.container,this.config.selectors.container.replace(\".\",\"\"),!0),R(this.elements.container,this.config.classNames.uiSupported,this.supported.ui)},toggleNativeControls(e=!1){e&&this.isHTML5?this.media.setAttribute(\"controls\",\"\"):this.media.removeAttribute(\"controls\")},build(){if(this.listeners.media(),!this.supported.ui)return this.debug.warn(`Basic support only for ${this.provider} ${this.type}`),void Fe.toggleNativeControls.call(this,!0);S.element(this.elements.controls)||(Pe.inject.call(this),this.listeners.controls()),Fe.toggleNativeControls.call(this),this.isHTML5&&xe.setup.call(this),this.volume=null,this.muted=null,this.loop=null,this.quality=null,this.speed=null,Pe.updateVolume.call(this),Pe.timeUpdate.call(this),Pe.durationUpdate.call(this),Fe.checkPlaying.call(this),R(this.elements.container,this.config.classNames.pip.supported,K.pip&&this.isHTML5&&this.isVideo),R(this.elements.container,this.config.classNames.airplay.supported,K.airplay&&this.isHTML5),R(this.elements.container,this.config.classNames.isTouch,this.touch),this.ready=!0,setTimeout((()=>{Z.call(this,this.media,\"ready\")}),0),Fe.setTitle.call(this),this.poster&&Fe.setPoster.call(this,this.poster,!1).catch((()=>{})),this.config.duration&&Pe.durationUpdate.call(this),this.config.mediaMetadata&&Pe.setMediaMetadata.call(this)},setTitle(){let e=ve.get(\"play\",this.config);if(S.string(this.config.title)&&!S.empty(this.config.title)&&(e+=`, ${this.config.title}`),Array.from(this.elements.buttons.play||[]).forEach((t=>{t.setAttribute(\"aria-label\",e)})),this.isEmbed){const e=B.call(this,\"iframe\");if(!S.element(e))return;const t=S.empty(this.config.title)?\"video\":this.config.title,i=ve.get(\"frameTitle\",this.config);e.setAttribute(\"title\",i.replace(\"{title}\",t))}},togglePoster(e){R(this.elements.container,this.config.classNames.posterEnabled,e)},setPoster(e,t=!0){return t&&this.poster?Promise.reject(new Error(\"Poster already set\")):(this.media.setAttribute(\"data-poster\",e),this.elements.poster.removeAttribute(\"hidden\"),te.call(this).then((()=>Re(e))).catch((t=>{throw e===this.poster&&Fe.togglePoster.call(this,!1),t})).then((()=>{if(e!==this.poster)throw new Error(\"setPoster cancelled by later call to setPoster\")})).then((()=>(Object.assign(this.elements.poster.style,{backgroundImage:`url('${e}')`,backgroundSize:\"\"}),Fe.togglePoster.call(this,!0),e))))},checkPlaying(e){R(this.elements.container,this.config.classNames.playing,this.playing),R(this.elements.container,this.config.classNames.paused,this.paused),R(this.elements.container,this.config.classNames.stopped,this.stopped),Array.from(this.elements.buttons.play||[]).forEach((e=>{Object.assign(e,{pressed:this.playing}),e.setAttribute(\"aria-label\",ve.get(this.playing?\"pause\":\"play\",this.config))})),S.event(e)&&\"timeupdate\"===e.type||Fe.toggleControls.call(this)},checkLoading(e){this.loading=[\"stalled\",\"waiting\"].includes(e.type),clearTimeout(this.timers.loading),this.timers.loading=setTimeout((()=>{R(this.elements.container,this.config.classNames.loading,this.loading),Fe.toggleControls.call(this)}),this.loading?250:0)},toggleControls(e){const{controls:t}=this.elements;if(t&&this.config.hideControls){const i=this.touch&&this.lastSeekTime+2e3>Date.now();this.toggleControls(Boolean(e||this.loading||this.paused||t.pressed||t.hover||i))}},migrateStyles(){Object.values({...this.media.style}).filter((e=>!S.empty(e)&&S.string(e)&&e.startsWith(\"--plyr\"))).forEach((e=>{this.elements.container.style.setProperty(e,this.media.style.getPropertyValue(e)),this.media.style.removeProperty(e)})),S.empty(this.media.style)&&this.media.removeAttribute(\"style\")}};class Ve{constructor(t){e(this,\"firstTouch\",(()=>{const{player:e}=this,{elements:t}=e;e.touch=!0,R(t.container,e.config.classNames.isTouch,!0)})),e(this,\"global\",((e=!0)=>{const{player:t}=this;t.config.keyboard.global&&Q.call(t,window,\"keydown keyup\",this.handleKey,e,!1),Q.call(t,document.body,\"click\",this.toggleMenu,e),G.call(t,document.body,\"touchstart\",this.firstTouch)})),e(this,\"container\",(()=>{const{player:e}=this,{config:t,elements:i,timers:s}=e;!t.keyboard.global&&t.keyboard.focused&&X.call(e,i.container,\"keydown keyup\",this.handleKey,!1),X.call(e,i.container,\"mousemove mouseleave touchstart touchmove enterfullscreen exitfullscreen\",(t=>{const{controls:n}=i;n&&\"enterfullscreen\"===t.type&&(n.pressed=!1,n.hover=!1);let a=0;[\"touchstart\",\"touchmove\",\"mousemove\"].includes(t.type)&&(Fe.toggleControls.call(e,!0),a=e.touch?3e3:2e3),clearTimeout(s.controls),s.controls=setTimeout((()=>Fe.toggleControls.call(e,!1)),a)}));const n=()=>{if(!e.isVimeo||e.config.vimeo.premium)return;const t=i.wrapper,{active:s}=e.fullscreen,[n,a]=ce.call(e),l=ae(`aspect-ratio: ${n} / ${a}`);if(!s)return void(l?(t.style.width=null,t.style.height=null):(t.style.maxWidth=null,t.style.margin=null));const[r,o]=[Math.max(document.documentElement.clientWidth||0,window.innerWidth||0),Math.max(document.documentElement.clientHeight||0,window.innerHeight||0)],c=r/o>n/a;l?(t.style.width=c?\"auto\":\"100%\",t.style.height=c?\"100%\":\"auto\"):(t.style.maxWidth=c?o/a*n+\"px\":null,t.style.margin=c?\"0 auto\":null)},a=()=>{clearTimeout(s.resized),s.resized=setTimeout(n,50)};X.call(e,i.container,\"enterfullscreen exitfullscreen\",(t=>{const{target:s}=e.fullscreen;if(s!==i.container)return;if(!e.isEmbed&&S.empty(e.config.ratio))return;n();(\"enterfullscreen\"===t.type?X:J).call(e,window,\"resize\",a)}))})),e(this,\"media\",(()=>{const{player:e}=this,{elements:t}=e;if(X.call(e,e.media,\"timeupdate seeking seeked\",(t=>Pe.timeUpdate.call(e,t))),X.call(e,e.media,\"durationchange loadeddata loadedmetadata\",(t=>Pe.durationUpdate.call(e,t))),X.call(e,e.media,\"ended\",(()=>{e.isHTML5&&e.isVideo&&e.config.resetOnEnd&&(e.restart(),e.pause())})),X.call(e,e.media,\"progress playing seeking seeked\",(t=>Pe.updateProgress.call(e,t))),X.call(e,e.media,\"volumechange\",(t=>Pe.updateVolume.call(e,t))),X.call(e,e.media,\"playing play pause ended emptied timeupdate\",(t=>Fe.checkPlaying.call(e,t))),X.call(e,e.media,\"waiting canplay seeked playing\",(t=>Fe.checkLoading.call(e,t))),e.supported.ui&&e.config.clickToPlay&&!e.isAudio){const i=B.call(e,`.${e.config.classNames.video}`);if(!S.element(i))return;X.call(e,t.container,\"click\",(s=>{([t.container,i].includes(s.target)||i.contains(s.target))&&(e.touch&&e.config.hideControls||(e.ended?(this.proxy(s,e.restart,\"restart\"),this.proxy(s,(()=>{ie(e.play())}),\"play\")):this.proxy(s,(()=>{ie(e.togglePlay())}),\"play\")))}))}e.supported.ui&&e.config.disableContextMenu&&X.call(e,t.wrapper,\"contextmenu\",(e=>{e.preventDefault()}),!1),X.call(e,e.media,\"volumechange\",(()=>{e.storage.set({volume:e.volume,muted:e.muted})})),X.call(e,e.media,\"ratechange\",(()=>{Pe.updateSetting.call(e,\"speed\"),e.storage.set({speed:e.speed})})),X.call(e,e.media,\"qualitychange\",(t=>{Pe.updateSetting.call(e,\"quality\",null,t.detail.quality)})),X.call(e,e.media,\"ready qualitychange\",(()=>{Pe.setDownloadUrl.call(e)}));const i=e.config.events.concat([\"keyup\",\"keydown\"]).join(\" \");X.call(e,e.media,i,(i=>{let{detail:s={}}=i;\"error\"===i.type&&(s=e.media.error),Z.call(e,t.container,i.type,!0,s)}))})),e(this,\"proxy\",((e,t,i)=>{const{player:s}=this,n=s.config.listeners[i];let a=!0;S.function(n)&&(a=n.call(s,e)),!1!==a&&S.function(t)&&t.call(s,e)})),e(this,\"bind\",((e,t,i,s,n=!0)=>{const{player:a}=this,l=a.config.listeners[s],r=S.function(l);X.call(a,e,t,(e=>this.proxy(e,i,s)),n&&!r)})),e(this,\"controls\",(()=>{const{player:e}=this,{elements:t}=e,i=M.isIE?\"change\":\"input\";if(t.buttons.play&&Array.from(t.buttons.play).forEach((t=>{this.bind(t,\"click\",(()=>{ie(e.togglePlay())}),\"play\")})),this.bind(t.buttons.restart,\"click\",e.restart,\"restart\"),this.bind(t.buttons.rewind,\"click\",(()=>{e.lastSeekTime=Date.now(),e.rewind()}),\"rewind\"),this.bind(t.buttons.fastForward,\"click\",(()=>{e.lastSeekTime=Date.now(),e.forward()}),\"fastForward\"),this.bind(t.buttons.mute,\"click\",(()=>{e.muted=!e.muted}),\"mute\"),this.bind(t.buttons.captions,\"click\",(()=>e.toggleCaptions())),this.bind(t.buttons.download,\"click\",(()=>{Z.call(e,e.media,\"download\")}),\"download\"),this.bind(t.buttons.fullscreen,\"click\",(()=>{e.fullscreen.toggle()}),\"fullscreen\"),this.bind(t.buttons.pip,\"click\",(()=>{e.pip=\"toggle\"}),\"pip\"),this.bind(t.buttons.airplay,\"click\",e.airplay,\"airplay\"),this.bind(t.buttons.settings,\"click\",(t=>{t.stopPropagation(),t.preventDefault(),Pe.toggleMenu.call(e,t)}),null,!1),this.bind(t.buttons.settings,\"keyup\",(t=>{[\" \",\"Enter\"].includes(t.key)&&(\"Enter\"!==t.key?(t.preventDefault(),t.stopPropagation(),Pe.toggleMenu.call(e,t)):Pe.focusFirstMenuItem.call(e,null,!0))}),null,!1),this.bind(t.settings.menu,\"keydown\",(t=>{\"Escape\"===t.key&&Pe.toggleMenu.call(e,t)})),this.bind(t.inputs.seek,\"mousedown mousemove\",(e=>{const i=t.progress.getBoundingClientRect(),s=100/i.width*(e.pageX-i.left);e.currentTarget.setAttribute(\"seek-value\",s)})),this.bind(t.inputs.seek,\"mousedown mouseup keydown keyup touchstart touchend\",(t=>{const i=t.currentTarget,s=\"play-on-seeked\";if(S.keyboardEvent(t)&&![\"ArrowLeft\",\"ArrowRight\"].includes(t.key))return;e.lastSeekTime=Date.now();const n=i.hasAttribute(s),a=[\"mouseup\",\"touchend\",\"keyup\"].includes(t.type);n&&a?(i.removeAttribute(s),ie(e.play())):!a&&e.playing&&(i.setAttribute(s,\"\"),e.pause())})),M.isIos){const t=U.call(e,'input[type=\"range\"]');Array.from(t).forEach((e=>this.bind(e,i,(e=>P(e.target)))))}this.bind(t.inputs.seek,i,(t=>{const i=t.currentTarget;let s=i.getAttribute(\"seek-value\");S.empty(s)&&(s=i.value),i.removeAttribute(\"seek-value\"),e.currentTime=s/i.max*e.duration}),\"seek\"),this.bind(t.progress,\"mouseenter mouseleave mousemove\",(t=>Pe.updateSeekTooltip.call(e,t))),this.bind(t.progress,\"mousemove touchmove\",(t=>{const{previewThumbnails:i}=e;i&&i.loaded&&i.startMove(t)})),this.bind(t.progress,\"mouseleave touchend click\",(()=>{const{previewThumbnails:t}=e;t&&t.loaded&&t.endMove(!1,!0)})),this.bind(t.progress,\"mousedown touchstart\",(t=>{const{previewThumbnails:i}=e;i&&i.loaded&&i.startScrubbing(t)})),this.bind(t.progress,\"mouseup touchend\",(t=>{const{previewThumbnails:i}=e;i&&i.loaded&&i.endScrubbing(t)})),M.isWebKit&&Array.from(U.call(e,'input[type=\"range\"]')).forEach((t=>{this.bind(t,\"input\",(t=>Pe.updateRangeFill.call(e,t.target)))})),e.config.toggleInvert&&!S.element(t.display.duration)&&this.bind(t.display.currentTime,\"click\",(()=>{0!==e.currentTime&&(e.config.invertTime=!e.config.invertTime,Pe.timeUpdate.call(e))})),this.bind(t.inputs.volume,i,(t=>{e.volume=t.target.value}),\"volume\"),this.bind(t.controls,\"mouseenter mouseleave\",(i=>{t.controls.hover=!e.touch&&\"mouseenter\"===i.type})),t.fullscreen&&Array.from(t.fullscreen.children).filter((e=>!e.contains(t.container))).forEach((i=>{this.bind(i,\"mouseenter mouseleave\",(i=>{t.controls&&(t.controls.hover=!e.touch&&\"mouseenter\"===i.type)}))})),this.bind(t.controls,\"mousedown mouseup touchstart touchend touchcancel\",(e=>{t.controls.pressed=[\"mousedown\",\"touchstart\"].includes(e.type)})),this.bind(t.controls,\"focusin\",(()=>{const{config:i,timers:s}=e;R(t.controls,i.classNames.noTransition,!0),Fe.toggleControls.call(e,!0),setTimeout((()=>{R(t.controls,i.classNames.noTransition,!1)}),0);const n=this.touch?3e3:4e3;clearTimeout(s.controls),s.controls=setTimeout((()=>Fe.toggleControls.call(e,!1)),n)})),this.bind(t.inputs.volume,\"wheel\",(t=>{const i=t.webkitDirectionInvertedFromDevice,[s,n]=[t.deltaX,-t.deltaY].map((e=>i?-e:e)),a=Math.sign(Math.abs(s)>Math.abs(n)?s:n);e.increaseVolume(a/50);const{volume:l}=e.media;(1===a&&l<1||-1===a&&l>0)&&t.preventDefault()}),\"volume\",!1)})),this.player=t,this.lastKey=null,this.focusTimer=null,this.lastKeyDown=null,this.handleKey=this.handleKey.bind(this),this.toggleMenu=this.toggleMenu.bind(this),this.firstTouch=this.firstTouch.bind(this)}handleKey(e){const{player:t}=this,{elements:i}=t,{key:s,type:n,altKey:a,ctrlKey:l,metaKey:r,shiftKey:o}=e,c=\"keydown\"===n,u=c&&s===this.lastKey;if(a||l||r||o)return;if(!s)return;if(c){const n=document.activeElement;if(S.element(n)){const{editable:s}=t.config.selectors,{seek:a}=i.inputs;if(n!==a&&V(n,s))return;if(\" \"===e.key&&V(n,'button, [role^=\"menuitem\"]'))return}switch([\" \",\"ArrowLeft\",\"ArrowUp\",\"ArrowRight\",\"ArrowDown\",\"0\",\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"c\",\"f\",\"k\",\"l\",\"m\"].includes(s)&&(e.preventDefault(),e.stopPropagation()),s){case\"0\":case\"1\":case\"2\":case\"3\":case\"4\":case\"5\":case\"6\":case\"7\":case\"8\":case\"9\":u||(h=parseInt(s,10),t.currentTime=t.duration/10*h);break;case\" \":case\"k\":u||ie(t.togglePlay());break;case\"ArrowUp\":t.increaseVolume(.1);break;case\"ArrowDown\":t.decreaseVolume(.1);break;case\"m\":u||(t.muted=!t.muted);break;case\"ArrowRight\":t.forward();break;case\"ArrowLeft\":t.rewind();break;case\"f\":t.fullscreen.toggle();break;case\"c\":u||t.toggleCaptions();break;case\"l\":t.loop=!t.loop}\"Escape\"===s&&!t.fullscreen.usingNative&&t.fullscreen.active&&t.fullscreen.toggle(),this.lastKey=s}else this.lastKey=null;var h}toggleMenu(e){Pe.toggleMenu.call(this.player,e)}}\"undefined\"!=typeof globalThis?globalThis:\"undefined\"!=typeof window?window:\"undefined\"!=typeof __webpack_require__.g?__webpack_require__.g:\"undefined\"!=typeof self&&self;var Ue=function(e,t){return e(t={exports:{}},t.exports),t.exports}((function(e,t){e.exports=function(){var e=function(){},t={},i={},s={};function n(e,t){e=e.push?e:[e];var n,a,l,r=[],o=e.length,c=o;for(n=function(e,i){i.length&&r.push(e),--c||t(r)};o--;)a=e[o],(l=i[a])?n(a,l):(s[a]=s[a]||[]).push(n)}function a(e,t){if(e){var n=s[e];if(i[e]=t,n)for(;n.length;)n[0](e,t),n.splice(0,1)}}function l(t,i){t.call&&(t={success:t}),i.length?(t.error||e)(i):(t.success||e)(t)}function r(t,i,s,n){var a,l,o=document,c=s.async,u=(s.numRetries||0)+1,h=s.before||e,d=t.replace(/[\\?|#].*$/,\"\"),m=t.replace(/^(css|img)!/,\"\");n=n||0,/(^css!|\\.css$)/.test(d)?((l=o.createElement(\"link\")).rel=\"stylesheet\",l.href=m,(a=\"hideFocus\"in l)&&l.relList&&(a=0,l.rel=\"preload\",l.as=\"style\")):/(^img!|\\.(png|gif|jpg|svg|webp)$)/.test(d)?(l=o.createElement(\"img\")).src=m:((l=o.createElement(\"script\")).src=t,l.async=void 0===c||c),l.onload=l.onerror=l.onbeforeload=function(e){var o=e.type[0];if(a)try{l.sheet.cssText.length||(o=\"e\")}catch(e){18!=e.code&&(o=\"e\")}if(\"e\"==o){if((n+=1)<u)return r(t,i,s,n)}else if(\"preload\"==l.rel&&\"style\"==l.as)return l.rel=\"stylesheet\";i(t,o,e.defaultPrevented)},!1!==h(t,l)&&o.head.appendChild(l)}function o(e,t,i){var s,n,a=(e=e.push?e:[e]).length,l=a,o=[];for(s=function(e,i,s){if(\"e\"==i&&o.push(e),\"b\"==i){if(!s)return;o.push(e)}--a||t(o)},n=0;n<l;n++)r(e[n],s,i)}function c(e,i,s){var n,r;if(i&&i.trim&&(n=i),r=(n?s:i)||{},n){if(n in t)throw\"LoadJS\";t[n]=!0}function c(t,i){o(e,(function(e){l(r,e),t&&l({success:t,error:i},e),a(n,e)}),r)}if(r.returnPromise)return new Promise(c);c()}return c.ready=function(e,t){return n(e,(function(e){l(t,e)})),c},c.done=function(e){a(e,[])},c.reset=function(){t={},i={},s={}},c.isDefined=function(e){return e in t},c}()}));function Be(e){return new Promise(((t,i)=>{Ue(e,{success:t,error:i})}))}function We(e){e&&!this.embed.hasPlayed&&(this.embed.hasPlayed=!0),this.media.paused===e&&(this.media.paused=!e,Z.call(this,this.media,e?\"play\":\"pause\"))}const ze={setup(){const e=this;R(e.elements.wrapper,e.config.classNames.embed,!0),e.options.speed=e.config.speed.options,ue.call(e),S.object(window.Vimeo)?ze.ready.call(e):Be(e.config.urls.vimeo.sdk).then((()=>{ze.ready.call(e)})).catch((t=>{e.debug.warn(\"Vimeo SDK (player.js) failed to load\",t)}))},ready(){const e=this,t=e.config.vimeo,{premium:i,referrerPolicy:s,...n}=t;let a=e.media.getAttribute(\"src\"),l=\"\";S.empty(a)?(a=e.media.getAttribute(e.config.attributes.embed.id),l=e.media.getAttribute(e.config.attributes.embed.hash)):l=function(e){const t=e.match(/^.*(vimeo.com\\/|video\\/)(\\d+)(\\?.*&*h=|\\/)+([\\d,a-f]+)/);return t&&5===t.length?t[4]:null}(a);const r=l?{h:l}:{};i&&Object.assign(n,{controls:!1,sidedock:!1});const o=Ne({loop:e.config.loop.active,autoplay:e.autoplay,muted:e.muted,gesture:\"media\",playsinline:e.config.playsinline,...r,...n}),c=(u=a,S.empty(u)?null:S.number(Number(u))?u:u.match(/^.*(vimeo.com\\/|video\\/)(\\d+).*/)?RegExp.$2:u);var u;const h=$(\"iframe\"),d=me(e.config.urls.vimeo.iframe,c,o);if(h.setAttribute(\"src\",d),h.setAttribute(\"allowfullscreen\",\"\"),h.setAttribute(\"allow\",[\"autoplay\",\"fullscreen\",\"picture-in-picture\",\"encrypted-media\",\"accelerometer\",\"gyroscope\"].join(\"; \")),S.empty(s)||h.setAttribute(\"referrerPolicy\",s),i||!t.customControls)h.setAttribute(\"data-poster\",e.poster),e.media=q(h,e.media);else{const t=$(\"div\",{class:e.config.classNames.embedContainer,\"data-poster\":e.poster});t.appendChild(h),e.media=q(t,e.media)}t.customControls||Te(me(e.config.urls.vimeo.api,d)).then((t=>{!S.empty(t)&&t.thumbnail_url&&Fe.setPoster.call(e,t.thumbnail_url).catch((()=>{}))})),e.embed=new window.Vimeo.Player(h,{autopause:e.config.autopause,muted:e.muted}),e.media.paused=!0,e.media.currentTime=0,e.supported.ui&&e.embed.disableTextTrack(),e.media.play=()=>(We.call(e,!0),e.embed.play()),e.media.pause=()=>(We.call(e,!1),e.embed.pause()),e.media.stop=()=>{e.pause(),e.currentTime=0};let{currentTime:m}=e.media;Object.defineProperty(e.media,\"currentTime\",{get:()=>m,set(t){const{embed:i,media:s,paused:n,volume:a}=e,l=n&&!i.hasPlayed;s.seeking=!0,Z.call(e,s,\"seeking\"),Promise.resolve(l&&i.setVolume(0)).then((()=>i.setCurrentTime(t))).then((()=>l&&i.pause())).then((()=>l&&i.setVolume(a))).catch((()=>{}))}});let p=e.config.speed.selected;Object.defineProperty(e.media,\"playbackRate\",{get:()=>p,set(t){e.embed.setPlaybackRate(t).then((()=>{p=t,Z.call(e,e.media,\"ratechange\")})).catch((()=>{e.options.speed=[1]}))}});let{volume:g}=e.config;Object.defineProperty(e.media,\"volume\",{get:()=>g,set(t){e.embed.setVolume(t).then((()=>{g=t,Z.call(e,e.media,\"volumechange\")}))}});let{muted:f}=e.config;Object.defineProperty(e.media,\"muted\",{get:()=>f,set(t){const i=!!S.boolean(t)&&t;e.embed.setMuted(!!i||e.config.muted).then((()=>{f=i,Z.call(e,e.media,\"volumechange\")}))}});let y,{loop:b}=e.config;Object.defineProperty(e.media,\"loop\",{get:()=>b,set(t){const i=S.boolean(t)?t:e.config.loop.active;e.embed.setLoop(i).then((()=>{b=i}))}}),e.embed.getVideoUrl().then((t=>{y=t,Pe.setDownloadUrl.call(e)})).catch((e=>{this.debug.warn(e)})),Object.defineProperty(e.media,\"currentSrc\",{get:()=>y}),Object.defineProperty(e.media,\"ended\",{get:()=>e.currentTime===e.duration}),Promise.all([e.embed.getVideoWidth(),e.embed.getVideoHeight()]).then((t=>{const[i,s]=t;e.embed.ratio=he(i,s),ue.call(this)})),e.embed.setAutopause(e.config.autopause).then((t=>{e.config.autopause=t})),e.embed.getVideoTitle().then((t=>{e.config.title=t,Fe.setTitle.call(this)})),e.embed.getCurrentTime().then((t=>{m=t,Z.call(e,e.media,\"timeupdate\")})),e.embed.getDuration().then((t=>{e.media.duration=t,Z.call(e,e.media,\"durationchange\")})),e.embed.getTextTracks().then((t=>{e.media.textTracks=t,xe.setup.call(e)})),e.embed.on(\"cuechange\",(({cues:t=[]})=>{const i=t.map((e=>function(e){const t=document.createDocumentFragment(),i=document.createElement(\"div\");return t.appendChild(i),i.innerHTML=e,t.firstChild.innerText}(e.text)));xe.updateCues.call(e,i)})),e.embed.on(\"loaded\",(()=>{if(e.embed.getPaused().then((t=>{We.call(e,!t),t||Z.call(e,e.media,\"playing\")})),S.element(e.embed.element)&&e.supported.ui){e.embed.element.setAttribute(\"tabindex\",-1)}})),e.embed.on(\"bufferstart\",(()=>{Z.call(e,e.media,\"waiting\")})),e.embed.on(\"bufferend\",(()=>{Z.call(e,e.media,\"playing\")})),e.embed.on(\"play\",(()=>{We.call(e,!0),Z.call(e,e.media,\"playing\")})),e.embed.on(\"pause\",(()=>{We.call(e,!1)})),e.embed.on(\"timeupdate\",(t=>{e.media.seeking=!1,m=t.seconds,Z.call(e,e.media,\"timeupdate\")})),e.embed.on(\"progress\",(t=>{e.media.buffered=t.percent,Z.call(e,e.media,\"progress\"),1===parseInt(t.percent,10)&&Z.call(e,e.media,\"canplaythrough\"),e.embed.getDuration().then((t=>{t!==e.media.duration&&(e.media.duration=t,Z.call(e,e.media,\"durationchange\"))}))})),e.embed.on(\"seeked\",(()=>{e.media.seeking=!1,Z.call(e,e.media,\"seeked\")})),e.embed.on(\"ended\",(()=>{e.media.paused=!0,Z.call(e,e.media,\"ended\")})),e.embed.on(\"error\",(t=>{e.media.error=t,Z.call(e,e.media,\"error\")})),t.customControls&&setTimeout((()=>Fe.build.call(e)),0)}};function Ke(e){e&&!this.embed.hasPlayed&&(this.embed.hasPlayed=!0),this.media.paused===e&&(this.media.paused=!e,Z.call(this,this.media,e?\"play\":\"pause\"))}function Ye(e){return e.noCookie?\"https://www.youtube-nocookie.com\":\"http:\"===window.location.protocol?\"http://www.youtube.com\":void 0}const Qe={setup(){if(R(this.elements.wrapper,this.config.classNames.embed,!0),S.object(window.YT)&&S.function(window.YT.Player))Qe.ready.call(this);else{const e=window.onYouTubeIframeAPIReady;window.onYouTubeIframeAPIReady=()=>{S.function(e)&&e(),Qe.ready.call(this)},Be(this.config.urls.youtube.sdk).catch((e=>{this.debug.warn(\"YouTube API failed to load\",e)}))}},getTitle(e){Te(me(this.config.urls.youtube.api,e)).then((e=>{if(S.object(e)){const{title:t,height:i,width:s}=e;this.config.title=t,Fe.setTitle.call(this),this.embed.ratio=he(s,i)}ue.call(this)})).catch((()=>{ue.call(this)}))},ready(){const e=this,t=e.config.youtube,i=e.media&&e.media.getAttribute(\"id\");if(!S.empty(i)&&i.startsWith(\"youtube-\"))return;let s=e.media.getAttribute(\"src\");S.empty(s)&&(s=e.media.getAttribute(this.config.attributes.embed.id));const n=(a=s,S.empty(a)?null:a.match(/^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|&v=)([^#&?]*).*/)?RegExp.$2:a);var a;const l=$(\"div\",{id:`${e.provider}-${Math.floor(1e4*Math.random())}`,\"data-poster\":t.customControls?e.poster:void 0});if(e.media=q(l,e.media),t.customControls){const t=e=>`https://i.ytimg.com/vi/${n}/${e}default.jpg`;Re(t(\"maxres\"),121).catch((()=>Re(t(\"sd\"),121))).catch((()=>Re(t(\"hq\")))).then((t=>Fe.setPoster.call(e,t.src))).then((t=>{t.includes(\"maxres\")||(e.elements.poster.style.backgroundSize=\"cover\")})).catch((()=>{}))}e.embed=new window.YT.Player(e.media,{videoId:n,host:Ye(t),playerVars:x({},{autoplay:e.config.autoplay?1:0,hl:e.config.hl,controls:e.supported.ui&&t.customControls?0:1,disablekb:1,playsinline:e.config.playsinline&&!e.config.fullscreen.iosNative?1:0,cc_load_policy:e.captions.active?1:0,cc_lang_pref:e.config.captions.language,widget_referrer:window?window.location.href:null},t),events:{onError(t){if(!e.media.error){const i=t.data,s={2:\"The request contains an invalid parameter value. For example, this error occurs if you specify a video ID that does not have 11 characters, or if the video ID contains invalid characters, such as exclamation points or asterisks.\",5:\"The requested content cannot be played in an HTML5 player or another error related to the HTML5 player has occurred.\",100:\"The video requested was not found. This error occurs when a video has been removed (for any reason) or has been marked as private.\",101:\"The owner of the requested video does not allow it to be played in embedded players.\",150:\"The owner of the requested video does not allow it to be played in embedded players.\"}[i]||\"An unknown error occurred\";e.media.error={code:i,message:s},Z.call(e,e.media,\"error\")}},onPlaybackRateChange(t){const i=t.target;e.media.playbackRate=i.getPlaybackRate(),Z.call(e,e.media,\"ratechange\")},onReady(i){if(S.function(e.media.play))return;const s=i.target;Qe.getTitle.call(e,n),e.media.play=()=>{Ke.call(e,!0),s.playVideo()},e.media.pause=()=>{Ke.call(e,!1),s.pauseVideo()},e.media.stop=()=>{s.stopVideo()},e.media.duration=s.getDuration(),e.media.paused=!0,e.media.currentTime=0,Object.defineProperty(e.media,\"currentTime\",{get:()=>Number(s.getCurrentTime()),set(t){e.paused&&!e.embed.hasPlayed&&e.embed.mute(),e.media.seeking=!0,Z.call(e,e.media,\"seeking\"),s.seekTo(t)}}),Object.defineProperty(e.media,\"playbackRate\",{get:()=>s.getPlaybackRate(),set(e){s.setPlaybackRate(e)}});let{volume:a}=e.config;Object.defineProperty(e.media,\"volume\",{get:()=>a,set(t){a=t,s.setVolume(100*a),Z.call(e,e.media,\"volumechange\")}});let{muted:l}=e.config;Object.defineProperty(e.media,\"muted\",{get:()=>l,set(t){const i=S.boolean(t)?t:l;l=i,s[i?\"mute\":\"unMute\"](),s.setVolume(100*a),Z.call(e,e.media,\"volumechange\")}}),Object.defineProperty(e.media,\"currentSrc\",{get:()=>s.getVideoUrl()}),Object.defineProperty(e.media,\"ended\",{get:()=>e.currentTime===e.duration});const r=s.getAvailablePlaybackRates();e.options.speed=r.filter((t=>e.config.speed.options.includes(t))),e.supported.ui&&t.customControls&&e.media.setAttribute(\"tabindex\",-1),Z.call(e,e.media,\"timeupdate\"),Z.call(e,e.media,\"durationchange\"),clearInterval(e.timers.buffering),e.timers.buffering=setInterval((()=>{e.media.buffered=s.getVideoLoadedFraction(),(null===e.media.lastBuffered||e.media.lastBuffered<e.media.buffered)&&Z.call(e,e.media,\"progress\"),e.media.lastBuffered=e.media.buffered,1===e.media.buffered&&(clearInterval(e.timers.buffering),Z.call(e,e.media,\"canplaythrough\"))}),200),t.customControls&&setTimeout((()=>Fe.build.call(e)),50)},onStateChange(i){const s=i.target;clearInterval(e.timers.playing);switch(e.media.seeking&&[1,2].includes(i.data)&&(e.media.seeking=!1,Z.call(e,e.media,\"seeked\")),i.data){case-1:Z.call(e,e.media,\"timeupdate\"),e.media.buffered=s.getVideoLoadedFraction(),Z.call(e,e.media,\"progress\");break;case 0:Ke.call(e,!1),e.media.loop?(s.stopVideo(),s.playVideo()):Z.call(e,e.media,\"ended\");break;case 1:t.customControls&&!e.config.autoplay&&e.media.paused&&!e.embed.hasPlayed?e.media.pause():(Ke.call(e,!0),Z.call(e,e.media,\"playing\"),e.timers.playing=setInterval((()=>{Z.call(e,e.media,\"timeupdate\")}),50),e.media.duration!==s.getDuration()&&(e.media.duration=s.getDuration(),Z.call(e,e.media,\"durationchange\")));break;case 2:e.muted||e.embed.unMute(),Ke.call(e,!1);break;case 3:Z.call(e,e.media,\"waiting\")}Z.call(e,e.elements.container,\"statechange\",!1,{code:i.data})}}})}},Xe={setup(){this.media?(R(this.elements.container,this.config.classNames.type.replace(\"{0}\",this.type),!0),R(this.elements.container,this.config.classNames.provider.replace(\"{0}\",this.provider),!0),this.isEmbed&&R(this.elements.container,this.config.classNames.type.replace(\"{0}\",\"video\"),!0),this.isVideo&&(this.elements.wrapper=$(\"div\",{class:this.config.classNames.video}),L(this.media,this.elements.wrapper),this.elements.poster=$(\"div\",{class:this.config.classNames.poster}),this.elements.wrapper.appendChild(this.elements.poster)),this.isHTML5?de.setup.call(this):this.isYouTube?Qe.setup.call(this):this.isVimeo&&ze.setup.call(this)):this.debug.warn(\"No media element found!\")}};class Je{constructor(t){e(this,\"load\",(()=>{this.enabled&&(S.object(window.google)&&S.object(window.google.ima)?this.ready():Be(this.player.config.urls.googleIMA.sdk).then((()=>{this.ready()})).catch((()=>{this.trigger(\"error\",new Error(\"Google IMA SDK failed to load\"))})))})),e(this,\"ready\",(()=>{var e;this.enabled||((e=this).manager&&e.manager.destroy(),e.elements.displayContainer&&e.elements.displayContainer.destroy(),e.elements.container.remove()),this.startSafetyTimer(12e3,\"ready()\"),this.managerPromise.then((()=>{this.clearSafetyTimer(\"onAdsManagerLoaded()\")})),this.listeners(),this.setupIMA()})),e(this,\"setupIMA\",(()=>{this.elements.container=$(\"div\",{class:this.player.config.classNames.ads}),this.player.elements.container.appendChild(this.elements.container),google.ima.settings.setVpaidMode(google.ima.ImaSdkSettings.VpaidMode.ENABLED),google.ima.settings.setLocale(this.player.config.ads.language),google.ima.settings.setDisableCustomPlaybackForIOS10Plus(this.player.config.playsinline),this.elements.displayContainer=new google.ima.AdDisplayContainer(this.elements.container,this.player.media),this.loader=new google.ima.AdsLoader(this.elements.displayContainer),this.loader.addEventListener(google.ima.AdsManagerLoadedEvent.Type.ADS_MANAGER_LOADED,(e=>this.onAdsManagerLoaded(e)),!1),this.loader.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,(e=>this.onAdError(e)),!1),this.requestAds()})),e(this,\"requestAds\",(()=>{const{container:e}=this.player.elements;try{const t=new google.ima.AdsRequest;t.adTagUrl=this.tagUrl,t.linearAdSlotWidth=e.offsetWidth,t.linearAdSlotHeight=e.offsetHeight,t.nonLinearAdSlotWidth=e.offsetWidth,t.nonLinearAdSlotHeight=e.offsetHeight,t.forceNonLinearFullSlot=!1,t.setAdWillPlayMuted(!this.player.muted),this.loader.requestAds(t)}catch(e){this.onAdError(e)}})),e(this,\"pollCountdown\",((e=!1)=>{if(!e)return clearInterval(this.countdownTimer),void this.elements.container.removeAttribute(\"data-badge-text\");this.countdownTimer=setInterval((()=>{const e=Ee(Math.max(this.manager.getRemainingTime(),0)),t=`${ve.get(\"advertisement\",this.player.config)} - ${e}`;this.elements.container.setAttribute(\"data-badge-text\",t)}),100)})),e(this,\"onAdsManagerLoaded\",(e=>{if(!this.enabled)return;const t=new google.ima.AdsRenderingSettings;t.restoreCustomPlaybackStateOnAdBreakComplete=!0,t.enablePreloading=!0,this.manager=e.getAdsManager(this.player,t),this.cuePoints=this.manager.getCuePoints(),this.manager.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,(e=>this.onAdError(e))),Object.keys(google.ima.AdEvent.Type).forEach((e=>{this.manager.addEventListener(google.ima.AdEvent.Type[e],(e=>this.onAdEvent(e)))})),this.trigger(\"loaded\")})),e(this,\"addCuePoints\",(()=>{S.empty(this.cuePoints)||this.cuePoints.forEach((e=>{if(0!==e&&-1!==e&&e<this.player.duration){const t=this.player.elements.progress;if(S.element(t)){const i=100/this.player.duration*e,s=$(\"span\",{class:this.player.config.classNames.cues});s.style.left=`${i.toString()}%`,t.appendChild(s)}}}))})),e(this,\"onAdEvent\",(e=>{const{container:t}=this.player.elements,i=e.getAd(),s=e.getAdData();switch((e=>{Z.call(this.player,this.player.media,`ads${e.replace(/_/g,\"\").toLowerCase()}`)})(e.type),e.type){case google.ima.AdEvent.Type.LOADED:this.trigger(\"loaded\"),this.pollCountdown(!0),i.isLinear()||(i.width=t.offsetWidth,i.height=t.offsetHeight);break;case google.ima.AdEvent.Type.STARTED:this.manager.setVolume(this.player.volume);break;case google.ima.AdEvent.Type.ALL_ADS_COMPLETED:this.player.ended?this.loadAds():this.loader.contentComplete();break;case google.ima.AdEvent.Type.CONTENT_PAUSE_REQUESTED:this.pauseContent();break;case google.ima.AdEvent.Type.CONTENT_RESUME_REQUESTED:this.pollCountdown(),this.resumeContent();break;case google.ima.AdEvent.Type.LOG:s.adError&&this.player.debug.warn(`Non-fatal ad error: ${s.adError.getMessage()}`)}})),e(this,\"onAdError\",(e=>{this.cancel(),this.player.debug.warn(\"Ads error\",e)})),e(this,\"listeners\",(()=>{const{container:e}=this.player.elements;let t;this.player.on(\"canplay\",(()=>{this.addCuePoints()})),this.player.on(\"ended\",(()=>{this.loader.contentComplete()})),this.player.on(\"timeupdate\",(()=>{t=this.player.currentTime})),this.player.on(\"seeked\",(()=>{const e=this.player.currentTime;S.empty(this.cuePoints)||this.cuePoints.forEach(((i,s)=>{t<i&&i<e&&(this.manager.discardAdBreak(),this.cuePoints.splice(s,1))}))})),window.addEventListener(\"resize\",(()=>{this.manager&&this.manager.resize(e.offsetWidth,e.offsetHeight,google.ima.ViewMode.NORMAL)}))})),e(this,\"play\",(()=>{const{container:e}=this.player.elements;this.managerPromise||this.resumeContent(),this.managerPromise.then((()=>{this.manager.setVolume(this.player.volume),this.elements.displayContainer.initialize();try{this.initialized||(this.manager.init(e.offsetWidth,e.offsetHeight,google.ima.ViewMode.NORMAL),this.manager.start()),this.initialized=!0}catch(e){this.onAdError(e)}})).catch((()=>{}))})),e(this,\"resumeContent\",(()=>{this.elements.container.style.zIndex=\"\",this.playing=!1,ie(this.player.media.play())})),e(this,\"pauseContent\",(()=>{this.elements.container.style.zIndex=3,this.playing=!0,this.player.media.pause()})),e(this,\"cancel\",(()=>{this.initialized&&this.resumeContent(),this.trigger(\"error\"),this.loadAds()})),e(this,\"loadAds\",(()=>{this.managerPromise.then((()=>{this.manager&&this.manager.destroy(),this.managerPromise=new Promise((e=>{this.on(\"loaded\",e),this.player.debug.log(this.manager)})),this.initialized=!1,this.requestAds()})).catch((()=>{}))})),e(this,\"trigger\",((e,...t)=>{const i=this.events[e];S.array(i)&&i.forEach((e=>{S.function(e)&&e.apply(this,t)}))})),e(this,\"on\",((e,t)=>(S.array(this.events[e])||(this.events[e]=[]),this.events[e].push(t),this))),e(this,\"startSafetyTimer\",((e,t)=>{this.player.debug.log(`Safety timer invoked from: ${t}`),this.safetyTimer=setTimeout((()=>{this.cancel(),this.clearSafetyTimer(\"startSafetyTimer()\")}),e)})),e(this,\"clearSafetyTimer\",(e=>{S.nullOrUndefined(this.safetyTimer)||(this.player.debug.log(`Safety timer cleared from: ${e}`),clearTimeout(this.safetyTimer),this.safetyTimer=null)})),this.player=t,this.config=t.config.ads,this.playing=!1,this.initialized=!1,this.elements={container:null,displayContainer:null},this.manager=null,this.loader=null,this.cuePoints=null,this.events={},this.safetyTimer=null,this.countdownTimer=null,this.managerPromise=new Promise(((e,t)=>{this.on(\"loaded\",e),this.on(\"error\",t)})),this.load()}get enabled(){const{config:e}=this;return this.player.isHTML5&&this.player.isVideo&&e.enabled&&(!S.empty(e.publisherId)||S.url(e.tagUrl))}get tagUrl(){const{config:e}=this;if(S.url(e.tagUrl))return e.tagUrl;return`https://go.aniview.com/api/adserver6/vast/?${Ne({AV_PUBLISHERID:\"58c25bb0073ef448b1087ad6\",AV_CHANNELID:\"5a0458dc28a06145e4519d21\",AV_URL:window.location.hostname,cb:Date.now(),AV_WIDTH:640,AV_HEIGHT:480,AV_CDIM2:e.publisherId})}`}}function Ge(e=0,t=0,i=255){return Math.min(Math.max(e,t),i)}const Ze=e=>{const t=[];return e.split(/\\r\\n\\r\\n|\\n\\n|\\r\\r/).forEach((e=>{const i={};e.split(/\\r\\n|\\n|\\r/).forEach((e=>{if(S.number(i.startTime)){if(!S.empty(e.trim())&&S.empty(i.text)){const t=e.trim().split(\"#xywh=\");[i.text]=t,t[1]&&([i.x,i.y,i.w,i.h]=t[1].split(\",\"))}}else{const t=e.match(/([0-9]{2})?:?([0-9]{2}):([0-9]{2}).([0-9]{2,3})( ?--> ?)([0-9]{2})?:?([0-9]{2}):([0-9]{2}).([0-9]{2,3})/);t&&(i.startTime=60*Number(t[1]||0)*60+60*Number(t[2])+Number(t[3])+Number(`0.${t[4]}`),i.endTime=60*Number(t[6]||0)*60+60*Number(t[7])+Number(t[8])+Number(`0.${t[9]}`))}})),i.text&&t.push(i)})),t},et=(e,t)=>{const i={};return e>t.width/t.height?(i.width=t.width,i.height=1/e*t.width):(i.height=t.height,i.width=e*t.height),i};class tt{constructor(t){e(this,\"load\",(()=>{this.player.elements.display.seekTooltip&&(this.player.elements.display.seekTooltip.hidden=this.enabled),this.enabled&&this.getThumbnails().then((()=>{this.enabled&&(this.render(),this.determineContainerAutoSizing(),this.listeners(),this.loaded=!0)}))})),e(this,\"getThumbnails\",(()=>new Promise((e=>{const{src:t}=this.player.config.previewThumbnails;if(S.empty(t))throw new Error(\"Missing previewThumbnails.src config attribute\");const i=()=>{this.thumbnails.sort(((e,t)=>e.height-t.height)),this.player.debug.log(\"Preview thumbnails\",this.thumbnails),e()};if(S.function(t))t((e=>{this.thumbnails=e,i()}));else{const e=(S.string(t)?[t]:t).map((e=>this.getThumbnail(e)));Promise.all(e).then(i)}})))),e(this,\"getThumbnail\",(e=>new Promise((t=>{Te(e).then((i=>{const s={frames:Ze(i),height:null,urlPrefix:\"\"};s.frames[0].text.startsWith(\"/\")||s.frames[0].text.startsWith(\"http://\")||s.frames[0].text.startsWith(\"https://\")||(s.urlPrefix=e.substring(0,e.lastIndexOf(\"/\")+1));const n=new Image;n.onload=()=>{s.height=n.naturalHeight,s.width=n.naturalWidth,this.thumbnails.push(s),t()},n.src=s.urlPrefix+s.frames[0].text}))})))),e(this,\"startMove\",(e=>{if(this.loaded&&S.event(e)&&[\"touchmove\",\"mousemove\"].includes(e.type)&&this.player.media.duration){if(\"touchmove\"===e.type)this.seekTime=this.player.media.duration*(this.player.elements.inputs.seek.value/100);else{var t,i;const s=this.player.elements.progress.getBoundingClientRect(),n=100/s.width*(e.pageX-s.left);this.seekTime=this.player.media.duration*(n/100),this.seekTime<0&&(this.seekTime=0),this.seekTime>this.player.media.duration-1&&(this.seekTime=this.player.media.duration-1),this.mousePosX=e.pageX,this.elements.thumb.time.innerText=Ee(this.seekTime);const a=null===(t=this.player.config.markers)||void 0===t||null===(i=t.points)||void 0===i?void 0:i.find((({time:e})=>e===Math.round(this.seekTime)));a&&this.elements.thumb.time.insertAdjacentHTML(\"afterbegin\",`${a.label}<br>`)}this.showImageAtCurrentTime()}})),e(this,\"endMove\",(()=>{this.toggleThumbContainer(!1,!0)})),e(this,\"startScrubbing\",(e=>{(S.nullOrUndefined(e.button)||!1===e.button||0===e.button)&&(this.mouseDown=!0,this.player.media.duration&&(this.toggleScrubbingContainer(!0),this.toggleThumbContainer(!1,!0),this.showImageAtCurrentTime()))})),e(this,\"endScrubbing\",(()=>{this.mouseDown=!1,Math.ceil(this.lastTime)===Math.ceil(this.player.media.currentTime)?this.toggleScrubbingContainer(!1):G.call(this.player,this.player.media,\"timeupdate\",(()=>{this.mouseDown||this.toggleScrubbingContainer(!1)}))})),e(this,\"listeners\",(()=>{this.player.on(\"play\",(()=>{this.toggleThumbContainer(!1,!0)})),this.player.on(\"seeked\",(()=>{this.toggleThumbContainer(!1)})),this.player.on(\"timeupdate\",(()=>{this.lastTime=this.player.media.currentTime}))})),e(this,\"render\",(()=>{this.elements.thumb.container=$(\"div\",{class:this.player.config.classNames.previewThumbnails.thumbContainer}),this.elements.thumb.imageContainer=$(\"div\",{class:this.player.config.classNames.previewThumbnails.imageContainer}),this.elements.thumb.container.appendChild(this.elements.thumb.imageContainer);const e=$(\"div\",{class:this.player.config.classNames.previewThumbnails.timeContainer});this.elements.thumb.time=$(\"span\",{},\"00:00\"),e.appendChild(this.elements.thumb.time),this.elements.thumb.imageContainer.appendChild(e),S.element(this.player.elements.progress)&&this.player.elements.progress.appendChild(this.elements.thumb.container),this.elements.scrubbing.container=$(\"div\",{class:this.player.config.classNames.previewThumbnails.scrubbingContainer}),this.player.elements.wrapper.appendChild(this.elements.scrubbing.container)})),e(this,\"destroy\",(()=>{this.elements.thumb.container&&this.elements.thumb.container.remove(),this.elements.scrubbing.container&&this.elements.scrubbing.container.remove()})),e(this,\"showImageAtCurrentTime\",(()=>{this.mouseDown?this.setScrubbingContainerSize():this.setThumbContainerSizeAndPos();const e=this.thumbnails[0].frames.findIndex((e=>this.seekTime>=e.startTime&&this.seekTime<=e.endTime)),t=e>=0;let i=0;this.mouseDown||this.toggleThumbContainer(t),t&&(this.thumbnails.forEach(((t,s)=>{this.loadedImages.includes(t.frames[e].text)&&(i=s)})),e!==this.showingThumb&&(this.showingThumb=e,this.loadImage(i)))})),e(this,\"loadImage\",((e=0)=>{const t=this.showingThumb,i=this.thumbnails[e],{urlPrefix:s}=i,n=i.frames[t],a=i.frames[t].text,l=s+a;if(this.currentImageElement&&this.currentImageElement.dataset.filename===a)this.showImage(this.currentImageElement,n,e,t,a,!1),this.currentImageElement.dataset.index=t,this.removeOldImages(this.currentImageElement);else{this.loadingImage&&this.usingSprites&&(this.loadingImage.onload=null);const i=new Image;i.src=l,i.dataset.index=t,i.dataset.filename=a,this.showingThumbFilename=a,this.player.debug.log(`Loading image: ${l}`),i.onload=()=>this.showImage(i,n,e,t,a,!0),this.loadingImage=i,this.removeOldImages(i)}})),e(this,\"showImage\",((e,t,i,s,n,a=!0)=>{this.player.debug.log(`Showing thumb: ${n}. num: ${s}. qual: ${i}. newimg: ${a}`),this.setImageSizeAndOffset(e,t),a&&(this.currentImageContainer.appendChild(e),this.currentImageElement=e,this.loadedImages.includes(n)||this.loadedImages.push(n)),this.preloadNearby(s,!0).then(this.preloadNearby(s,!1)).then(this.getHigherQuality(i,e,t,n))})),e(this,\"removeOldImages\",(e=>{Array.from(this.currentImageContainer.children).forEach((t=>{if(\"img\"!==t.tagName.toLowerCase())return;const i=this.usingSprites?500:1e3;if(t.dataset.index!==e.dataset.index&&!t.dataset.deleting){t.dataset.deleting=!0;const{currentImageContainer:e}=this;setTimeout((()=>{e.removeChild(t),this.player.debug.log(`Removing thumb: ${t.dataset.filename}`)}),i)}}))})),e(this,\"preloadNearby\",((e,t=!0)=>new Promise((i=>{setTimeout((()=>{const s=this.thumbnails[0].frames[e].text;if(this.showingThumbFilename===s){let n;n=t?this.thumbnails[0].frames.slice(e):this.thumbnails[0].frames.slice(0,e).reverse();let a=!1;n.forEach((e=>{const t=e.text;if(t!==s&&!this.loadedImages.includes(t)){a=!0,this.player.debug.log(`Preloading thumb filename: ${t}`);const{urlPrefix:e}=this.thumbnails[0],s=e+t,n=new Image;n.src=s,n.onload=()=>{this.player.debug.log(`Preloaded thumb filename: ${t}`),this.loadedImages.includes(t)||this.loadedImages.push(t),i()}}})),a||i()}}),300)})))),e(this,\"getHigherQuality\",((e,t,i,s)=>{if(e<this.thumbnails.length-1){let n=t.naturalHeight;this.usingSprites&&(n=i.h),n<this.thumbContainerHeight&&setTimeout((()=>{this.showingThumbFilename===s&&(this.player.debug.log(`Showing higher quality thumb for: ${s}`),this.loadImage(e+1))}),300)}})),e(this,\"toggleThumbContainer\",((e=!1,t=!1)=>{const i=this.player.config.classNames.previewThumbnails.thumbContainerShown;this.elements.thumb.container.classList.toggle(i,e),!e&&t&&(this.showingThumb=null,this.showingThumbFilename=null)})),e(this,\"toggleScrubbingContainer\",((e=!1)=>{const t=this.player.config.classNames.previewThumbnails.scrubbingContainerShown;this.elements.scrubbing.container.classList.toggle(t,e),e||(this.showingThumb=null,this.showingThumbFilename=null)})),e(this,\"determineContainerAutoSizing\",(()=>{(this.elements.thumb.imageContainer.clientHeight>20||this.elements.thumb.imageContainer.clientWidth>20)&&(this.sizeSpecifiedInCSS=!0)})),e(this,\"setThumbContainerSizeAndPos\",(()=>{const{imageContainer:e}=this.elements.thumb;if(this.sizeSpecifiedInCSS){if(e.clientHeight>20&&e.clientWidth<20){const t=Math.floor(e.clientHeight*this.thumbAspectRatio);e.style.width=`${t}px`}else if(e.clientHeight<20&&e.clientWidth>20){const t=Math.floor(e.clientWidth/this.thumbAspectRatio);e.style.height=`${t}px`}}else{const t=Math.floor(this.thumbContainerHeight*this.thumbAspectRatio);e.style.height=`${this.thumbContainerHeight}px`,e.style.width=`${t}px`}this.setThumbContainerPos()})),e(this,\"setThumbContainerPos\",(()=>{const e=this.player.elements.progress.getBoundingClientRect(),t=this.player.elements.container.getBoundingClientRect(),{container:i}=this.elements.thumb,s=t.left-e.left+10,n=t.right-e.left-i.clientWidth-10,a=this.mousePosX-e.left-i.clientWidth/2,l=Ge(a,s,n);i.style.left=`${l}px`,i.style.setProperty(\"--preview-arrow-offset\",a-l+\"px\")})),e(this,\"setScrubbingContainerSize\",(()=>{const{width:e,height:t}=et(this.thumbAspectRatio,{width:this.player.media.clientWidth,height:this.player.media.clientHeight});this.elements.scrubbing.container.style.width=`${e}px`,this.elements.scrubbing.container.style.height=`${t}px`})),e(this,\"setImageSizeAndOffset\",((e,t)=>{if(!this.usingSprites)return;const i=this.thumbContainerHeight/t.h;e.style.height=e.naturalHeight*i+\"px\",e.style.width=e.naturalWidth*i+\"px\",e.style.left=`-${t.x*i}px`,e.style.top=`-${t.y*i}px`})),this.player=t,this.thumbnails=[],this.loaded=!1,this.lastMouseMoveTime=Date.now(),this.mouseDown=!1,this.loadedImages=[],this.elements={thumb:{},scrubbing:{}},this.load()}get enabled(){return this.player.isHTML5&&this.player.isVideo&&this.player.config.previewThumbnails.enabled}get currentImageContainer(){return this.mouseDown?this.elements.scrubbing.container:this.elements.thumb.imageContainer}get usingSprites(){return Object.keys(this.thumbnails[0].frames[0]).includes(\"w\")}get thumbAspectRatio(){return this.usingSprites?this.thumbnails[0].frames[0].w/this.thumbnails[0].frames[0].h:this.thumbnails[0].width/this.thumbnails[0].height}get thumbContainerHeight(){if(this.mouseDown){const{height:e}=et(this.thumbAspectRatio,{width:this.player.media.clientWidth,height:this.player.media.clientHeight});return e}return this.sizeSpecifiedInCSS?this.elements.thumb.imageContainer.clientHeight:Math.floor(this.player.media.clientWidth/this.thumbAspectRatio/4)}get currentImageElement(){return this.mouseDown?this.currentScrubbingImageElement:this.currentThumbnailImageElement}set currentImageElement(e){this.mouseDown?this.currentScrubbingImageElement=e:this.currentThumbnailImageElement=e}}const it={insertElements(e,t){S.string(t)?_(e,this.media,{src:t}):S.array(t)&&t.forEach((t=>{_(e,this.media,t)}))},change(e){N(e,\"sources.length\")?(de.cancelRequests.call(this),this.destroy.call(this,(()=>{this.options.quality=[],O(this.media),this.media=null,S.element(this.elements.container)&&this.elements.container.removeAttribute(\"class\");const{sources:t,type:i}=e,[{provider:s=_e.html5,src:n}]=t,a=\"html5\"===s?i:\"div\",l=\"html5\"===s?{}:{src:n};Object.assign(this,{provider:s,type:i,supported:K.check(i,s,this.config.playsinline),media:$(a,l)}),this.elements.container.appendChild(this.media),S.boolean(e.autoplay)&&(this.config.autoplay=e.autoplay),this.isHTML5&&(this.config.crossorigin&&this.media.setAttribute(\"crossorigin\",\"\"),this.config.autoplay&&this.media.setAttribute(\"autoplay\",\"\"),S.empty(e.poster)||(this.poster=e.poster),this.config.loop.active&&this.media.setAttribute(\"loop\",\"\"),this.config.muted&&this.media.setAttribute(\"muted\",\"\"),this.config.playsinline&&this.media.setAttribute(\"playsinline\",\"\")),Fe.addStyleHook.call(this),this.isHTML5&&it.insertElements.call(this,\"source\",t),this.config.title=e.title,Xe.setup.call(this),this.isHTML5&&Object.keys(e).includes(\"tracks\")&&it.insertElements.call(this,\"track\",e.tracks),(this.isHTML5||this.isEmbed&&!this.supported.ui)&&Fe.build.call(this),this.isHTML5&&this.media.load(),S.empty(e.previewThumbnails)||(Object.assign(this.config.previewThumbnails,e.previewThumbnails),this.previewThumbnails&&this.previewThumbnails.loaded&&(this.previewThumbnails.destroy(),this.previewThumbnails=null),this.config.previewThumbnails.enabled&&(this.previewThumbnails=new tt(this))),this.fullscreen.update()}),!0)):this.debug.warn(\"Invalid source format\")}};class st{constructor(t,i){if(e(this,\"play\",(()=>S.function(this.media.play)?(this.ads&&this.ads.enabled&&this.ads.managerPromise.then((()=>this.ads.play())).catch((()=>ie(this.media.play()))),this.media.play()):null)),e(this,\"pause\",(()=>this.playing&&S.function(this.media.pause)?this.media.pause():null)),e(this,\"togglePlay\",(e=>(S.boolean(e)?e:!this.playing)?this.play():this.pause())),e(this,\"stop\",(()=>{this.isHTML5?(this.pause(),this.restart()):S.function(this.media.stop)&&this.media.stop()})),e(this,\"restart\",(()=>{this.currentTime=0})),e(this,\"rewind\",(e=>{this.currentTime-=S.number(e)?e:this.config.seekTime})),e(this,\"forward\",(e=>{this.currentTime+=S.number(e)?e:this.config.seekTime})),e(this,\"increaseVolume\",(e=>{const t=this.media.muted?0:this.volume;this.volume=t+(S.number(e)?e:0)})),e(this,\"decreaseVolume\",(e=>{this.increaseVolume(-e)})),e(this,\"airplay\",(()=>{K.airplay&&this.media.webkitShowPlaybackTargetPicker()})),e(this,\"toggleControls\",(e=>{if(this.supported.ui&&!this.isAudio){const t=F(this.elements.container,this.config.classNames.hideControls),i=void 0===e?void 0:!e,s=R(this.elements.container,this.config.classNames.hideControls,i);if(s&&S.array(this.config.controls)&&this.config.controls.includes(\"settings\")&&!S.empty(this.config.settings)&&Pe.toggleMenu.call(this,!1),s!==t){const e=s?\"controlshidden\":\"controlsshown\";Z.call(this,this.media,e)}return!s}return!1})),e(this,\"on\",((e,t)=>{X.call(this,this.elements.container,e,t)})),e(this,\"once\",((e,t)=>{G.call(this,this.elements.container,e,t)})),e(this,\"off\",((e,t)=>{J(this.elements.container,e,t)})),e(this,\"destroy\",((e,t=!1)=>{if(!this.ready)return;const i=()=>{document.body.style.overflow=\"\",this.embed=null,t?(Object.keys(this.elements).length&&(O(this.elements.buttons.play),O(this.elements.captions),O(this.elements.controls),O(this.elements.wrapper),this.elements.buttons.play=null,this.elements.captions=null,this.elements.controls=null,this.elements.wrapper=null),S.function(e)&&e()):(ee.call(this),de.cancelRequests.call(this),q(this.elements.original,this.elements.container),Z.call(this,this.elements.original,\"destroyed\",!0),S.function(e)&&e.call(this.elements.original),this.ready=!1,setTimeout((()=>{this.elements=null,this.media=null}),200))};this.stop(),clearTimeout(this.timers.loading),clearTimeout(this.timers.controls),clearTimeout(this.timers.resized),this.isHTML5?(Fe.toggleNativeControls.call(this,!0),i()):this.isYouTube?(clearInterval(this.timers.buffering),clearInterval(this.timers.playing),null!==this.embed&&S.function(this.embed.destroy)&&this.embed.destroy(),i()):this.isVimeo&&(null!==this.embed&&this.embed.unload().then(i),setTimeout(i,200))})),e(this,\"supports\",(e=>K.mime.call(this,e))),this.timers={},this.ready=!1,this.loading=!1,this.failed=!1,this.touch=K.touch,this.media=t,S.string(this.media)&&(this.media=document.querySelectorAll(this.media)),(window.jQuery&&this.media instanceof jQuery||S.nodeList(this.media)||S.array(this.media))&&(this.media=this.media[0]),this.config=x({},Le,st.defaults,i||{},(()=>{try{return JSON.parse(this.media.getAttribute(\"data-plyr-config\"))}catch(e){return{}}})()),this.elements={container:null,fullscreen:null,captions:null,buttons:{},display:{},progress:{},inputs:{},settings:{popup:null,menu:null,panels:{},buttons:{}}},this.captions={active:null,currentTrack:-1,meta:new WeakMap},this.fullscreen={active:!1},this.options={speed:[],quality:[]},this.debug=new De(this.config.debug),this.debug.log(\"Config\",this.config),this.debug.log(\"Support\",K),S.nullOrUndefined(this.media)||!S.element(this.media))return void this.debug.error(\"Setup failed: no suitable element passed\");if(this.media.plyr)return void this.debug.warn(\"Target already setup\");if(!this.config.enabled)return void this.debug.error(\"Setup failed: disabled by config\");if(!K.check().api)return void this.debug.error(\"Setup failed: no support\");const s=this.media.cloneNode(!0);s.autoplay=!1,this.elements.original=s;const n=this.media.tagName.toLowerCase();let a=null,l=null;switch(n){case\"div\":if(a=this.media.querySelector(\"iframe\"),S.element(a)){if(l=Me(a.getAttribute(\"src\")),this.provider=function(e){return/^(https?:\\/\\/)?(www\\.)?(youtube\\.com|youtube-nocookie\\.com|youtu\\.?be)\\/.+$/.test(e)?_e.youtube:/^https?:\\/\\/player.vimeo.com\\/video\\/\\d{0,9}(?=\\b|\\/)/.test(e)?_e.vimeo:null}(l.toString()),this.elements.container=this.media,this.media=a,this.elements.container.className=\"\",l.search.length){const e=[\"1\",\"true\"];e.includes(l.searchParams.get(\"autoplay\"))&&(this.config.autoplay=!0),e.includes(l.searchParams.get(\"loop\"))&&(this.config.loop.active=!0),this.isYouTube?(this.config.playsinline=e.includes(l.searchParams.get(\"playsinline\")),this.config.youtube.hl=l.searchParams.get(\"hl\")):this.config.playsinline=!0}}else this.provider=this.media.getAttribute(this.config.attributes.embed.provider),this.media.removeAttribute(this.config.attributes.embed.provider);if(S.empty(this.provider)||!Object.values(_e).includes(this.provider))return void this.debug.error(\"Setup failed: Invalid provider\");this.type=je;break;case\"video\":case\"audio\":this.type=n,this.provider=_e.html5,this.media.hasAttribute(\"crossorigin\")&&(this.config.crossorigin=!0),this.media.hasAttribute(\"autoplay\")&&(this.config.autoplay=!0),(this.media.hasAttribute(\"playsinline\")||this.media.hasAttribute(\"webkit-playsinline\"))&&(this.config.playsinline=!0),this.media.hasAttribute(\"muted\")&&(this.config.muted=!0),this.media.hasAttribute(\"loop\")&&(this.config.loop.active=!0);break;default:return void this.debug.error(\"Setup failed: unsupported type\")}this.supported=K.check(this.type,this.provider),this.supported.api?(this.eventListeners=[],this.listeners=new Ve(this),this.storage=new we(this),this.media.plyr=this,S.element(this.elements.container)||(this.elements.container=$(\"div\"),L(this.media,this.elements.container)),Fe.migrateStyles.call(this),Fe.addStyleHook.call(this),Xe.setup.call(this),this.config.debug&&X.call(this,this.elements.container,this.config.events.join(\" \"),(e=>{this.debug.log(`event: ${e.type}`)})),this.fullscreen=new He(this),(this.isHTML5||this.isEmbed&&!this.supported.ui)&&Fe.build.call(this),this.listeners.container(),this.listeners.global(),this.config.ads.enabled&&(this.ads=new Je(this)),this.isHTML5&&this.config.autoplay&&this.once(\"canplay\",(()=>ie(this.play()))),this.lastSeekTime=0,this.config.previewThumbnails.enabled&&(this.previewThumbnails=new tt(this))):this.debug.error(\"Setup failed: no support\")}get isHTML5(){return this.provider===_e.html5}get isEmbed(){return this.isYouTube||this.isVimeo}get isYouTube(){return this.provider===_e.youtube}get isVimeo(){return this.provider===_e.vimeo}get isVideo(){return this.type===je}get isAudio(){return this.type===Oe}get playing(){return Boolean(this.ready&&!this.paused&&!this.ended)}get paused(){return Boolean(this.media.paused)}get stopped(){return Boolean(this.paused&&0===this.currentTime)}get ended(){return Boolean(this.media.ended)}set currentTime(e){if(!this.duration)return;const t=S.number(e)&&e>0;this.media.currentTime=t?Math.min(e,this.duration):0,this.debug.log(`Seeking to ${this.currentTime} seconds`)}get currentTime(){return Number(this.media.currentTime)}get buffered(){const{buffered:e}=this.media;return S.number(e)?e:e&&e.length&&this.duration>0?e.end(0)/this.duration:0}get seeking(){return Boolean(this.media.seeking)}get duration(){const e=parseFloat(this.config.duration),t=(this.media||{}).duration,i=S.number(t)&&t!==1/0?t:0;return e||i}set volume(e){let t=e;S.string(t)&&(t=Number(t)),S.number(t)||(t=this.storage.get(\"volume\")),S.number(t)||({volume:t}=this.config),t>1&&(t=1),t<0&&(t=0),this.config.volume=t,this.media.volume=t,!S.empty(e)&&this.muted&&t>0&&(this.muted=!1)}get volume(){return Number(this.media.volume)}set muted(e){let t=e;S.boolean(t)||(t=this.storage.get(\"muted\")),S.boolean(t)||(t=this.config.muted),this.config.muted=t,this.media.muted=t}get muted(){return Boolean(this.media.muted)}get hasAudio(){return!this.isHTML5||(!!this.isAudio||(Boolean(this.media.mozHasAudio)||Boolean(this.media.webkitAudioDecodedByteCount)||Boolean(this.media.audioTracks&&this.media.audioTracks.length)))}set speed(e){let t=null;S.number(e)&&(t=e),S.number(t)||(t=this.storage.get(\"speed\")),S.number(t)||(t=this.config.speed.selected);const{minimumSpeed:i,maximumSpeed:s}=this;t=Ge(t,i,s),this.config.speed.selected=t,setTimeout((()=>{this.media&&(this.media.playbackRate=t)}),0)}get speed(){return Number(this.media.playbackRate)}get minimumSpeed(){return this.isYouTube?Math.min(...this.options.speed):this.isVimeo?.5:.0625}get maximumSpeed(){return this.isYouTube?Math.max(...this.options.speed):this.isVimeo?2:16}set quality(e){const t=this.config.quality,i=this.options.quality;if(!i.length)return;let s=[!S.empty(e)&&Number(e),this.storage.get(\"quality\"),t.selected,t.default].find(S.number),n=!0;if(!i.includes(s)){const e=ne(i,s);this.debug.warn(`Unsupported quality option: ${s}, using ${e} instead`),s=e,n=!1}t.selected=s,this.media.quality=s,n&&this.storage.set({quality:s})}get quality(){return this.media.quality}set loop(e){const t=S.boolean(e)?e:this.config.loop.active;this.config.loop.active=t,this.media.loop=t}get loop(){return Boolean(this.media.loop)}set source(e){it.change.call(this,e)}get source(){return this.media.currentSrc}get download(){const{download:e}=this.config.urls;return S.url(e)?e:this.source}set download(e){S.url(e)&&(this.config.urls.download=e,Pe.setDownloadUrl.call(this))}set poster(e){this.isVideo?Fe.setPoster.call(this,e,!1).catch((()=>{})):this.debug.warn(\"Poster can only be set for video\")}get poster(){return this.isVideo?this.media.getAttribute(\"poster\")||this.media.getAttribute(\"data-poster\"):null}get ratio(){if(!this.isVideo)return null;const e=oe(ce.call(this));return S.array(e)?e.join(\":\"):e}set ratio(e){this.isVideo?S.string(e)&&re(e)?(this.config.ratio=oe(e),ue.call(this)):this.debug.error(`Invalid aspect ratio specified (${e})`):this.debug.warn(\"Aspect ratio can only be set for video\")}set autoplay(e){this.config.autoplay=S.boolean(e)?e:this.config.autoplay}get autoplay(){return Boolean(this.config.autoplay)}toggleCaptions(e){xe.toggle.call(this,e,!1)}set currentTrack(e){xe.set.call(this,e,!1),xe.setup.call(this)}get currentTrack(){const{toggled:e,currentTrack:t}=this.captions;return e?t:-1}set language(e){xe.setLanguage.call(this,e,!1)}get language(){return(xe.getCurrentTrack.call(this)||{}).language}set pip(e){if(!K.pip)return;const t=S.boolean(e)?e:!this.pip;S.function(this.media.webkitSetPresentationMode)&&this.media.webkitSetPresentationMode(t?Ie:$e),S.function(this.media.requestPictureInPicture)&&(!this.pip&&t?this.media.requestPictureInPicture():this.pip&&!t&&document.exitPictureInPicture())}get pip(){return K.pip?S.empty(this.media.webkitPresentationMode)?this.media===document.pictureInPictureElement:this.media.webkitPresentationMode===Ie:null}setPreviewThumbnails(e){this.previewThumbnails&&this.previewThumbnails.loaded&&(this.previewThumbnails.destroy(),this.previewThumbnails=null),Object.assign(this.config.previewThumbnails,e),this.config.previewThumbnails.enabled&&(this.previewThumbnails=new tt(this))}static supported(e,t){return K.check(e,t)}static loadSprite(e,t){return ke(e,t)}static setup(e,t={}){let i=null;return S.string(e)?i=Array.from(document.querySelectorAll(e)):S.nodeList(e)?i=Array.from(e):S.array(e)&&(i=e.filter(S.element)),S.empty(i)?null:i.map((e=>new st(e,t)))}}var nt;return st.defaults=(nt=Le,JSON.parse(JSON.stringify(nt))),st}));\n//# sourceMappingURL=plyr.min.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/plyr/dist/plyr.min.js\n"));

/***/ })

}]);