"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(pages)/instructor/dashboard/[id]/page",{

/***/ "(app-pages-browser)/./src/app/_Components/pdf/QuizPDFGenerator.jsx":
/*!******************************************************!*\
  !*** ./src/app/_Components/pdf/QuizPDFGenerator.jsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateQuizPDF: () => (/* binding */ generateQuizPDF)\n/* harmony export */ });\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jspdf */ \"(app-pages-browser)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n/* harmony import */ var jspdf_autotable__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jspdf-autotable */ \"(app-pages-browser)/./node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.mjs\");\n/* harmony import */ var _fonts_Amiri_Regular_normal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../fonts/Amiri-Regular-normal */ \"(app-pages-browser)/./src/fonts/Amiri-Regular-normal.js\");\n// ✅ ملف: src/app/_Components/pdf/QuizPDFGenerator.jsx\n\n\n\nfunction toArabicNumber(number) {\n    const arabicDigits = [\n        \"٠\",\n        \"١\",\n        \"٢\",\n        \"٣\",\n        \"٤\",\n        \"٥\",\n        \"٦\",\n        \"٧\",\n        \"٨\",\n        \"٩\"\n    ];\n    return number.toString().replace(/\\d/g, (d)=>arabicDigits[d]);\n}\nconst toBase64 = (url)=>fetch(url).then((res)=>res.blob()).then((blob)=>new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.onloadend = ()=>resolve(reader.result);\n            reader.onerror = reject;\n            reader.readAsDataURL(blob);\n        }));\n// دالة لحساب أبعاد الصورة مع الحفاظ على النسبة الأصلية\nconst calculateImageDimensions = (originalWidth, originalHeight, maxWidth, maxHeight)=>{\n    const aspectRatio = originalWidth / originalHeight;\n    let newWidth = maxWidth;\n    let newHeight = maxWidth / aspectRatio;\n    // إذا كان الارتفاع أكبر من المسموح، نقلل العرض\n    if (newHeight > maxHeight) {\n        newHeight = maxHeight;\n        newWidth = maxHeight * aspectRatio;\n    }\n    return {\n        width: newWidth,\n        height: newHeight\n    };\n};\n// دالة لتحميل الصورة والحصول على أبعادها الأصلية\nconst loadImageWithDimensions = (url)=>{\n    return new Promise((resolve, reject)=>{\n        const img = new Image();\n        img.crossOrigin = \"anonymous\";\n        img.onload = ()=>{\n            const canvas = document.createElement('canvas');\n            const ctx = canvas.getContext('2d');\n            canvas.width = img.naturalWidth;\n            canvas.height = img.naturalHeight;\n            ctx.drawImage(img, 0, 0);\n            const base64 = canvas.toDataURL('image/jpeg', 0.9); // جودة عالية\n            resolve({\n                base64,\n                originalWidth: img.naturalWidth,\n                originalHeight: img.naturalHeight\n            });\n        };\n        img.onerror = reject;\n        img.src = url;\n    });\n};\nasync function generateQuizPDF(quiz, user) {\n    // تحسين إعدادات PDF - zakialkholy\n    const doc = new jspdf__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n        orientation: \"portrait\",\n        unit: \"mm\",\n        format: \"a4\",\n        compress: true // ضغط الملف لتقليل الحجم\n    });\n    doc.setFont(\"Amiri-Regular\", \"normal\");\n    doc.setFontSize(14);\n    const pageHeight = 297;\n    const pageWidth = 210;\n    const marginHorizontal = 15;\n    const contentMarginTop = 35; // زيادة المساحة العلوية - zakialkholy\n    const contentMarginBottom = 30; // زيادة المساحة السفلية - zakialkholy\n    const labels = [\n        \"أ\",\n        \"ب\",\n        \"ج\",\n        \"د\",\n        \"هـ\",\n        \"و\"\n    ];\n    let y = contentMarginTop;\n    const estimateQuestionHeight = (q)=>{\n        let height = 10;\n        // السؤال في سطر واحد\n        height += 10;\n        // الصورة تأخذ مساحة متغيرة حسب حجمها الأصلي (حد أقصى 85)\n        if (q.image_url) height += 85;\n        // الاختيارات تحت الصورة - حساب عدد الصفوف المطلوبة\n        const answersPerRow = 2; // عدد الاختيارات في كل صف\n        const answerRows = Math.ceil(q.answers.length / answersPerRow);\n        height += answerRows * 10;\n        return height + 15;\n    };\n    const renderHeader = ()=>{\n        var _quiz_questions;\n        const topY = 15;\n        // تحسين تصميم الهيدر - zakialkholy\n        doc.setFontSize(16);\n        doc.setFont(\"Amiri-Regular\", \"bold\");\n        doc.text(\"\".concat(quiz.title), pageWidth / 2, topY, {\n            align: \"center\"\n        });\n        doc.setFontSize(10);\n        doc.setFont(\"Amiri-Regular\", \"normal\");\n        doc.text(\"اسم المعلم: \".concat(user.first_name || user.username), marginHorizontal + 35, topY + 8, {\n            align: \"right\"\n        });\n        doc.text(\"رقم الهاتف: \".concat(user.phone_number || \"غير محدد\"), pageWidth - marginHorizontal - 35, topY + 8, {\n            align: \"left\"\n        });\n        // إضافة معلومات إضافية - zakialkholy\n        doc.setFontSize(9);\n        doc.text(\"نوع التقييم: \".concat(quiz.quiz_type === \"exam\" ? \"امتحان\" : \"واجب\"), marginHorizontal, topY + 16, {\n            align: \"right\"\n        });\n        doc.text(\"الدرجة النهائية: \".concat(quiz.max_score), pageWidth / 2, topY + 16, {\n            align: \"center\"\n        });\n        doc.text(\"عدد الأسئلة: \".concat(((_quiz_questions = quiz.questions) === null || _quiz_questions === void 0 ? void 0 : _quiz_questions.length) || 0), pageWidth - marginHorizontal, topY + 16, {\n            align: \"left\"\n        });\n        // خط فاصل محسن - zakialkholy\n        doc.setLineWidth(0.5);\n        doc.line(marginHorizontal, topY + 20, pageWidth - marginHorizontal, topY + 20);\n        doc.setLineWidth(0.2);\n        doc.setFontSize(14);\n        doc.setFont(\"Amiri-Regular\", \"normal\");\n    };\n    const renderFooter = ()=>{\n        const footerY = pageHeight - 15;\n        // خط فوق الفوتر محسن - zakialkholy\n        doc.setLineWidth(0.3);\n        doc.line(marginHorizontal, footerY - 8, pageWidth - marginHorizontal, footerY - 8);\n        // تحسين تصميم الفوتر - zakialkholy\n        doc.setFontSize(8);\n        doc.setFont(\"Amiri-Regular\", \"normal\");\n        // إضافة تاريخ الإنشاء - zakialkholy\n        const currentDate = new Date().toLocaleDateString('ar-EG', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n        doc.text(\"تاريخ الإنشاء: \".concat(currentDate), marginHorizontal, footerY, {\n            align: \"right\"\n        });\n        doc.text(\"تم إنشاء هذا الملف بواسطة منصة منصة\", pageWidth / 2, footerY, {\n            align: \"center\"\n        });\n        doc.text(\"صفحة \".concat(doc.internal.getCurrentPageInfo().pageNumber), pageWidth - marginHorizontal, footerY, {\n            align: \"left\"\n        });\n        doc.setFontSize(14);\n        doc.setFont(\"Amiri-Regular\", \"normal\");\n    };\n    renderHeader();\n    for(let i = 0; i < quiz.questions.length; i++){\n        const q = quiz.questions[i];\n        const qNumber = toArabicNumber(i + 1);\n        const estimatedHeight = estimateQuestionHeight(q);\n        if (y + estimatedHeight > pageHeight - contentMarginBottom) {\n            renderFooter();\n            doc.addPage();\n            renderHeader();\n            y = contentMarginTop;\n        }\n        const boxTopY = y;\n        let contentY = y + 5;\n        // السؤال في سطر واحد\n        const qText = \"\".concat(qNumber, \"- \").concat(q.text);\n        doc.text(qText, pageWidth - marginHorizontal - 5, contentY, {\n            align: \"right\"\n        });\n        contentY += 10;\n        let maxContentY = contentY;\n        // الصورة بجودة عالية مع الحفاظ على النسبة الأصلية\n        if (q.image_url) {\n            try {\n                const imageData = await loadImageWithDimensions(q.image_url);\n                // الحد الأقصى للعرض والارتفاع\n                const maxWidth = pageWidth - 2 * marginHorizontal - 10;\n                const maxHeight = 80; // ارتفاع أقصى معقول\n                // حساب الأبعاد مع الحفاظ على النسبة الأصلية\n                const { width: imgWidth, height: imgHeight } = calculateImageDimensions(imageData.originalWidth, imageData.originalHeight, maxWidth, maxHeight);\n                // توسيط الصورة أفقياً\n                const imgX = marginHorizontal + (maxWidth - imgWidth) / 2;\n                doc.addImage(imageData.base64, \"JPEG\", imgX, contentY, imgWidth, imgHeight);\n                contentY += imgHeight + 5; // مسافة بعد الصورة\n                maxContentY = contentY;\n            } catch (err) {\n                console.warn(\"⚠️ فشل تحميل الصورة:\", err);\n                // في حالة فشل التحميل، نستخدم الطريقة القديمة\n                try {\n                    const base64 = await toBase64(q.image_url);\n                    const imgWidth = 100; // حجم معقول\n                    const imgHeight = 60;\n                    const imgX = marginHorizontal + 5;\n                    doc.addImage(base64, \"JPEG\", imgX, contentY, imgWidth, imgHeight);\n                    contentY += imgHeight + 5;\n                    maxContentY = contentY;\n                } catch (fallbackErr) {\n                    console.warn(\"⚠️ فشل تحميل الصورة نهائياً:\", fallbackErr);\n                }\n            }\n        }\n        // الاختيارات تحت الصورة وجنب بعض\n        let answerY = contentY;\n        const answersPerRow = 2; // عدد الاختيارات في كل صف\n        const answerWidth = (pageWidth - 2 * marginHorizontal - 20) / answersPerRow; // عرض كل اختيار\n        for(let j = 0; j < q.answers.length; j += answersPerRow){\n            // الاختيار الأول (على اليمين)\n            if (q.answers[j]) {\n                const a1 = \"\".concat(labels[j] || toArabicNumber(j + 1), \"- \").concat(q.answers[j].text);\n                const a1X = pageWidth - marginHorizontal - 5;\n                doc.text(a1, a1X, answerY, {\n                    align: \"right\"\n                });\n            }\n            // الاختيار الثاني (على اليسار)\n            if (q.answers[j + 1]) {\n                const a2 = \"\".concat(labels[j + 1] || toArabicNumber(j + 2), \"- \").concat(q.answers[j + 1].text);\n                const a2X = pageWidth - marginHorizontal - answerWidth;\n                doc.text(a2, a2X, answerY, {\n                    align: \"right\"\n                });\n            }\n            answerY += 10;\n        }\n        const bottomY = Math.max(answerY, maxContentY);\n        const boxHeight = bottomY - boxTopY + 5;\n        doc.rect(marginHorizontal, boxTopY, pageWidth - 2 * marginHorizontal, boxHeight);\n        y = bottomY + 10;\n    }\n    renderFooter();\n    doc.save(\"\".concat(quiz.title, \".pdf\"));\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/_Components/pdf/QuizPDFGenerator.jsx\n"));

/***/ })

});