from rest_framework import serializers
from django.contrib.auth import get_user_model

from ..models import Course, InstructorProfile, InstructorAvailability
from .course_serializer import CourseReadSerializer

User = get_user_model()


# -------------------- InstructorWithCoursesSerializer --------------------


class InstructorWithCoursesSerializer(serializers.ModelSerializer):
    courses = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            "id",
            "username",
            "email",
            "first_name",
            "last_name",
            "is_instructor",
            "is_student",
            "profile_image",
            "phone_number",
            "bio",
            "date_of_birth",
            "language",
            "courses",
            "has_wallet",
            "wallet_number",
            "payment_method",
        ]

    def get_courses(self, obj):
        # الحصول على المستخدم الحالي من الـ context - zakialkholy
        request = self.context.get('request')
        current_user = request.user if request else None

        if current_user:
            if current_user == obj:
                # لو المستخدم الحالي هو المعلم نفسه، يشوف كل كورساته - zakialkholy
                courses = Course.objects.filter(instructor=obj)
            elif current_user.is_authenticated:
                # لو المستخدم مسجل دخول، يشوف الكورسات المنشورة + الكورسات اللي مسجل فيها - zakialkholy
                from django.db.models import Q
                courses = Course.objects.filter(
                    Q(instructor=obj) & (Q(is_published=True) | Q(students=current_user))
                )
            else:
                # لو المستخدم مش مسجل دخول، يشوف الكورسات المنشورة بس - zakialkholy
                courses = Course.objects.filter(instructor=obj, is_published=True)
        else:
            # fallback: الكورسات المنشورة بس - zakialkholy
            courses = Course.objects.filter(instructor=obj, is_published=True)

        return CourseReadSerializer(courses, many=True, context=self.context).data


class InstructorProfileSerializer(serializers.ModelSerializer):
    payment_details = serializers.JSONField(required=False, allow_null=True)

    class Meta:
        model = InstructorProfile
        fields = [
            "id",
            "user",
            "specialization",
            "qualifications",
            "website",
            "linkedin",
            "payment_method",
            "payment_details",
            "is_approved",
        ]
        read_only_fields = ["user", "is_approved"]

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        # أضف payment_details من user إذا لم يكن موجوداً في البروفايل
        if not ret.get("payment_details") and hasattr(instance, "user"):
            user_payment_details = getattr(instance.user, "payment_details", None)
            if user_payment_details:
                ret["payment_details"] = user_payment_details
        return ret


class InstructorAvailabilitySerializer(serializers.ModelSerializer):
    class Meta:
        model = InstructorAvailability
        fields = [
            "id",
            "user",
            "day",
            "from_time",
            "to_time",
            "timezone",
            "enabled",
            "note",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "user", "created_at", "updated_at"]
