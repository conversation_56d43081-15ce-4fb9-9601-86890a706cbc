"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(pages)/instructor/dashboard/[id]/page",{

/***/ "(app-pages-browser)/./src/components/instructor/CourseHeader.jsx":
/*!****************************************************!*\
  !*** ./src/components/instructor/CourseHeader.jsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _config_environment__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/environment */ \"(app-pages-browser)/./src/config/environment.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst CourseHeader = (param)=>{\n    let { courseId, courseData, publishing, togglePublish, setShowEditModal } = param;\n    var _courseData_category;\n    _s();\n    // إضافة state لإدارة حالة المشاركة - zakialkholy\n    const [shareLoading, setShareLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [shareSuccess, setShareSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showShareModal, setShowShareModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [courseUrl, setCourseUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // دالة مشاركة رابط الكورس للطلبة - zakialkholy\n    const handleShareCourse = async ()=>{\n        if (!(courseData === null || courseData === void 0 ? void 0 : courseData.slug)) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"لا يمكن مشاركة الكورس - البيانات غير مكتملة\");\n            return;\n        }\n        setShareLoading(true);\n        try {\n            // بناء رابط الكورس للطلبة - zakialkholy\n            const frontendUrl = _config_environment__WEBPACK_IMPORTED_MODULE_4__.ENV.FRONTEND_URL || window.location.origin;\n            const courseUrl = \"\".concat(frontendUrl, \"/student/course/\").concat(courseData.slug);\n            // نسخ الرابط للحافظة - zakialkholy\n            if (navigator.clipboard && window.isSecureContext) {\n                await navigator.clipboard.writeText(courseUrl);\n            } else {\n                // fallback للمتصفحات القديمة - zakialkholy\n                const textArea = document.createElement(\"textarea\");\n                textArea.value = courseUrl;\n                document.body.appendChild(textArea);\n                textArea.focus();\n                textArea.select();\n                document.execCommand('copy');\n                document.body.removeChild(textArea);\n            }\n            // إظهار رسالة نجاح - zakialkholy\n            setShareSuccess(true);\n            setTimeout(()=>setShareSuccess(false), 3000);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"تم نسخ رابط الكورس بنجاح! يمكنك الآن مشاركته مع الطلبة\");\n        } catch (error) {\n            console.error(\"خطأ في نسخ الرابط:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"حدث خطأ أثناء نسخ الرابط. يرجى المحاولة مرة أخرى.\");\n        } finally{\n            setShareLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl lg:text-4xl font-bold text-white\",\n                                children: (courseData === null || courseData === void 0 ? void 0 : courseData.title) || \"تفاصيل الكورس\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-3 py-1 rounded-full text-sm font-medium \".concat((courseData === null || courseData === void 0 ? void 0 : courseData.is_published) ? \"bg-green-500/20 text-green-100 border border-green-400/30\" : \"bg-yellow-500/20 text-yellow-100 border border-yellow-400/30\"),\n                                children: (courseData === null || courseData === void 0 ? void 0 : courseData.is_published) ? \"منشور\" : \"مسودة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/80 text-lg max-w-2xl\",\n                        children: (courseData === null || courseData === void 0 ? void 0 : courseData.short_description) || (courseData === null || courseData === void 0 ? void 0 : courseData.description) || \"إدارة وتحرير محتوى الكورس\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-6 mt-4 text-white/70\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            (courseData === null || courseData === void 0 ? void 0 : courseData.students_count) || 0,\n                                            \" طالب\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: (courseData === null || courseData === void 0 ? void 0 : courseData.rating) || \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: (courseData === null || courseData === void 0 ? void 0 : (_courseData_category = courseData.category) === null || _courseData_category === void 0 ? void 0 : _courseData_category.name) || \"غير محدد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleShareCourse,\n                        disabled: shareLoading || !(courseData === null || courseData === void 0 ? void 0 : courseData.slug),\n                        className: \"bg-white/10 backdrop-blur-sm text-white px-6 py-3 rounded-xl hover:bg-white/20 transition-all duration-300 border border-white/20 flex items-center gap-2 font-medium \".concat(shareLoading ? \"opacity-50 cursor-not-allowed\" : \"\", \" \").concat(shareSuccess ? \"bg-green-500/20 border-green-400/30\" : \"\"),\n                        title: \"نسخ رابط الكورس لمشاركته مع الطلبة\",\n                        children: shareLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 animate-spin\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"جاري النسخ...\"\n                            ]\n                        }, void 0, true) : shareSuccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M5 13l4 4L19 7\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"تم النسخ!\"\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"مشاركة الكورس\"\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/instructor/dashboard/\".concat(courseId, \"/add-lesson\"),\n                        className: \"bg-white/10 backdrop-blur-sm text-white px-6 py-3 rounded-xl hover:bg-white/20 transition-all duration-300 border border-white/20 flex items-center gap-2 font-medium\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, undefined),\n                            \"إضافة درس جديد\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"bg-white/10 backdrop-blur-sm text-white px-6 py-3 rounded-xl hover:bg-white/20 transition-all duration-300 border border-white/20 flex items-center gap-2 font-medium\",\n                        onClick: ()=>setShowEditModal(true),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, undefined),\n                            \"تعديل الكورس\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: togglePublish,\n                        disabled: publishing,\n                        className: \"px-6 py-3 rounded-xl font-medium transition-all duration-300 flex items-center gap-2 \".concat((courseData === null || courseData === void 0 ? void 0 : courseData.is_published) ? \"bg-red-500/20 text-red-100 border border-red-400/30 hover:bg-red-500/30\" : \"bg-green-500/20 text-green-100 border border-green-400/30 hover:bg-green-500/30\", \" \").concat(publishing ? \"opacity-50 cursor-not-allowed\" : \"\"),\n                        children: publishing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 animate-spin\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"جاري التحديث...\"\n                            ]\n                        }, void 0, true) : (courseData === null || courseData === void 0 ? void 0 : courseData.is_published) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"إلغاء النشر\"\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"نشر الكورس\"\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CourseHeader, \"1MU0yVyCAy5D7ioa/SJOw8B9rL8=\");\n_c = CourseHeader;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CourseHeader);\nvar _c;\n$RefreshReg$(_c, \"CourseHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/instructor/CourseHeader.jsx\n"));

/***/ })

});