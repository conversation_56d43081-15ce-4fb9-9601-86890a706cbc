"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/stackblur-canvas";
exports.ids = ["vendor-chunks/stackblur-canvas"];
exports.modules = {

/***/ "(ssr)/./node_modules/stackblur-canvas/dist/stackblur-es.js":
/*!************************************************************!*\
  !*** ./node_modules/stackblur-canvas/dist/stackblur-es.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlurStack: () => (/* binding */ BlurStack),\n/* harmony export */   canvasRGB: () => (/* binding */ processCanvasRGB),\n/* harmony export */   canvasRGBA: () => (/* binding */ processCanvasRGBA),\n/* harmony export */   image: () => (/* binding */ processImage),\n/* harmony export */   imageDataRGB: () => (/* binding */ processImageDataRGB),\n/* harmony export */   imageDataRGBA: () => (/* binding */ processImageDataRGBA)\n/* harmony export */ });\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\n/* eslint-disable no-bitwise -- used for calculations */\n\n/* eslint-disable unicorn/prefer-query-selector -- aiming at\n  backward-compatibility */\n\n/**\n* StackBlur - a fast almost Gaussian Blur For Canvas\n*\n* In case you find this class useful - especially in commercial projects -\n* I am not totally unhappy for a small donation to my PayPal account\n* <EMAIL>\n*\n* Or support me on flattr:\n* {@link https://flattr.com/thing/72791/StackBlur-a-fast-almost-Gaussian-Blur-Effect-for-CanvasJavascript}.\n*\n* @module StackBlur\n* <AUTHOR> Klingemann\n* Contact: <EMAIL>\n* Website: {@link http://www.quasimondo.com/StackBlurForCanvas/StackBlurDemo.html}\n* Twitter: @quasimondo\n*\n* @copyright (c) 2010 Mario Klingemann\n*\n* Permission is hereby granted, free of charge, to any person\n* obtaining a copy of this software and associated documentation\n* files (the \"Software\"), to deal in the Software without\n* restriction, including without limitation the rights to use,\n* copy, modify, merge, publish, distribute, sublicense, and/or sell\n* copies of the Software, and to permit persons to whom the\n* Software is furnished to do so, subject to the following\n* conditions:\n*\n* The above copyright notice and this permission notice shall be\n* included in all copies or substantial portions of the Software.\n*\n* THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n* EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\n* OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n* NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\n* HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\n* WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n* FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\n* OTHER DEALINGS IN THE SOFTWARE.\n*/\nvar mulTable = [512, 512, 456, 512, 328, 456, 335, 512, 405, 328, 271, 456, 388, 335, 292, 512, 454, 405, 364, 328, 298, 271, 496, 456, 420, 388, 360, 335, 312, 292, 273, 512, 482, 454, 428, 405, 383, 364, 345, 328, 312, 298, 284, 271, 259, 496, 475, 456, 437, 420, 404, 388, 374, 360, 347, 335, 323, 312, 302, 292, 282, 273, 265, 512, 497, 482, 468, 454, 441, 428, 417, 405, 394, 383, 373, 364, 354, 345, 337, 328, 320, 312, 305, 298, 291, 284, 278, 271, 265, 259, 507, 496, 485, 475, 465, 456, 446, 437, 428, 420, 412, 404, 396, 388, 381, 374, 367, 360, 354, 347, 341, 335, 329, 323, 318, 312, 307, 302, 297, 292, 287, 282, 278, 273, 269, 265, 261, 512, 505, 497, 489, 482, 475, 468, 461, 454, 447, 441, 435, 428, 422, 417, 411, 405, 399, 394, 389, 383, 378, 373, 368, 364, 359, 354, 350, 345, 341, 337, 332, 328, 324, 320, 316, 312, 309, 305, 301, 298, 294, 291, 287, 284, 281, 278, 274, 271, 268, 265, 262, 259, 257, 507, 501, 496, 491, 485, 480, 475, 470, 465, 460, 456, 451, 446, 442, 437, 433, 428, 424, 420, 416, 412, 408, 404, 400, 396, 392, 388, 385, 381, 377, 374, 370, 367, 363, 360, 357, 354, 350, 347, 344, 341, 338, 335, 332, 329, 326, 323, 320, 318, 315, 312, 310, 307, 304, 302, 299, 297, 294, 292, 289, 287, 285, 282, 280, 278, 275, 273, 271, 269, 267, 265, 263, 261, 259];\nvar shgTable = [9, 11, 12, 13, 13, 14, 14, 15, 15, 15, 15, 16, 16, 16, 16, 17, 17, 17, 17, 17, 17, 17, 18, 18, 18, 18, 18, 18, 18, 18, 18, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24];\n/**\n * @param {string|HTMLImageElement} img\n * @param {string|HTMLCanvasElement} canvas\n * @param {Float} radius\n * @param {boolean} blurAlphaChannel\n * @param {boolean} useOffset\n * @param {boolean} skipStyles\n * @returns {undefined}\n */\n\nfunction processImage(img, canvas, radius, blurAlphaChannel, useOffset, skipStyles) {\n  if (typeof img === 'string') {\n    img = document.getElementById(img);\n  }\n\n  if (!img || Object.prototype.toString.call(img).slice(8, -1) === 'HTMLImageElement' && !('naturalWidth' in img)) {\n    return;\n  }\n\n  var dimensionType = useOffset ? 'offset' : 'natural';\n  var w = img[dimensionType + 'Width'];\n  var h = img[dimensionType + 'Height']; // add ImageBitmap support,can blur texture source\n\n  if (Object.prototype.toString.call(img).slice(8, -1) === 'ImageBitmap') {\n    w = img.width;\n    h = img.height;\n  }\n\n  if (typeof canvas === 'string') {\n    canvas = document.getElementById(canvas);\n  }\n\n  if (!canvas || !('getContext' in canvas)) {\n    return;\n  }\n\n  if (!skipStyles) {\n    canvas.style.width = w + 'px';\n    canvas.style.height = h + 'px';\n  }\n\n  canvas.width = w;\n  canvas.height = h;\n  var context = canvas.getContext('2d');\n  context.clearRect(0, 0, w, h);\n  context.drawImage(img, 0, 0, img.naturalWidth, img.naturalHeight, 0, 0, w, h);\n\n  if (isNaN(radius) || radius < 1) {\n    return;\n  }\n\n  if (blurAlphaChannel) {\n    processCanvasRGBA(canvas, 0, 0, w, h, radius);\n  } else {\n    processCanvasRGB(canvas, 0, 0, w, h, radius);\n  }\n}\n/**\n * @param {string|HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @throws {Error|TypeError}\n * @returns {ImageData} See {@link https://html.spec.whatwg.org/multipage/canvas.html#imagedata}\n */\n\n\nfunction getImageDataFromCanvas(canvas, topX, topY, width, height) {\n  if (typeof canvas === 'string') {\n    canvas = document.getElementById(canvas);\n  }\n\n  if (!canvas || _typeof(canvas) !== 'object' || !('getContext' in canvas)) {\n    throw new TypeError('Expecting canvas with `getContext` method ' + 'in processCanvasRGB(A) calls!');\n  }\n\n  var context = canvas.getContext('2d');\n\n  try {\n    return context.getImageData(topX, topY, width, height);\n  } catch (e) {\n    throw new Error('unable to access image data: ' + e);\n  }\n}\n/**\n * @param {HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {undefined}\n */\n\n\nfunction processCanvasRGBA(canvas, topX, topY, width, height, radius) {\n  if (isNaN(radius) || radius < 1) {\n    return;\n  }\n\n  radius |= 0;\n  var imageData = getImageDataFromCanvas(canvas, topX, topY, width, height);\n  imageData = processImageDataRGBA(imageData, topX, topY, width, height, radius);\n  canvas.getContext('2d').putImageData(imageData, topX, topY);\n}\n/**\n * @param {ImageData} imageData\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {ImageData}\n */\n\n\nfunction processImageDataRGBA(imageData, topX, topY, width, height, radius) {\n  var pixels = imageData.data;\n  var div = 2 * radius + 1; // const w4 = width << 2;\n\n  var widthMinus1 = width - 1;\n  var heightMinus1 = height - 1;\n  var radiusPlus1 = radius + 1;\n  var sumFactor = radiusPlus1 * (radiusPlus1 + 1) / 2;\n  var stackStart = new BlurStack();\n  var stack = stackStart;\n  var stackEnd;\n\n  for (var i = 1; i < div; i++) {\n    stack = stack.next = new BlurStack();\n\n    if (i === radiusPlus1) {\n      stackEnd = stack;\n    }\n  }\n\n  stack.next = stackStart;\n  var stackIn = null,\n      stackOut = null,\n      yw = 0,\n      yi = 0;\n  var mulSum = mulTable[radius];\n  var shgSum = shgTable[radius];\n\n  for (var y = 0; y < height; y++) {\n    stack = stackStart;\n    var pr = pixels[yi],\n        pg = pixels[yi + 1],\n        pb = pixels[yi + 2],\n        pa = pixels[yi + 3];\n\n    for (var _i = 0; _i < radiusPlus1; _i++) {\n      stack.r = pr;\n      stack.g = pg;\n      stack.b = pb;\n      stack.a = pa;\n      stack = stack.next;\n    }\n\n    var rInSum = 0,\n        gInSum = 0,\n        bInSum = 0,\n        aInSum = 0,\n        rOutSum = radiusPlus1 * pr,\n        gOutSum = radiusPlus1 * pg,\n        bOutSum = radiusPlus1 * pb,\n        aOutSum = radiusPlus1 * pa,\n        rSum = sumFactor * pr,\n        gSum = sumFactor * pg,\n        bSum = sumFactor * pb,\n        aSum = sumFactor * pa;\n\n    for (var _i2 = 1; _i2 < radiusPlus1; _i2++) {\n      var p = yi + ((widthMinus1 < _i2 ? widthMinus1 : _i2) << 2);\n      var r = pixels[p],\n          g = pixels[p + 1],\n          b = pixels[p + 2],\n          a = pixels[p + 3];\n      var rbs = radiusPlus1 - _i2;\n      rSum += (stack.r = r) * rbs;\n      gSum += (stack.g = g) * rbs;\n      bSum += (stack.b = b) * rbs;\n      aSum += (stack.a = a) * rbs;\n      rInSum += r;\n      gInSum += g;\n      bInSum += b;\n      aInSum += a;\n      stack = stack.next;\n    }\n\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (var x = 0; x < width; x++) {\n      var paInitial = aSum * mulSum >>> shgSum;\n      pixels[yi + 3] = paInitial;\n\n      if (paInitial !== 0) {\n        var _a2 = 255 / paInitial;\n\n        pixels[yi] = (rSum * mulSum >>> shgSum) * _a2;\n        pixels[yi + 1] = (gSum * mulSum >>> shgSum) * _a2;\n        pixels[yi + 2] = (bSum * mulSum >>> shgSum) * _a2;\n      } else {\n        pixels[yi] = pixels[yi + 1] = pixels[yi + 2] = 0;\n      }\n\n      rSum -= rOutSum;\n      gSum -= gOutSum;\n      bSum -= bOutSum;\n      aSum -= aOutSum;\n      rOutSum -= stackIn.r;\n      gOutSum -= stackIn.g;\n      bOutSum -= stackIn.b;\n      aOutSum -= stackIn.a;\n\n      var _p = x + radius + 1;\n\n      _p = yw + (_p < widthMinus1 ? _p : widthMinus1) << 2;\n      rInSum += stackIn.r = pixels[_p];\n      gInSum += stackIn.g = pixels[_p + 1];\n      bInSum += stackIn.b = pixels[_p + 2];\n      aInSum += stackIn.a = pixels[_p + 3];\n      rSum += rInSum;\n      gSum += gInSum;\n      bSum += bInSum;\n      aSum += aInSum;\n      stackIn = stackIn.next;\n      var _stackOut = stackOut,\n          _r = _stackOut.r,\n          _g = _stackOut.g,\n          _b = _stackOut.b,\n          _a = _stackOut.a;\n      rOutSum += _r;\n      gOutSum += _g;\n      bOutSum += _b;\n      aOutSum += _a;\n      rInSum -= _r;\n      gInSum -= _g;\n      bInSum -= _b;\n      aInSum -= _a;\n      stackOut = stackOut.next;\n      yi += 4;\n    }\n\n    yw += width;\n  }\n\n  for (var _x = 0; _x < width; _x++) {\n    yi = _x << 2;\n\n    var _pr = pixels[yi],\n        _pg = pixels[yi + 1],\n        _pb = pixels[yi + 2],\n        _pa = pixels[yi + 3],\n        _rOutSum = radiusPlus1 * _pr,\n        _gOutSum = radiusPlus1 * _pg,\n        _bOutSum = radiusPlus1 * _pb,\n        _aOutSum = radiusPlus1 * _pa,\n        _rSum = sumFactor * _pr,\n        _gSum = sumFactor * _pg,\n        _bSum = sumFactor * _pb,\n        _aSum = sumFactor * _pa;\n\n    stack = stackStart;\n\n    for (var _i3 = 0; _i3 < radiusPlus1; _i3++) {\n      stack.r = _pr;\n      stack.g = _pg;\n      stack.b = _pb;\n      stack.a = _pa;\n      stack = stack.next;\n    }\n\n    var yp = width;\n    var _gInSum = 0,\n        _bInSum = 0,\n        _aInSum = 0,\n        _rInSum = 0;\n\n    for (var _i4 = 1; _i4 <= radius; _i4++) {\n      yi = yp + _x << 2;\n\n      var _rbs = radiusPlus1 - _i4;\n\n      _rSum += (stack.r = _pr = pixels[yi]) * _rbs;\n      _gSum += (stack.g = _pg = pixels[yi + 1]) * _rbs;\n      _bSum += (stack.b = _pb = pixels[yi + 2]) * _rbs;\n      _aSum += (stack.a = _pa = pixels[yi + 3]) * _rbs;\n      _rInSum += _pr;\n      _gInSum += _pg;\n      _bInSum += _pb;\n      _aInSum += _pa;\n      stack = stack.next;\n\n      if (_i4 < heightMinus1) {\n        yp += width;\n      }\n    }\n\n    yi = _x;\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (var _y = 0; _y < height; _y++) {\n      var _p2 = yi << 2;\n\n      pixels[_p2 + 3] = _pa = _aSum * mulSum >>> shgSum;\n\n      if (_pa > 0) {\n        _pa = 255 / _pa;\n        pixels[_p2] = (_rSum * mulSum >>> shgSum) * _pa;\n        pixels[_p2 + 1] = (_gSum * mulSum >>> shgSum) * _pa;\n        pixels[_p2 + 2] = (_bSum * mulSum >>> shgSum) * _pa;\n      } else {\n        pixels[_p2] = pixels[_p2 + 1] = pixels[_p2 + 2] = 0;\n      }\n\n      _rSum -= _rOutSum;\n      _gSum -= _gOutSum;\n      _bSum -= _bOutSum;\n      _aSum -= _aOutSum;\n      _rOutSum -= stackIn.r;\n      _gOutSum -= stackIn.g;\n      _bOutSum -= stackIn.b;\n      _aOutSum -= stackIn.a;\n      _p2 = _x + ((_p2 = _y + radiusPlus1) < heightMinus1 ? _p2 : heightMinus1) * width << 2;\n      _rSum += _rInSum += stackIn.r = pixels[_p2];\n      _gSum += _gInSum += stackIn.g = pixels[_p2 + 1];\n      _bSum += _bInSum += stackIn.b = pixels[_p2 + 2];\n      _aSum += _aInSum += stackIn.a = pixels[_p2 + 3];\n      stackIn = stackIn.next;\n      _rOutSum += _pr = stackOut.r;\n      _gOutSum += _pg = stackOut.g;\n      _bOutSum += _pb = stackOut.b;\n      _aOutSum += _pa = stackOut.a;\n      _rInSum -= _pr;\n      _gInSum -= _pg;\n      _bInSum -= _pb;\n      _aInSum -= _pa;\n      stackOut = stackOut.next;\n      yi += width;\n    }\n  }\n\n  return imageData;\n}\n/**\n * @param {HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {undefined}\n */\n\n\nfunction processCanvasRGB(canvas, topX, topY, width, height, radius) {\n  if (isNaN(radius) || radius < 1) {\n    return;\n  }\n\n  radius |= 0;\n  var imageData = getImageDataFromCanvas(canvas, topX, topY, width, height);\n  imageData = processImageDataRGB(imageData, topX, topY, width, height, radius);\n  canvas.getContext('2d').putImageData(imageData, topX, topY);\n}\n/**\n * @param {ImageData} imageData\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {ImageData}\n */\n\n\nfunction processImageDataRGB(imageData, topX, topY, width, height, radius) {\n  var pixels = imageData.data;\n  var div = 2 * radius + 1; // const w4 = width << 2;\n\n  var widthMinus1 = width - 1;\n  var heightMinus1 = height - 1;\n  var radiusPlus1 = radius + 1;\n  var sumFactor = radiusPlus1 * (radiusPlus1 + 1) / 2;\n  var stackStart = new BlurStack();\n  var stack = stackStart;\n  var stackEnd;\n\n  for (var i = 1; i < div; i++) {\n    stack = stack.next = new BlurStack();\n\n    if (i === radiusPlus1) {\n      stackEnd = stack;\n    }\n  }\n\n  stack.next = stackStart;\n  var stackIn = null;\n  var stackOut = null;\n  var mulSum = mulTable[radius];\n  var shgSum = shgTable[radius];\n  var p, rbs;\n  var yw = 0,\n      yi = 0;\n\n  for (var y = 0; y < height; y++) {\n    var pr = pixels[yi],\n        pg = pixels[yi + 1],\n        pb = pixels[yi + 2],\n        rOutSum = radiusPlus1 * pr,\n        gOutSum = radiusPlus1 * pg,\n        bOutSum = radiusPlus1 * pb,\n        rSum = sumFactor * pr,\n        gSum = sumFactor * pg,\n        bSum = sumFactor * pb;\n    stack = stackStart;\n\n    for (var _i5 = 0; _i5 < radiusPlus1; _i5++) {\n      stack.r = pr;\n      stack.g = pg;\n      stack.b = pb;\n      stack = stack.next;\n    }\n\n    var rInSum = 0,\n        gInSum = 0,\n        bInSum = 0;\n\n    for (var _i6 = 1; _i6 < radiusPlus1; _i6++) {\n      p = yi + ((widthMinus1 < _i6 ? widthMinus1 : _i6) << 2);\n      rSum += (stack.r = pr = pixels[p]) * (rbs = radiusPlus1 - _i6);\n      gSum += (stack.g = pg = pixels[p + 1]) * rbs;\n      bSum += (stack.b = pb = pixels[p + 2]) * rbs;\n      rInSum += pr;\n      gInSum += pg;\n      bInSum += pb;\n      stack = stack.next;\n    }\n\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (var x = 0; x < width; x++) {\n      pixels[yi] = rSum * mulSum >>> shgSum;\n      pixels[yi + 1] = gSum * mulSum >>> shgSum;\n      pixels[yi + 2] = bSum * mulSum >>> shgSum;\n      rSum -= rOutSum;\n      gSum -= gOutSum;\n      bSum -= bOutSum;\n      rOutSum -= stackIn.r;\n      gOutSum -= stackIn.g;\n      bOutSum -= stackIn.b;\n      p = yw + ((p = x + radius + 1) < widthMinus1 ? p : widthMinus1) << 2;\n      rInSum += stackIn.r = pixels[p];\n      gInSum += stackIn.g = pixels[p + 1];\n      bInSum += stackIn.b = pixels[p + 2];\n      rSum += rInSum;\n      gSum += gInSum;\n      bSum += bInSum;\n      stackIn = stackIn.next;\n      rOutSum += pr = stackOut.r;\n      gOutSum += pg = stackOut.g;\n      bOutSum += pb = stackOut.b;\n      rInSum -= pr;\n      gInSum -= pg;\n      bInSum -= pb;\n      stackOut = stackOut.next;\n      yi += 4;\n    }\n\n    yw += width;\n  }\n\n  for (var _x2 = 0; _x2 < width; _x2++) {\n    yi = _x2 << 2;\n\n    var _pr2 = pixels[yi],\n        _pg2 = pixels[yi + 1],\n        _pb2 = pixels[yi + 2],\n        _rOutSum2 = radiusPlus1 * _pr2,\n        _gOutSum2 = radiusPlus1 * _pg2,\n        _bOutSum2 = radiusPlus1 * _pb2,\n        _rSum2 = sumFactor * _pr2,\n        _gSum2 = sumFactor * _pg2,\n        _bSum2 = sumFactor * _pb2;\n\n    stack = stackStart;\n\n    for (var _i7 = 0; _i7 < radiusPlus1; _i7++) {\n      stack.r = _pr2;\n      stack.g = _pg2;\n      stack.b = _pb2;\n      stack = stack.next;\n    }\n\n    var _rInSum2 = 0,\n        _gInSum2 = 0,\n        _bInSum2 = 0;\n\n    for (var _i8 = 1, yp = width; _i8 <= radius; _i8++) {\n      yi = yp + _x2 << 2;\n      _rSum2 += (stack.r = _pr2 = pixels[yi]) * (rbs = radiusPlus1 - _i8);\n      _gSum2 += (stack.g = _pg2 = pixels[yi + 1]) * rbs;\n      _bSum2 += (stack.b = _pb2 = pixels[yi + 2]) * rbs;\n      _rInSum2 += _pr2;\n      _gInSum2 += _pg2;\n      _bInSum2 += _pb2;\n      stack = stack.next;\n\n      if (_i8 < heightMinus1) {\n        yp += width;\n      }\n    }\n\n    yi = _x2;\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (var _y2 = 0; _y2 < height; _y2++) {\n      p = yi << 2;\n      pixels[p] = _rSum2 * mulSum >>> shgSum;\n      pixels[p + 1] = _gSum2 * mulSum >>> shgSum;\n      pixels[p + 2] = _bSum2 * mulSum >>> shgSum;\n      _rSum2 -= _rOutSum2;\n      _gSum2 -= _gOutSum2;\n      _bSum2 -= _bOutSum2;\n      _rOutSum2 -= stackIn.r;\n      _gOutSum2 -= stackIn.g;\n      _bOutSum2 -= stackIn.b;\n      p = _x2 + ((p = _y2 + radiusPlus1) < heightMinus1 ? p : heightMinus1) * width << 2;\n      _rSum2 += _rInSum2 += stackIn.r = pixels[p];\n      _gSum2 += _gInSum2 += stackIn.g = pixels[p + 1];\n      _bSum2 += _bInSum2 += stackIn.b = pixels[p + 2];\n      stackIn = stackIn.next;\n      _rOutSum2 += _pr2 = stackOut.r;\n      _gOutSum2 += _pg2 = stackOut.g;\n      _bOutSum2 += _pb2 = stackOut.b;\n      _rInSum2 -= _pr2;\n      _gInSum2 -= _pg2;\n      _bInSum2 -= _pb2;\n      stackOut = stackOut.next;\n      yi += width;\n    }\n  }\n\n  return imageData;\n}\n/**\n *\n */\n\n\nvar BlurStack =\n/**\n * Set properties.\n */\nfunction BlurStack() {\n  _classCallCheck(this, BlurStack);\n\n  this.r = 0;\n  this.g = 0;\n  this.b = 0;\n  this.a = 0;\n  this.next = null;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/stackblur-canvas/dist/stackblur-es.js\n");

/***/ })

};
;