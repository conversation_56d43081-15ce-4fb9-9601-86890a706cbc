"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(pages)/instructor/dashboard/[id]/page",{

/***/ "(app-pages-browser)/./src/components/instructor/CourseHeader.jsx":
/*!****************************************************!*\
  !*** ./src/components/instructor/CourseHeader.jsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _config_environment__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/environment */ \"(app-pages-browser)/./src/config/environment.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst CourseHeader = (param)=>{\n    let { courseId, courseData, publishing, togglePublish, setShowEditModal } = param;\n    var _courseData_category;\n    _s();\n    // إضافة state لإدارة حالة المشاركة - zakialkholy\n    const [shareLoading, setShareLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [shareSuccess, setShareSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // دالة مشاركة رابط الكورس للطلبة - zakialkholy\n    const handleShareCourse = async ()=>{\n        if (!(courseData === null || courseData === void 0 ? void 0 : courseData.slug)) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"لا يمكن مشاركة الكورس - البيانات غير مكتملة\");\n            return;\n        }\n        setShareLoading(true);\n        try {\n            // بناء رابط الكورس للطلبة - zakialkholy\n            const frontendUrl = _config_environment__WEBPACK_IMPORTED_MODULE_4__.ENV.FRONTEND_URL || window.location.origin;\n            const courseUrl = \"\".concat(frontendUrl, \"/student/course/\").concat(courseData.slug);\n            // نسخ الرابط للحافظة - zakialkholy\n            if (navigator.clipboard && window.isSecureContext) {\n                await navigator.clipboard.writeText(courseUrl);\n            } else {\n                // fallback للمتصفحات القديمة - zakialkholy\n                const textArea = document.createElement(\"textarea\");\n                textArea.value = courseUrl;\n                document.body.appendChild(textArea);\n                textArea.focus();\n                textArea.select();\n                document.execCommand('copy');\n                document.body.removeChild(textArea);\n            }\n            // إظهار رسالة نجاح - zakialkholy\n            setShareSuccess(true);\n            setTimeout(()=>setShareSuccess(false), 3000);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"تم نسخ رابط الكورس بنجاح! يمكنك الآن مشاركته مع الطلبة\");\n        } catch (error) {\n            console.error(\"خطأ في نسخ الرابط:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"حدث خطأ أثناء نسخ الرابط. يرجى المحاولة مرة أخرى.\");\n        } finally{\n            setShareLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl lg:text-4xl font-bold text-white\",\n                                children: (courseData === null || courseData === void 0 ? void 0 : courseData.title) || \"تفاصيل الكورس\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-3 py-1 rounded-full text-sm font-medium \".concat((courseData === null || courseData === void 0 ? void 0 : courseData.is_published) ? \"bg-green-500/20 text-green-100 border border-green-400/30\" : \"bg-yellow-500/20 text-yellow-100 border border-yellow-400/30\"),\n                                children: (courseData === null || courseData === void 0 ? void 0 : courseData.is_published) ? \"منشور\" : \"مسودة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/80 text-lg max-w-2xl\",\n                        children: (courseData === null || courseData === void 0 ? void 0 : courseData.short_description) || (courseData === null || courseData === void 0 ? void 0 : courseData.description) || \"إدارة وتحرير محتوى الكورس\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-6 mt-4 text-white/70\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            (courseData === null || courseData === void 0 ? void 0 : courseData.students_count) || 0,\n                                            \" طالب\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: (courseData === null || courseData === void 0 ? void 0 : courseData.rating) || \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: (courseData === null || courseData === void 0 ? void 0 : (_courseData_category = courseData.category) === null || _courseData_category === void 0 ? void 0 : _courseData_category.name) || \"غير محدد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleShareCourse,\n                        disabled: shareLoading || !(courseData === null || courseData === void 0 ? void 0 : courseData.slug),\n                        className: \"bg-white/10 backdrop-blur-sm text-white px-6 py-3 rounded-xl hover:bg-white/20 transition-all duration-300 border border-white/20 flex items-center gap-2 font-medium \".concat(shareLoading ? \"opacity-50 cursor-not-allowed\" : \"\", \" \").concat(shareSuccess ? \"bg-green-500/20 border-green-400/30\" : \"\"),\n                        title: \"نسخ رابط الكورس لمشاركته مع الطلبة\",\n                        children: shareLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 animate-spin\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"جاري النسخ...\"\n                            ]\n                        }, void 0, true) : shareSuccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M5 13l4 4L19 7\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"تم النسخ!\"\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"مشاركة الكورس\"\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/instructor/dashboard/\".concat(courseId, \"/add-lesson\"),\n                        className: \"bg-white/10 backdrop-blur-sm text-white px-6 py-3 rounded-xl hover:bg-white/20 transition-all duration-300 border border-white/20 flex items-center gap-2 font-medium\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, undefined),\n                            \"إضافة درس جديد\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"bg-white/10 backdrop-blur-sm text-white px-6 py-3 rounded-xl hover:bg-white/20 transition-all duration-300 border border-white/20 flex items-center gap-2 font-medium\",\n                        onClick: ()=>setShowEditModal(true),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, undefined),\n                            \"تعديل الكورس\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: togglePublish,\n                        disabled: publishing,\n                        className: \"px-6 py-3 rounded-xl font-medium transition-all duration-300 flex items-center gap-2 \".concat((courseData === null || courseData === void 0 ? void 0 : courseData.is_published) ? \"bg-red-500/20 text-red-100 border border-red-400/30 hover:bg-red-500/30\" : \"bg-green-500/20 text-green-100 border border-green-400/30 hover:bg-green-500/30\", \" \").concat(publishing ? \"opacity-50 cursor-not-allowed\" : \"\"),\n                        children: publishing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 animate-spin\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"جاري التحديث...\"\n                            ]\n                        }, void 0, true) : (courseData === null || courseData === void 0 ? void 0 : courseData.is_published) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"إلغاء النشر\"\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"نشر الكورس\"\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\CourseHeader.jsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CourseHeader, \"qQO3yjIZCNvibPwKkZ/sGi7BtXM=\");\n_c = CourseHeader;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CourseHeader);\nvar _c;\n$RefreshReg$(_c, \"CourseHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/instructor/CourseHeader.jsx\n"));

/***/ })

});