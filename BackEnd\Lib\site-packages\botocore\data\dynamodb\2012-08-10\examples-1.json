{"version": "1.0", "examples": {"BatchGetItem": [{"input": {"RequestItems": {"Music": {"Keys": [{"Artist": {"S": "No One You Know"}, "SongTitle": {"S": "Call Me Today"}}, {"Artist": {"S": "Acme Band"}, "SongTitle": {"S": "Happy Day"}}, {"Artist": {"S": "No One You Know"}, "SongTitle": {"S": "Scared of My Shadow"}}], "ProjectionExpression": "AlbumTitle"}}}, "output": {"Responses": {"Music": [{"AlbumTitle": {"S": "Somewhat Famous"}}, {"AlbumTitle": {"S": "Blue Sky Blues"}}, {"AlbumTitle": {"S": "Louder Than Ever"}}]}}, "comments": {"input": {}, "output": {}}, "description": "This example reads multiple items from the Music table using a batch of three GetItem requests.  Only the AlbumTitle attribute is returned.", "id": "to-retrieve-multiple-items-from-a-table-1476118438992", "title": "To retrieve multiple items from a table"}], "BatchWriteItem": [{"input": {"RequestItems": {"Music": [{"PutRequest": {"Item": {"AlbumTitle": {"S": "Somewhat Famous"}, "Artist": {"S": "No One You Know"}, "SongTitle": {"S": "Call Me Today"}}}}, {"PutRequest": {"Item": {"AlbumTitle": {"S": "Songs About Life"}, "Artist": {"S": "Acme Band"}, "SongTitle": {"S": "Happy Day"}}}}, {"PutRequest": {"Item": {"AlbumTitle": {"S": "Blue Sky Blues"}, "Artist": {"S": "No One You Know"}, "SongTitle": {"S": "Scared of My Shadow"}}}}]}}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example adds three new items to the Music table using a batch of three PutItem requests.", "id": "to-add-multiple-items-to-a-table-1476118519747", "title": "To add multiple items to a table"}], "CreateTable": [{"input": {"AttributeDefinitions": [{"AttributeName": "Artist", "AttributeType": "S"}, {"AttributeName": "Song<PERSON><PERSON>le", "AttributeType": "S"}], "KeySchema": [{"AttributeName": "Artist", "KeyType": "HASH"}, {"AttributeName": "Song<PERSON><PERSON>le", "KeyType": "RANGE"}], "ProvisionedThroughput": {"ReadCapacityUnits": 5, "WriteCapacityUnits": 5}, "TableName": "Music"}, "output": {"TableDescription": {"AttributeDefinitions": [{"AttributeName": "Artist", "AttributeType": "S"}, {"AttributeName": "Song<PERSON><PERSON>le", "AttributeType": "S"}], "CreationDateTime": "**********.062", "ItemCount": 0, "KeySchema": [{"AttributeName": "Artist", "KeyType": "HASH"}, {"AttributeName": "Song<PERSON><PERSON>le", "KeyType": "RANGE"}], "ProvisionedThroughput": {"ReadCapacityUnits": 5, "WriteCapacityUnits": 5}, "TableName": "Music", "TableSizeBytes": 0, "TableStatus": "CREATING"}}, "comments": {"input": {}, "output": {}}, "description": "This example creates a table named Music.", "id": "to-create-a-table-1476116291743", "title": "To create a table"}], "DeleteItem": [{"input": {"Key": {"Artist": {"S": "No One You Know"}, "SongTitle": {"S": "Scared of My Shadow"}}, "TableName": "Music"}, "output": {"ConsumedCapacity": {"CapacityUnits": 1, "TableName": "Music"}}, "comments": {"input": {}, "output": {}}, "description": "This example deletes an item from the Music table.", "id": "to-delete-an-item-1475884573758", "title": "To delete an item"}], "DeleteTable": [{"input": {"TableName": "Music"}, "output": {"TableDescription": {"ItemCount": 0, "ProvisionedThroughput": {"NumberOfDecreasesToday": 1, "ReadCapacityUnits": 5, "WriteCapacityUnits": 5}, "TableName": "Music", "TableSizeBytes": 0, "TableStatus": "DELETING"}}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the Music table.", "id": "to-delete-a-table-*************", "title": "To delete a table"}], "DescribeLimits": [{"input": {}, "output": {"AccountMaxReadCapacityUnits": 20000, "AccountMaxWriteCapacityUnits": 20000, "TableMaxReadCapacityUnits": 10000, "TableMaxWriteCapacityUnits": 10000}, "comments": {"input": {}, "output": {}}, "description": "The following example returns the maximum read and write capacity units per table, and for the AWS account, in the current AWS region.", "id": "to-determine-capacity-limits-per-table-and-account-in-the-current-aws-region-*************", "title": "To determine capacity limits per table and account, in the current AWS region"}], "DescribeTable": [{"input": {"TableName": "Music"}, "output": {"Table": {"AttributeDefinitions": [{"AttributeName": "Artist", "AttributeType": "S"}, {"AttributeName": "Song<PERSON><PERSON>le", "AttributeType": "S"}], "CreationDateTime": "**********.062", "ItemCount": 0, "KeySchema": [{"AttributeName": "Artist", "KeyType": "HASH"}, {"AttributeName": "Song<PERSON><PERSON>le", "KeyType": "RANGE"}], "ProvisionedThroughput": {"NumberOfDecreasesToday": 1, "ReadCapacityUnits": 5, "WriteCapacityUnits": 5}, "TableName": "Music", "TableSizeBytes": 0, "TableStatus": "ACTIVE"}}, "comments": {"input": {}, "output": {}}, "description": "This example describes the Music table.", "id": "to-describe-a-table-*************", "title": "To describe a table"}], "GetItem": [{"input": {"Key": {"Artist": {"S": "Acme Band"}, "SongTitle": {"S": "Happy Day"}}, "TableName": "Music"}, "output": {"Item": {"AlbumTitle": {"S": "Songs About Life"}, "Artist": {"S": "Acme Band"}, "SongTitle": {"S": "Happy Day"}}}, "comments": {"input": {}, "output": {}}, "description": "This example retrieves an item from the Music table. The table has a partition key and a sort key (<PERSON> and <PERSON>Title), so you must specify both of these attributes.", "id": "to-read-an-item-from-a-table-*************", "title": "To read an item from a table"}], "ListTables": [{"input": {}, "output": {"TableNames": ["Forum", "ProductCatalog", "Reply", "<PERSON><PERSON><PERSON>"]}, "comments": {"input": {}, "output": {}}, "description": "This example lists all of the tables associated with the current AWS account and endpoint.", "id": "to-list-tables-*************", "title": "To list tables"}], "PutItem": [{"input": {"Item": {"AlbumTitle": {"S": "Somewhat Famous"}, "Artist": {"S": "No One You Know"}, "SongTitle": {"S": "Call Me Today"}}, "ReturnConsumedCapacity": "TOTAL", "TableName": "Music"}, "output": {"ConsumedCapacity": {"CapacityUnits": 1, "TableName": "Music"}}, "comments": {"input": {}, "output": {}}, "description": "This example adds a new item to the Music table.", "id": "to-add-an-item-to-a-table-*************", "title": "To add an item to a table"}], "Query": [{"input": {"ExpressionAttributeValues": {":v1": {"S": "No One You Know"}}, "KeyConditionExpression": "Artist = :v1", "ProjectionExpression": "Song<PERSON><PERSON>le", "TableName": "Music"}, "output": {"ConsumedCapacity": {}, "Count": 2, "Items": [{"SongTitle": {"S": "Call Me Today"}}], "ScannedCount": 2}, "comments": {"input": {}, "output": {}}, "description": "This example queries items in the Music table. The table has a partition key and sort key (<PERSON> and SongTitle), but this query only specifies the partition key value. It returns song titles by the artist named \"No One You Know\".", "id": "to-query-an-item-1475883874631", "title": "To query an item"}], "Scan": [{"input": {"ExpressionAttributeNames": {"#AT": "AlbumTitle", "#ST": "Song<PERSON><PERSON>le"}, "ExpressionAttributeValues": {":a": {"S": "No One You Know"}}, "FilterExpression": "Artist = :a", "ProjectionExpression": "#ST, #AT", "TableName": "Music"}, "output": {"ConsumedCapacity": {}, "Count": 2, "Items": [{"AlbumTitle": {"S": "Somewhat Famous"}, "SongTitle": {"S": "Call Me Today"}}, {"AlbumTitle": {"S": "Blue Sky Blues"}, "SongTitle": {"S": "Scared of My Shadow"}}], "ScannedCount": 3}, "comments": {"input": {}, "output": {}}, "description": "This example scans the entire Music table, and then narrows the results to songs by the artist \"No One You Know\". For each item, only the album title and song title are returned.", "id": "to-scan-a-table-1475883652470", "title": "To scan a table"}], "UpdateItem": [{"input": {"ExpressionAttributeNames": {"#AT": "AlbumTitle", "#Y": "Year"}, "ExpressionAttributeValues": {":t": {"S": "Louder Than Ever"}, ":y": {"N": "2015"}}, "Key": {"Artist": {"S": "Acme Band"}, "SongTitle": {"S": "Happy Day"}}, "ReturnValues": "ALL_NEW", "TableName": "Music", "UpdateExpression": "SET #Y = :y, #AT = :t"}, "output": {"Attributes": {"AlbumTitle": {"S": "Louder Than Ever"}, "Artist": {"S": "Acme Band"}, "SongTitle": {"S": "Happy Day"}, "Year": {"N": "2015"}}}, "comments": {"input": {}, "output": {}}, "description": "This example updates an item in the Music table. It adds a new attribute (Year) and modifies the AlbumTitle attribute.  All of the attributes in the item, as they appear after the update, are returned in the response.", "id": "to-update-an-item-in-a-table-1476118250055", "title": "To update an item in a table"}], "UpdateTable": [{"input": {"ProvisionedThroughput": {"ReadCapacityUnits": 10, "WriteCapacityUnits": 10}, "TableName": "MusicCollection"}, "output": {"TableDescription": {"AttributeDefinitions": [{"AttributeName": "Artist", "AttributeType": "S"}, {"AttributeName": "Song<PERSON><PERSON>le", "AttributeType": "S"}], "CreationDateTime": "**********.062", "ItemCount": 0, "KeySchema": [{"AttributeName": "Artist", "KeyType": "HASH"}, {"AttributeName": "Song<PERSON><PERSON>le", "KeyType": "RANGE"}], "ProvisionedThroughput": {"LastIncreaseDateTime": "1421874759.194", "NumberOfDecreasesToday": 1, "ReadCapacityUnits": 1, "WriteCapacityUnits": 1}, "TableName": "MusicCollection", "TableSizeBytes": 0, "TableStatus": "UPDATING"}}, "comments": {"input": {}, "output": {}}, "description": "This example increases the provisioned read and write capacity on the Music table.", "id": "to-modify-a-tables-provisioned-throughput-1476118076147", "title": "To modify a table's provisioned throughput"}]}}