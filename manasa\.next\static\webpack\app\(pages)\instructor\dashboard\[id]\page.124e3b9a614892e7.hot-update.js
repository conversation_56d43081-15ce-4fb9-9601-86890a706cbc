"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(pages)/instructor/dashboard/[id]/page",{

/***/ "(app-pages-browser)/./src/components/instructor/LessonList.jsx":
/*!**************************************************!*\
  !*** ./src/components/instructor/LessonList.jsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_Components_course_QuizEditModal__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/_Components/course/QuizEditModal */ \"(app-pages-browser)/./src/app/_Components/course/QuizEditModal.jsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _contexts_CourseContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/CourseContext */ \"(app-pages-browser)/./src/contexts/CourseContext.jsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n/**\r\n * قائمة الدروس في صفحة تفاصيل الكورس للمدرب\r\n * @param {Object} props\r\n * @param {Array} props.lessons - قائمة الدروس\r\n * @param {number|null} props.expandedLesson - معرف الدرس المفتوح\r\n * @param {Function} props.setExpandedLesson - دالة لتغيير الدرس المفتوح\r\n * @param {Function} props.handleSeeLesson - دالة للانتقال لصفحة الدرس\r\n * @param {Function} props.handleDeleteLesson - دالة حذف الدرس\r\n * @param {Object} props.deletingResource - حالة حذف ملف الموارد لكل درس\r\n * @param {Function} props.handleDeleteResource - دالة حذف ملف الموارد\r\n * @param {Function} props.setShowFileForm - دالة إظهار فورم رفع ملف\r\n * @param {Object} props.showFileForm - حالة إظهار فورم رفع ملف لكل درس\r\n * @param {Function} props.setShowQuizModal - دالة إظهار فورم إضافة كويز\r\n * @param {Object} props.showQuizModal - حالة إظهار فورم إضافة كويز لكل درس\r\n * @param {Function} props.setQuizForm - دالة تحديث فورم الكويز\r\n * @param {Object} props.quizForm - بيانات فورم الكويز لكل درس\r\n * @param {Function} props.handleAddQuiz - دالة إضافة كويز\r\n * @param {Function} props.handleDeleteQuiz - دالة حذف كويز\r\n * @param {Function} props.setQuizEditForm - دالة تعديل بيانات الكويز\r\n * @param {Object} props.quizEditForm - بيانات تعديل الكويز\r\n * @param {Function} props.handleEditQuiz - دالة تعديل الكويز\r\n * @param {Function} props.handleAddFile - دالة رفع ملف\r\n * @param {Object} props.isUploading - حالة رفع الملف لكل درس\r\n * @param {Object} props.uploadProgress - تقدم رفع الملف لكل درس\r\n * @param {Object} props.uploadedFile - الملف المرفوع لكل درس\r\n * @param {Object} props.uploadError - خطأ رفع الملف لكل درس\r\n * @param {Object} props.uploadSuccess - نجاح رفع الملف لكل درس\r\n * @param {Object} props.fileInputRefs - مراجع حقول الملفات\r\n * @param {Component} props.QuizFormModal - مكون فورم الكويز\r\n * @param {Component} props.FileUploadModal - مكون رفع الملف\r\n * @param {Function} props.generateQuizPDF - دالة توليد PDF للامتحان\r\n * @param {Object} props.user - بيانات المستخدم الحالي\r\n * @param {Function} props.setLessons - دالة تحديث قائمة الدروس\r\n * @param {string} props.courseId - معرف الكورس\r\n *\r\n * يمكن تمرير المزيد من props حسب الحاجة.\r\n */ const LessonList = ()=>{\n    _s();\n    // استخدام Context بدلاً من Props\n    const { // Course Data\n    courseData, courseId, user, // Lessons\n    lessons = [], setLessons, expandedLesson, setExpandedLesson, expandedQuizzes = {}, setExpandedQuizzes, // Navigation & Actions\n    handleSeeLesson, handleDeleteLesson, // File Upload\n    handleAddFile, handleDeleteResource, deletingResource = {}, showFileForm = {}, setShowFileForm, fileInputRefs, isUploading = {}, uploadProgress = {}, uploadSuccess = {}, uploadError = {}, uploadedFile = {}, // Quiz Management\n    showQuizModal = {}, setShowQuizModal, quizForm = {}, setQuizForm, handleAddQuiz, handleDeleteQuiz, handleEditQuiz, quizEditForm, setQuizEditForm, // Question Management\n    showQuestionForm = {}, setShowQuestionForm, questionForm = {}, setQuestionForm, answersForm = {}, setAnswersForm, questionLoading = {}, handleAddQuestion, handleDeleteQuestion, handleEditQuestion, questionEditForm, setQuestionEditForm, // Image Management\n    croppedImages = {}, setCroppedImages, questionImages = {}, setQuestionImages, imagePreview = {}, setImagePreview, showImageCropper = {}, setShowImageCropper, removeQuestionImage, getCroppedImg, // PDF Generation\n    generateQuizPDF, // Components\n    QuestionFormModal, QuizFormModal, FileUploadModal } = (0,_contexts_CourseContext__WEBPACK_IMPORTED_MODULE_4__.useCourseContext)();\n    if (!lessons.length) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-2xl flex items-center justify-center mx-auto mb-6 border border-blue-200 dark:border-blue-700/50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-16 h-16 text-blue-500 dark:text-blue-400\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 1.5,\n                            d: \"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-2xl font-bold text-gray-900 dark:text-white mb-3\",\n                    children: \"لا توجد دروس بعد\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto\",\n                    children: \"ابدأ بإضافة أول درس للكورس وشارك معرفتك مع الطلاب\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-flex items-center px-4 py-2 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded-lg border border-blue-200 dark:border-blue-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 ml-2\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 4v16m8-8H4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium\",\n                            children: \"أضف درسك الأول\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: lessons.map((lesson, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"group bg-white dark:bg-gray-800 rounded-3xl shadow-lg hover:shadow-2xl border border-gray-200 dark:border-gray-700 transition-all duration-500 transform hover:-translate-y-1 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-6 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-16 h-16 bg-gradient-to-br from-blue-500 via-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg group-hover:shadow-xl transition-all duration-300\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xl\",\n                                                                children: lesson.order\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-3 h-3 text-white\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                lineNumber: 185,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-2xl font-bold text-gray-900 dark:text-white truncate\",\n                                                                    children: lesson.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"px-4 py-2 rounded-xl text-sm font-semibold shadow-sm \".concat(lesson.lesson_type === \"video\" ? \"bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-700\" : \"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700\"),\n                                                                    children: lesson.lesson_type === \"video\" ? \"📹 فيديو\" : \"📝 نصي\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        lesson.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 dark:text-gray-400 text-base mb-4 line-clamp-2 leading-relaxed\",\n                                                            children: lesson.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-6 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 px-3 py-2 bg-gray-50 dark:bg-gray-700/50 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-blue-500 dark:text-blue-400\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                lineNumber: 232,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                            lineNumber: 226,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-gray-700 dark:text-gray-300\",\n                                                                            children: lesson.duration || \"غير محدد\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                            lineNumber: 239,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                    lineNumber: 225,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                lesson.quizzes && lesson.quizzes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 px-3 py-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-purple-500 dark:text-purple-400\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                lineNumber: 251,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                            lineNumber: 245,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-purple-700 dark:text-purple-300\",\n                                                                            children: [\n                                                                                lesson.quizzes.length,\n                                                                                \" امتحان/واجب\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                            lineNumber: 258,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                    lineNumber: 244,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                lesson.resources && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 px-3 py-2 bg-green-50 dark:bg-green-900/20 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-green-500 dark:text-green-400\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                lineNumber: 271,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                            lineNumber: 265,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-green-700 dark:text-green-300\",\n                                                                            children: \"ملف مرفق\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                            lineNumber: 278,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3 flex-shrink-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleSeeLesson(lesson.id),\n                                                    className: \"group flex items-center gap-2 px-4 py-3 text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300 bg-indigo-50 dark:bg-indigo-900/20 hover:bg-indigo-100 dark:hover:bg-indigo-900/30 rounded-xl transition-all duration-300 shadow-sm hover:shadow-md\",\n                                                    title: \"عرض الدرس\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 group-hover:scale-110 transition-transform duration-200\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium hidden sm:block\",\n                                                            children: \"عرض\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setExpandedLesson(expandedLesson === lesson.id ? null : lesson.id),\n                                                    className: \"group flex items-center gap-2 px-4 py-3 text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-xl transition-all duration-300 shadow-sm hover:shadow-md\",\n                                                    title: \"إدارة المحتوى\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 transform transition-all duration-300 group-hover:scale-110 \".concat(expandedLesson === lesson.id ? \"rotate-180\" : \"\"),\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M19 9l-7 7-7-7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium hidden sm:block\",\n                                                            children: \"إدارة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteLesson(lesson.id),\n                                                    className: \"group flex items-center gap-2 px-4 py-3 text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-xl transition-all duration-300 shadow-sm hover:shadow-md\",\n                                                    title: \"حذف الدرس\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 group-hover:scale-110 transition-transform duration-200\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium hidden sm:block\",\n                                                            children: \"حذف\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                lineNumber: 174,\n                                columnNumber: 13\n                            }, undefined),\n                            expandedLesson === lesson.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-gray-200 dark:border-gray-700 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800/50 dark:to-gray-900/50 p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"group flex items-center justify-center gap-4 bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 hover:from-blue-600 hover:via-blue-700 hover:to-indigo-700 text-white px-8 py-4 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-2xl transform hover:-translate-y-1\",\n                                                onClick: ()=>{\n                                                    console.log(\"اضغط إضافة امتحان\", lesson.id);\n                                                    setShowQuizModal((prev)=>({\n                                                            ...prev,\n                                                            [lesson.id]: \"exam\"\n                                                        }));\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors duration-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg\",\n                                                        children: \"إضافة امتحان\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"group flex items-center justify-center gap-4 bg-gradient-to-r from-green-500 via-green-600 to-emerald-600 hover:from-green-600 hover:via-green-700 hover:to-emerald-700 text-white px-8 py-4 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-2xl transform hover:-translate-y-1\",\n                                                onClick: ()=>{\n                                                    console.log(\"اضغط إضافة واجب\", lesson.id);\n                                                    setShowQuizModal((prev)=>({\n                                                            ...prev,\n                                                            [lesson.id]: \"assignment\"\n                                                        }));\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors duration-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg\",\n                                                        children: \"إضافة واجب\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"group flex items-center justify-center gap-4 bg-gradient-to-r from-purple-500 via-purple-600 to-violet-600 hover:from-purple-600 hover:via-purple-700 hover:to-violet-700 text-white px-8 py-4 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-2xl transform hover:-translate-y-1\",\n                                                onClick: ()=>{\n                                                    console.log(\"اضغط رفع ملف\", lesson.id);\n                                                    setShowFileForm((prev)=>({\n                                                            ...prev,\n                                                            [lesson.id]: true\n                                                        }));\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors duration-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg\",\n                                                        children: \"رفع ملف\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    lesson.resources && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-700\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: lesson.resources,\n                                                    target: \"_blank\",\n                                                    rel: \"noopener noreferrer\",\n                                                    className: \"flex items-center gap-3 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-semibold transition-colors duration-200 group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-blue-100 dark:bg-blue-800/50 rounded-lg group-hover:bg-blue-200 dark:group-hover:bg-blue-700/50 transition-colors duration-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"تحميل الملف المرفق\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                deletingResource[lesson.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 px-3 py-2 bg-red-50 dark:bg-red-900/20 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"animate-spin h-5 w-5 text-red-500\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    className: \"opacity-25\",\n                                                                    cx: \"12\",\n                                                                    cy: \"12\",\n                                                                    r: \"10\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"4\",\n                                                                    fill: \"none\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    className: \"opacity-75\",\n                                                                    fill: \"currentColor\",\n                                                                    d: \"M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                    lineNumber: 492,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-red-600 dark:text-red-400\",\n                                                            children: \"جاري الحذف...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 25\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteResource(lesson.id),\n                                                    className: \"group flex items-center gap-2 px-3 py-2 text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all duration-200\",\n                                                    disabled: deletingResource[lesson.id],\n                                                    title: \"حذف الملف المرفق\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 group-hover:scale-110 transition-transform duration-200\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"حذف\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    lesson.quizzes && lesson.quizzes.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 text-white\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                lineNumber: 541,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-xl font-bold text-gray-900 dark:text-white\",\n                                                        children: \"الامتحانات والواجبات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 h-px bg-gradient-to-r from-gray-300 to-transparent dark:from-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-h-[600px] overflow-auto custom-scrollbar px-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: lesson.quizzes.map((quiz)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative bg-white dark:bg-gray-800 p-6 rounded-2xl transition-all duration-300 border-2 \".concat(quiz.quiz_type === \"exam\" ? expandedQuizzes[quiz.id] === true ? \"border-blue-400 dark:border-blue-500 shadow-lg shadow-blue-200/50 dark:shadow-blue-900/30 bg-blue-50/30 dark:bg-blue-900/10 ring-1 ring-blue-200 dark:ring-blue-700\" : \"border-blue-300 dark:border-blue-600 hover:border-blue-400 dark:hover:border-blue-500 shadow-sm hover:shadow-md shadow-blue-100 dark:shadow-blue-900/20\" : expandedQuizzes[quiz.id] === true ? \"border-green-400 dark:border-green-500 shadow-lg shadow-green-200/50 dark:shadow-green-900/30 bg-green-50/30 dark:bg-green-900/10 ring-1 ring-green-200 dark:ring-green-700\" : \"border-green-300 dark:border-green-600 hover:border-green-400 dark:hover:border-green-500 shadow-sm hover:shadow-md shadow-green-100 dark:shadow-green-900/20\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute right-0 top-0 bottom-0 w-2 rounded-r-2xl \".concat(quiz.quiz_type === \"exam\" ? \"bg-gradient-to-b from-blue-400 to-blue-600\" : \"bg-gradient-to-b from-green-400 to-green-600\"),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-3 right-0 transform translate-x-1/2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-bold shadow-lg \".concat(quiz.quiz_type === \"exam\" ? quiz.is_published ? \"bg-blue-500 shadow-blue-300\" : \"bg-blue-400 shadow-blue-200 opacity-75\" : quiz.is_published ? \"bg-green-500 shadow-green-300\" : \"bg-green-400 shadow-green-200 opacity-75\"),\n                                                                                children: quiz.quiz_type === \"exam\" ? \"📝\" : \"📋\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                lineNumber: 578,\n                                                                                columnNumber: 33\n                                                                            }, undefined),\n                                                                            quiz.is_published && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute -bottom-1 -right-1 w-3 h-3 bg-emerald-500 rounded-full border-2 border-white dark:border-gray-800 shadow-sm\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-full h-full bg-emerald-400 rounded-full animate-pulse\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 594,\n                                                                                    columnNumber: 37\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                lineNumber: 593,\n                                                                                columnNumber: 35\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                        lineNumber: 577,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                    lineNumber: 570,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-3\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"px-3 py-1 rounded-lg text-sm font-semibold border \".concat(quiz.quiz_type === \"exam\" ? \"bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-700\" : \"bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-700\"),\n                                                                                            children: quiz.quiz_type === \"exam\" ? \"📝 امتحان\" : \"📋 واجب\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 603,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                            className: \"text-lg font-bold text-gray-900 dark:text-white\",\n                                                                                            children: quiz.title\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 614,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 602,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>{\n                                                                                        setExpandedQuizzes((prev)=>({\n                                                                                                ...prev,\n                                                                                                [quiz.id]: !prev[quiz.id]\n                                                                                            }));\n                                                                                    },\n                                                                                    className: \"group flex items-center gap-2 px-4 py-2 rounded-xl transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105 \".concat(expandedQuizzes[quiz.id] === true ? \"bg-gradient-to-r from-blue-500 to-indigo-600 text-white hover:from-blue-600 hover:to-indigo-700\" : \"bg-gray-50 dark:bg-gray-700/50 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-700 dark:hover:text-gray-300\"),\n                                                                                    title: expandedQuizzes[quiz.id] === true ? \"إخفاء التفاصيل\" : \"إظهار التفاصيل\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4 transform transition-all duration-300 group-hover:scale-110 \".concat(expandedQuizzes[quiz.id] === true ? \"rotate-180\" : \"\"),\n                                                                                            fill: \"none\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            viewBox: \"0 0 24 24\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M19 9l-7 7-7-7\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 647,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 637,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-sm font-medium hidden sm:block\",\n                                                                                            children: expandedQuizzes[quiz.id] === true ? \"إخفاء\" : \"إظهار\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 654,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 619,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                            lineNumber: 601,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-600 dark:text-gray-400 mb-3 leading-relaxed\",\n                                                                            children: quiz.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                            lineNumber: 661,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        expandedQuizzes[quiz.id] === true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-4 animate-fade-in-down\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-4 text-sm\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2 px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-lg\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                    className: \"w-4 h-4 text-yellow-500\",\n                                                                                                    fill: \"currentColor\",\n                                                                                                    viewBox: \"0 0 20 20\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                        d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                        lineNumber: 675,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                    lineNumber: 670,\n                                                                                                    columnNumber: 39\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"font-medium text-gray-700 dark:text-gray-300\",\n                                                                                                    children: [\n                                                                                                        quiz.max_score,\n                                                                                                        \" نقطة\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                    lineNumber: 677,\n                                                                                                    columnNumber: 39\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 669,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2 px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-lg\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                    className: \"w-4 h-4 text-blue-500\",\n                                                                                                    fill: \"none\",\n                                                                                                    stroke: \"currentColor\",\n                                                                                                    viewBox: \"0 0 24 24\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                        strokeLinecap: \"round\",\n                                                                                                        strokeLinejoin: \"round\",\n                                                                                                        strokeWidth: 2,\n                                                                                                        d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                        lineNumber: 688,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                    lineNumber: 682,\n                                                                                                    columnNumber: 39\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"font-medium text-gray-700 dark:text-gray-300\",\n                                                                                                    children: [\n                                                                                                        quiz.time_limit,\n                                                                                                        \" دقيقة\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                    lineNumber: 695,\n                                                                                                    columnNumber: 39\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 681,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 668,\n                                                                                    columnNumber: 35\n                                                                                }, undefined),\n                                                                                quiz.questions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-4 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-700\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center gap-2 text-sm\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-gray-600 dark:text-gray-400\",\n                                                                                                children: \"مجموع الدرجات:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 704,\n                                                                                                columnNumber: 41\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"font-bold text-blue-600 dark:text-blue-400\",\n                                                                                                children: quiz.questions.reduce((sum, q)=>sum + Number(q.points || 0), 0)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 707,\n                                                                                                columnNumber: 41\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-gray-500 dark:text-gray-400\",\n                                                                                                children: \"من\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 714,\n                                                                                                columnNumber: 41\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"font-bold text-gray-700 dark:text-gray-300\",\n                                                                                                children: quiz.max_score\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 717,\n                                                                                                columnNumber: 41\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"px-2 py-1 rounded-md text-xs font-medium \".concat((()=>{\n                                                                                                    const total = quiz.questions.reduce((sum, q)=>sum + Number(q.points || 0), 0);\n                                                                                                    const diff = total - quiz.max_score;\n                                                                                                    if (diff === 0) return \"bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300\";\n                                                                                                    if (diff > 0) return \"bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300\";\n                                                                                                    return \"bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-300\";\n                                                                                                })()),\n                                                                                                children: (()=>{\n                                                                                                    const total = quiz.questions.reduce((sum, q)=>sum + Number(q.points || 0), 0);\n                                                                                                    const diff = total - quiz.max_score;\n                                                                                                    if (diff === 0) return \"✅ متساوي\";\n                                                                                                    if (diff > 0) return \"\\uD83D\\uDD3A زيادة \".concat(diff);\n                                                                                                    return \"\\uD83D\\uDD3B ناقص \".concat(Math.abs(diff));\n                                                                                                })()\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 720,\n                                                                                                columnNumber: 41\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                        lineNumber: 703,\n                                                                                        columnNumber: 39\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 702,\n                                                                                    columnNumber: 37\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-4\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        className: \"flex items-center gap-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md\",\n                                                                                        onClick: ()=>{\n                                                                                            setShowQuestionForm((prev)=>{\n                                                                                                // Toggle if open, else open and set default type\n                                                                                                const isOpen = !!prev[quiz.id];\n                                                                                                if (!isOpen) {\n                                                                                                    setQuestionForm((qprev)=>({\n                                                                                                            ...qprev,\n                                                                                                            [quiz.id]: {\n                                                                                                                ...qprev[quiz.id],\n                                                                                                                question_type: \"mcq\"\n                                                                                                            }\n                                                                                                        }));\n                                                                                                    setAnswersForm((aprev)=>({\n                                                                                                            ...aprev,\n                                                                                                            [quiz.id]: [\n                                                                                                                {\n                                                                                                                    text: \"\",\n                                                                                                                    is_correct: false\n                                                                                                                }\n                                                                                                            ]\n                                                                                                        }));\n                                                                                                }\n                                                                                                return {\n                                                                                                    ...prev,\n                                                                                                    [quiz.id]: !isOpen\n                                                                                                };\n                                                                                            });\n                                                                                        },\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                className: \"w-4 h-4\",\n                                                                                                fill: \"none\",\n                                                                                                stroke: \"currentColor\",\n                                                                                                viewBox: \"0 0 24 24\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                    strokeLinecap: \"round\",\n                                                                                                    strokeLinejoin: \"round\",\n                                                                                                    strokeWidth: 2,\n                                                                                                    d: \"M12 4v16m8-8H4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                    lineNumber: 787,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 781,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            showQuestionForm[quiz.id] ? \"إغلاق النموذج\" : \"إضافة سؤال\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                        lineNumber: 753,\n                                                                                        columnNumber: 37\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 752,\n                                                                                    columnNumber: 35\n                                                                                }, undefined),\n                                                                                quiz.questions && quiz.questions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-6 border-t border-gray-200 dark:border-gray-700 pt-6\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2 mb-4\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                    className: \"w-5 h-5 text-purple-500\",\n                                                                                                    fill: \"none\",\n                                                                                                    stroke: \"currentColor\",\n                                                                                                    viewBox: \"0 0 24 24\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                        strokeLinecap: \"round\",\n                                                                                                        strokeLinejoin: \"round\",\n                                                                                                        strokeWidth: 2,\n                                                                                                        d: \"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                        lineNumber: 810,\n                                                                                                        columnNumber: 43\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                    lineNumber: 804,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                                                                                    className: \"font-bold text-gray-900 dark:text-white text-lg\",\n                                                                                                    children: [\n                                                                                                        \"الأسئلة (\",\n                                                                                                        quiz.questions.length,\n                                                                                                        \")\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                    lineNumber: 817,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 803,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"space-y-4\",\n                                                                                            children: quiz.questions.map((question, idx)=>{\n                                                                                                const isEditOpen = questionEditForm && questionEditForm.quizId === quiz.id && questionEditForm.question && questionEditForm.question.id === question.id;\n                                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_2___default().Fragment), {\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700/50 dark:to-gray-800/50 rounded-xl p-4 border border-gray-200 dark:border-gray-600 hover:shadow-md transition-all duration-300\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"flex-1\",\n                                                                                                                children: [\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                        className: \"flex items-start gap-3 mb-3\",\n                                                                                                                        children: [\n                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0\",\n                                                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                                    className: \"text-white font-bold text-sm\",\n                                                                                                                                    children: question.order\n                                                                                                                                }, void 0, false, {\n                                                                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                    lineNumber: 836,\n                                                                                                                                    columnNumber: 55\n                                                                                                                                }, undefined)\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                lineNumber: 835,\n                                                                                                                                columnNumber: 53\n                                                                                                                            }, undefined),\n                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                className: \"flex-1\",\n                                                                                                                                children: [\n                                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                                                        className: \"text-gray-900 dark:text-white font-medium leading-relaxed\",\n                                                                                                                                        children: question.text\n                                                                                                                                    }, void 0, false, {\n                                                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                        lineNumber: 841,\n                                                                                                                                        columnNumber: 55\n                                                                                                                                    }, undefined),\n                                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                        className: \"flex items-center gap-3 mt-2\",\n                                                                                                                                        children: [\n                                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                                className: \"px-2 py-1 rounded-md text-xs font-medium \".concat(question.question_type === \"mcq\" || question.question_type === \"multiple_choice\" ? \"bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300\" : question.question_type === \"true_false\" ? \"bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300\" : \"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300\"),\n                                                                                                                                                children: question.question_type === \"mcq\" || question.question_type === \"multiple_choice\" ? \"اختيار من متعدد\" : question.question_type === \"true_false\" ? \"صح أو خطأ\" : \"غير محدد\"\n                                                                                                                                            }, void 0, false, {\n                                                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                                lineNumber: 845,\n                                                                                                                                                columnNumber: 57\n                                                                                                                                            }, undefined),\n                                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                                className: \"px-2 py-1 bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 rounded-md text-xs font-medium\",\n                                                                                                                                                children: [\n                                                                                                                                                    question.points,\n                                                                                                                                                    \" نقطة\"\n                                                                                                                                                ]\n                                                                                                                                            }, void 0, true, {\n                                                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                                lineNumber: 868,\n                                                                                                                                                columnNumber: 57\n                                                                                                                                            }, undefined),\n                                                                                                                                            question.image_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                                className: \"px-2 py-1 bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 rounded-md text-xs font-medium\",\n                                                                                                                                                children: \"\\uD83D\\uDCF7 صورة مرفقة\"\n                                                                                                                                            }, void 0, false, {\n                                                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                                lineNumber: 872,\n                                                                                                                                                columnNumber: 59\n                                                                                                                                            }, undefined)\n                                                                                                                                        ]\n                                                                                                                                    }, void 0, true, {\n                                                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                        lineNumber: 844,\n                                                                                                                                        columnNumber: 55\n                                                                                                                                    }, undefined)\n                                                                                                                                ]\n                                                                                                                            }, void 0, true, {\n                                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                lineNumber: 840,\n                                                                                                                                columnNumber: 53\n                                                                                                                            }, undefined)\n                                                                                                                        ]\n                                                                                                                    }, void 0, true, {\n                                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                        lineNumber: 834,\n                                                                                                                        columnNumber: 51\n                                                                                                                    }, undefined),\n                                                                                                                    question.image_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                        className: \"mt-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg\",\n                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                                                            src: question.image_url,\n                                                                                                                            alt: \"صورة السؤال\",\n                                                                                                                            className: \"max-w-full h-32 object-contain border border-gray-200 dark:border-gray-600 rounded-lg mx-auto\"\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                            lineNumber: 882,\n                                                                                                                            columnNumber: 55\n                                                                                                                        }, undefined)\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                        lineNumber: 881,\n                                                                                                                        columnNumber: 53\n                                                                                                                    }, undefined),\n                                                                                                                    (question.question_type === \"mcq\" || question.question_type === \"multiple_choice\" || question.question_type === \"true_false\") && question.answers && question.answers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                        className: \"mt-4 p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600\",\n                                                                                                                        children: [\n                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                                                                                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2\",\n                                                                                                                                children: \"الإجابات:\"\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                lineNumber: 900,\n                                                                                                                                columnNumber: 57\n                                                                                                                            }, undefined),\n                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                className: \"space-y-2\",\n                                                                                                                                children: question.answers.map((ans, ansIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                        className: \"flex items-center gap-2 p-2 rounded-lg \".concat(ans.is_correct ? \"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700\" : \"bg-gray-50 dark:bg-gray-700/50\"),\n                                                                                                                                        children: [\n                                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                                className: \"w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold \".concat(ans.is_correct ? \"bg-green-500 text-white\" : \"bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300\"),\n                                                                                                                                                children: String.fromCharCode(65 + ansIndex)\n                                                                                                                                            }, void 0, false, {\n                                                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                                lineNumber: 914,\n                                                                                                                                                columnNumber: 65\n                                                                                                                                            }, undefined),\n                                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                                                className: \"flex-1 \".concat(ans.is_correct ? \"text-green-700 dark:text-green-300 font-semibold\" : \"text-gray-700 dark:text-gray-300\"),\n                                                                                                                                                children: ans.text\n                                                                                                                                            }, void 0, false, {\n                                                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                                lineNumber: 926,\n                                                                                                                                                columnNumber: 65\n                                                                                                                                            }, undefined),\n                                                                                                                                            ans.is_correct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                                className: \"flex items-center gap-1 px-2 py-1 bg-green-100 dark:bg-green-800 text-green-700 dark:text-green-300 rounded-md text-xs font-medium\",\n                                                                                                                                                children: [\n                                                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                                                                        className: \"w-3 h-3\",\n                                                                                                                                                        fill: \"currentColor\",\n                                                                                                                                                        viewBox: \"0 0 20 20\",\n                                                                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                                                                            fillRule: \"evenodd\",\n                                                                                                                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                                                                                                            clipRule: \"evenodd\"\n                                                                                                                                                        }, void 0, false, {\n                                                                                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                                            lineNumber: 942,\n                                                                                                                                                            columnNumber: 71\n                                                                                                                                                        }, undefined)\n                                                                                                                                                    }, void 0, false, {\n                                                                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                                        lineNumber: 937,\n                                                                                                                                                        columnNumber: 69\n                                                                                                                                                    }, undefined),\n                                                                                                                                                    \"صحيح\"\n                                                                                                                                                ]\n                                                                                                                                            }, void 0, true, {\n                                                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                                lineNumber: 936,\n                                                                                                                                                columnNumber: 67\n                                                                                                                                            }, undefined)\n                                                                                                                                        ]\n                                                                                                                                    }, ans.id, true, {\n                                                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                        lineNumber: 906,\n                                                                                                                                        columnNumber: 63\n                                                                                                                                    }, undefined))\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                lineNumber: 903,\n                                                                                                                                columnNumber: 57\n                                                                                                                            }, undefined)\n                                                                                                                        ]\n                                                                                                                    }, void 0, true, {\n                                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                        lineNumber: 899,\n                                                                                                                        columnNumber: 55\n                                                                                                                    }, undefined)\n                                                                                                                ]\n                                                                                                            }, void 0, true, {\n                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                lineNumber: 833,\n                                                                                                                columnNumber: 49\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"flex gap-3 mt-4\",\n                                                                                                                children: [\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                        className: \"flex items-center gap-2 bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md\",\n                                                                                                                        onClick: ()=>{\n                                                                                                                            setQuestionEditForm({\n                                                                                                                                quizId: quiz.id,\n                                                                                                                                question: {\n                                                                                                                                    ...question\n                                                                                                                                },\n                                                                                                                                answers: question.answers ? question.answers.map((a)=>({\n                                                                                                                                        ...a\n                                                                                                                                    })) : []\n                                                                                                                            });\n                                                                                                                            // عند فتح التعديل، إذا كان هناك صورة، ضعها في imagePreview\n                                                                                                                            if (question.image_url) {\n                                                                                                                                setImagePreview((prev)=>({\n                                                                                                                                        ...prev,\n                                                                                                                                        [quiz.id]: question.image_url\n                                                                                                                                    }));\n                                                                                                                            } else {\n                                                                                                                                setImagePreview((prev)=>({\n                                                                                                                                        ...prev,\n                                                                                                                                        [quiz.id]: null\n                                                                                                                                    }));\n                                                                                                                            }\n                                                                                                                            // امسح الصورة المؤقتة (cropped) عند كل فتح\n                                                                                                                            setCroppedImages((prev)=>({\n                                                                                                                                    ...prev,\n                                                                                                                                    [quiz.id]: null\n                                                                                                                                }));\n                                                                                                                            setQuestionImages((prev)=>({\n                                                                                                                                    ...prev,\n                                                                                                                                    [quiz.id]: null\n                                                                                                                                }));\n                                                                                                                        },\n                                                                                                                        children: [\n                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                                                className: \"w-4 h-4\",\n                                                                                                                                fill: \"none\",\n                                                                                                                                stroke: \"currentColor\",\n                                                                                                                                viewBox: \"0 0 24 24\",\n                                                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                                                    strokeLinecap: \"round\",\n                                                                                                                                    strokeLinejoin: \"round\",\n                                                                                                                                    strokeWidth: 2,\n                                                                                                                                    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                                                                                                }, void 0, false, {\n                                                                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                    lineNumber: 1015,\n                                                                                                                                    columnNumber: 55\n                                                                                                                                }, undefined)\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                lineNumber: 1009,\n                                                                                                                                columnNumber: 53\n                                                                                                                            }, undefined),\n                                                                                                                            \"تعديل\"\n                                                                                                                        ]\n                                                                                                                    }, void 0, true, {\n                                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                        lineNumber: 959,\n                                                                                                                        columnNumber: 51\n                                                                                                                    }, undefined),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                        className: \"flex items-center gap-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md\",\n                                                                                                                        onClick: ()=>{\n                                                                                                                            if (typeof handleDeleteQuestion === \"function\") {\n                                                                                                                                handleDeleteQuestion(quiz.id, question.id);\n                                                                                                                            }\n                                                                                                                        },\n                                                                                                                        children: [\n                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                                                className: \"w-4 h-4\",\n                                                                                                                                fill: \"none\",\n                                                                                                                                stroke: \"currentColor\",\n                                                                                                                                viewBox: \"0 0 24 24\",\n                                                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                                                    strokeLinecap: \"round\",\n                                                                                                                                    strokeLinejoin: \"round\",\n                                                                                                                                    strokeWidth: 2,\n                                                                                                                                    d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                                                                                }, void 0, false, {\n                                                                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                    lineNumber: 1044,\n                                                                                                                                    columnNumber: 55\n                                                                                                                                }, undefined)\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                                lineNumber: 1038,\n                                                                                                                                columnNumber: 53\n                                                                                                                            }, undefined),\n                                                                                                                            \"حذف\"\n                                                                                                                        ]\n                                                                                                                    }, void 0, true, {\n                                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                        lineNumber: 1024,\n                                                                                                                        columnNumber: 51\n                                                                                                                    }, undefined)\n                                                                                                                ]\n                                                                                                            }, void 0, true, {\n                                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                                lineNumber: 958,\n                                                                                                                columnNumber: 49\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                        lineNumber: 832,\n                                                                                                        columnNumber: 47\n                                                                                                    }, undefined)\n                                                                                                }, question.id, false, {\n                                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                    lineNumber: 831,\n                                                                                                    columnNumber: 45\n                                                                                                }, undefined);\n                                                                                            })\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 821,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 802,\n                                                                                    columnNumber: 37\n                                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-6 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 text-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-8 h-8 text-gray-400 mx-auto mb-2\",\n                                                                                            fill: \"none\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            viewBox: \"0 0 24 24\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 1068,\n                                                                                                columnNumber: 41\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 1062,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                                                                            children: \"لا يوجد أسئلة بعد. ابدأ بإضافة أول سؤال للامتحان\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 1075,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 1061,\n                                                                                    columnNumber: 37\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                            lineNumber: 667,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                    lineNumber: 600,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex gap-3 mt-6 flex-wrap justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-3\",\n                                                                            children: [\n                                                                                quiz.is_published ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"flex items-center gap-2 bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md\",\n                                                                                    onClick: async ()=>{\n                                                                                        const token = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"authToken\");\n                                                                                        await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].post(\"\".concat(\"http://127.0.0.1:8000\", \"/api/quizzes/\").concat(quiz.id, \"/unpublish/\"), {}, {\n                                                                                            headers: {\n                                                                                                Authorization: \"Bearer \".concat(token)\n                                                                                            }\n                                                                                        });\n                                                                                        // تحديث الدروس بعد إلغاء النشر\n                                                                                        const { fetchInstructorLessons } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/services/instructor */ \"(app-pages-browser)/./src/services/instructor.js\"));\n                                                                                        const lessonsList = await fetchInstructorLessons(courseId, token);\n                                                                                        setLessons(lessonsList);\n                                                                                    },\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4\",\n                                                                                            fill: \"none\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            viewBox: \"0 0 24 24\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 1118,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 1112,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined),\n                                                                                        \"إلغاء النشر\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 1088,\n                                                                                    columnNumber: 35\n                                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"flex items-center gap-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md\",\n                                                                                    onClick: async ()=>{\n                                                                                        var _quiz_questions;\n                                                                                        // النظام الجديد: تحديث max_score تلقائياً - zaki alkholy\n                                                                                        const totalPoints = (_quiz_questions = quiz.questions) === null || _quiz_questions === void 0 ? void 0 : _quiz_questions.reduce((sum, q)=>sum + Number(q.points || 0), 0);\n                                                                                        // تحديث الدرجة النهائية إذا لزم الأمر\n                                                                                        if (totalPoints !== quiz.max_score) {\n                                                                                            const token = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"authToken\");\n                                                                                            await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].patch(\"\".concat(\"http://127.0.0.1:8000\", \"/api/quizzes/\").concat(quiz.id, \"/\"), {\n                                                                                                max_score: totalPoints\n                                                                                            }, {\n                                                                                                headers: {\n                                                                                                    Authorization: \"Bearer \".concat(token)\n                                                                                                }\n                                                                                            });\n                                                                                            console.log(\"✅ تم تحديث الدرجة النهائية من \".concat(quiz.max_score, \" إلى \").concat(totalPoints));\n                                                                                        }\n                                                                                        // للواجبات: التحقق من وجود أسئلة فقط\n                                                                                        if (quiz.quiz_type === \"assignment\") {\n                                                                                            if (!quiz.questions || quiz.questions.length === 0) {\n                                                                                                alert(\"⚠️ يجب إضافة أسئلة للواجب قبل النشر.\");\n                                                                                                return;\n                                                                                            }\n                                                                                        }\n                                                                                        const token = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"authToken\");\n                                                                                        try {\n                                                                                            // محاولة نشر الامتحان - zakialkholy\n                                                                                            await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].post(\"\".concat(\"http://127.0.0.1:8000\", \"/api/quizzes/\").concat(quiz.id, \"/publish/\"), {}, {\n                                                                                                headers: {\n                                                                                                    Authorization: \"Bearer \".concat(token)\n                                                                                                }\n                                                                                            });\n                                                                                            // إذا نجح النشر، تحديث قائمة الدروس - zakialkholy\n                                                                                            const { fetchInstructorLessons } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/services/instructor */ \"(app-pages-browser)/./src/services/instructor.js\"));\n                                                                                            const lessonsList = await fetchInstructorLessons(courseId, token);\n                                                                                            setLessons(lessonsList);\n                                                                                            // إظهار رسالة نجاح - zakialkholy\n                                                                                            const { toast } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\"));\n                                                                                            toast.success(\"تم نشر الامتحان/الواجب بنجاح!\");\n                                                                                        } catch (error) {\n                                                                                            var _error_response, _error_response_data_error, _error_response_data, _error_response1;\n                                                                                            // التعامل مع خطأ عدم تطابق الدرجات - zakialkholy\n                                                                                            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 400 && ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : (_error_response_data_error = _error_response_data.error) === null || _error_response_data_error === void 0 ? void 0 : _error_response_data_error.includes(\"تحذير\"))) {\n                                                                                                const errorData = error.response.data;\n                                                                                                const { toast } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\"));\n                                                                                                // إظهار رسالة تحذير مفصلة - zakialkholy\n                                                                                                toast.error(\"\".concat(errorData.error, \"\\n\").concat(errorData.message, \"\\n\").concat(errorData.suggestion), {\n                                                                                                    duration: 8000,\n                                                                                                    style: {\n                                                                                                        maxWidth: '500px',\n                                                                                                        fontSize: '14px',\n                                                                                                        lineHeight: '1.5'\n                                                                                                    }\n                                                                                                });\n                                                                                                // يمكن إضافة modal للتفاصيل أكثر لو احتجت - zakialkholy\n                                                                                                console.error(\"تفاصيل خطأ الدرجات:\", errorData.details);\n                                                                                            } else {\n                                                                                                var _error_response_data1, _error_response2;\n                                                                                                // خطأ عام آخر - zakialkholy\n                                                                                                const { toast } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\"));\n                                                                                                toast.error(((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data1 = _error_response2.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.error) || \"حدث خطأ أثناء نشر الامتحان\");\n                                                                                            }\n                                                                                        }\n                                                                                    },\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4\",\n                                                                                            fill: \"none\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            viewBox: \"0 0 24 24\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 1234,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 1228,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined),\n                                                                                        \"نشر\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 1128,\n                                                                                    columnNumber: 35\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>generateQuizPDF(quiz, user),\n                                                                                    className: \"flex items-center gap-2 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4\",\n                                                                                            fill: \"none\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            viewBox: \"0 0 24 24\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 1256,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 1250,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        \"تحميل PDF\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 1246,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                            lineNumber: 1085,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"flex items-center gap-2 bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md\",\n                                                                                    onClick: ()=>setQuizEditForm({\n                                                                                            lessonId: lesson.id,\n                                                                                            quiz: {\n                                                                                                id: quiz.id,\n                                                                                                title: quiz.title || \"\",\n                                                                                                description: quiz.description || \"\",\n                                                                                                passing_score: quiz.passing_score || 0,\n                                                                                                time_limit: quiz.time_limit || 0,\n                                                                                                quiz_type: quiz.quiz_type || \"\"\n                                                                                            }\n                                                                                        }),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4\",\n                                                                                            fill: \"none\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            viewBox: \"0 0 24 24\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 1291,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 1285,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        \"تعديل\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 1269,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"flex items-center gap-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md\",\n                                                                                    onClick: ()=>handleDeleteQuiz(lesson.id, quiz.id),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4\",\n                                                                                            fill: \"none\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            viewBox: \"0 0 24 24\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                                lineNumber: 1312,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                            lineNumber: 1306,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        \"حذف\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                                    lineNumber: 1300,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                            lineNumber: 1267,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                                    lineNumber: 1084,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, quiz.id, true, {\n                                                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 27\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 p-6 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700/50 dark:to-gray-800/50 rounded-xl border-2 border-dashed border-gray-300 dark:border-gray-600 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-gray-400 dark:text-gray-500\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                        lineNumber: 1337,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                    lineNumber: 1331,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                lineNumber: 1330,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                className: \"text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2\",\n                                                children: \"لا يوجد امتحانات أو واجبات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                lineNumber: 1345,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                                children: \"ابدأ بإضافة امتحان أو واجب لهذا الدرس لتقييم فهم الطلاب\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                                lineNumber: 1348,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                        lineNumber: 1329,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                lineNumber: 365,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, lesson.id, true, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, undefined),\n            lessons.map((lesson)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_2___default().Fragment), {\n                    children: [\n                        QuizFormModal && !!showQuizModal[lesson.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuizFormModal, {\n                            isOpen: !!showQuizModal[lesson.id],\n                            onClose: ()=>setShowQuizModal({}),\n                            onSubmit: ()=>handleAddQuiz(lesson.id, showQuizModal[lesson.id]),\n                            formState: quizForm[lesson.id] || {},\n                            setFormState: (updater)=>setQuizForm((prev)=>({\n                                        ...prev,\n                                        [lesson.id]: typeof updater === \"function\" ? updater(prev[lesson.id] || {}) : updater\n                                    })),\n                            loading: false,\n                            isExam: showQuizModal[lesson.id] === \"exam\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                            lineNumber: 1365,\n                            columnNumber: 13\n                        }, undefined),\n                        FileUploadModal && !!showFileForm[lesson.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileUploadModal, {\n                            isOpen: !!showFileForm[lesson.id],\n                            onClose: ()=>setShowFileForm((prev)=>({\n                                        ...prev,\n                                        [lesson.id]: false\n                                    })),\n                            onSubmit: (e)=>handleAddFile(lesson.id),\n                            fileInputRef: (el)=>fileInputRefs.current[lesson.id] = el,\n                            uploading: isUploading[lesson.id],\n                            uploadProgress: uploadProgress[lesson.id]\n                        }, void 0, false, {\n                            fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                            lineNumber: 1388,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, \"modals-\".concat(lesson.id), true, {\n                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                    lineNumber: 1362,\n                    columnNumber: 9\n                }, undefined)),\n            lessons.map((lesson)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_2___default().Fragment), {\n                    children: (quizEditForm === null || quizEditForm === void 0 ? void 0 : quizEditForm.lessonId) === lesson.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_Components_course_QuizEditModal__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        isOpen: !!(quizEditForm && quizEditForm.lessonId === lesson.id),\n                        onClose: ()=>setQuizEditForm(null),\n                        onSubmit: ()=>handleEditQuiz(lesson.id, quizEditForm.quiz.id),\n                        formState: quizEditForm.quiz,\n                        setFormState: (updater)=>setQuizEditForm((prev)=>({\n                                    ...prev,\n                                    quiz: typeof updater === \"function\" ? updater(prev.quiz || {}) : updater\n                                })),\n                        loading: false\n                    }, void 0, false, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                        lineNumber: 1409,\n                        columnNumber: 13\n                    }, undefined)\n                }, \"quiz-edit-modal-\".concat(lesson.id), false, {\n                    fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                    lineNumber: 1407,\n                    columnNumber: 9\n                }, undefined)),\n            lessons.map((lesson)=>{\n                var _lesson_quizzes;\n                return (_lesson_quizzes = lesson.quizzes) === null || _lesson_quizzes === void 0 ? void 0 : _lesson_quizzes.map((quiz)=>{\n                    var _quiz_questions, _quiz_questions1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_2___default().Fragment), {\n                        children: [\n                            QuestionFormModal && showQuestionForm[quiz.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuestionFormModal, {\n                                isOpen: !!showQuestionForm[quiz.id],\n                                onClose: ()=>setShowQuestionForm((prev)=>({\n                                            ...prev,\n                                            [quiz.id]: false\n                                        })),\n                                onSubmit: (quizId, data)=>handleAddQuestion(quizId, data),\n                                quizId: quiz.id,\n                                formState: questionForm[quiz.id] || {},\n                                setFormState: (updater)=>setQuestionForm((prev)=>({\n                                            ...prev,\n                                            [quiz.id]: typeof updater === \"function\" ? updater(prev[quiz.id] || {}) : updater\n                                        })),\n                                answers: answersForm[quiz.id] || [],\n                                setAnswers: (updater)=>setAnswersForm((prev)=>({\n                                            ...prev,\n                                            [quiz.id]: typeof updater === \"function\" ? updater(prev[quiz.id] || []) : updater\n                                        })),\n                                loading: !!questionLoading[quiz.id],\n                                croppedImage: croppedImages[quiz.id],\n                                setCroppedImage: (img)=>setCroppedImages((prev)=>({\n                                            ...prev,\n                                            [quiz.id]: img\n                                        })),\n                                questionImage: questionImages[quiz.id],\n                                setQuestionImage: (img)=>setQuestionImages((prev)=>({\n                                            ...prev,\n                                            [quiz.id]: img\n                                        })),\n                                imagePreview: imagePreview[quiz.id],\n                                setImagePreview: (img)=>setImagePreview((prev)=>({\n                                            ...prev,\n                                            [quiz.id]: img\n                                        })),\n                                showImageCropper: !!showImageCropper[quiz.id],\n                                setShowImageCropper: (val)=>setShowImageCropper((prev)=>({\n                                            ...prev,\n                                            [quiz.id]: val\n                                        })),\n                                removeQuestionImage: ()=>removeQuestionImage(quiz.id),\n                                getCroppedImg: getCroppedImg,\n                                user: user,\n                                quiz: quiz,\n                                existingOrders: (_quiz_questions = quiz.questions) === null || _quiz_questions === void 0 ? void 0 : _quiz_questions.map((q)=>q.order)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                lineNumber: 1435,\n                                columnNumber: 15\n                            }, undefined),\n                            QuestionFormModal && questionEditForm && questionEditForm.quizId === quiz.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuestionFormModal, {\n                                isOpen: !!(questionEditForm && questionEditForm.quizId === quiz.id),\n                                onClose: ()=>{\n                                    setQuestionEditForm(null);\n                                    setAnswersForm({}); // امسح الإجابات المؤقتة عند غلق المودال\n                                },\n                                onSubmit: (quizId, data)=>handleEditQuestion(quizId, questionEditForm.question.id, data),\n                                quizId: quiz.id,\n                                formState: (questionEditForm === null || questionEditForm === void 0 ? void 0 : questionEditForm.question) || {},\n                                setFormState: (updater)=>setQuestionEditForm((prev)=>({\n                                            ...prev,\n                                            question: typeof updater === \"function\" ? updater((prev === null || prev === void 0 ? void 0 : prev.question) || {}) : updater\n                                        })),\n                                answers: (questionEditForm === null || questionEditForm === void 0 ? void 0 : questionEditForm.answers) || [],\n                                setAnswers: (data)=>setQuestionEditForm((prev)=>({\n                                            ...prev,\n                                            answers: data\n                                        })),\n                                loading: questionLoading[quiz.id],\n                                quiz: quiz,\n                                existingOrders: (_quiz_questions1 = quiz.questions) === null || _quiz_questions1 === void 0 ? void 0 : _quiz_questions1.map((q)=>q.order)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                                lineNumber: 1506,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, \"question-modal-\".concat(quiz.id), true, {\n                        fileName: \"D:\\\\route\\\\منصة\\\\رفع\\\\4\\\\manasa\\\\src\\\\components\\\\instructor\\\\LessonList.jsx\",\n                        lineNumber: 1432,\n                        columnNumber: 11\n                    }, undefined);\n                });\n            })\n        ]\n    }, void 0, true);\n};\n_s(LessonList, \"Ibf13+xSpTb5/pWs4GbBvvtlrh8=\", false, function() {\n    return [\n        _contexts_CourseContext__WEBPACK_IMPORTED_MODULE_4__.useCourseContext\n    ];\n});\n_c = LessonList;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LessonList);\nvar _c;\n$RefreshReg$(_c, \"LessonList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/instructor/LessonList.jsx\n"));

/***/ })

});